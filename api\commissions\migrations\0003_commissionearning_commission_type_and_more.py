# Generated by Django 4.2.19 on 2025-05-24 05:39

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('commissions', '0002_commissionearning_is_eligible_therapistmonthstats_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='commissionearning',
            name='commission_type',
            field=models.CharField(choices=[('standard', 'Standard Percentage'), ('expert_session', 'Expert Session Flat Rate'), ('expert_package', 'Expert Package Flat Rate'), ('home_service', 'Home Service'), ('freelancer', 'Freelancer Rate')], default='standard', max_length=20),
        ),
        migrations.AddField(
            model_name='commissionearning',
            name='is_primary',
            field=models.BooleanField(default=True, help_text='Whether this is the primary commission for the sale'),
        ),
    ]
