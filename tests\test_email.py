import pytest
from django.conf import settings

from utils.email import send_email
from unittest.mock import patch


@pytest.mark.django_db
class TestSendEmail:

    @patch("utils.email.send_mail")
    def test_send_email(self, mock_send_mail):
        # Define email details
        subject = "Test Subject"
        message = "Test Message"
        recipient_list = ["<EMAIL>"]

        # Call the send_email function without a template
        send_email(subject, message, recipient_list)

        # Check that send_mail was called once
        mock_send_mail.assert_called_once()

        # Check that the call was made with the correct parameters
        args, kwargs = mock_send_mail.call_args
        assert args[0] == subject
        assert args[1] == message
        assert args[2] == settings.DEFAULT_FROM_EMAIL
        assert args[3] == recipient_list
        assert kwargs.get("html_message") is None

    @patch("utils.email.send_mail")
    def test_send_email_with_template(self, mock_send_mail):
        # Define email details
        subject = "Test Subject"
        message = "Test Message"
        recipient_list = ["<EMAIL>"]
        html_template = "email/welcome_email.html"
        context = {"user": {"first_name": "Test"}}

        # Call the send_email function with a template
        send_email(subject, message, recipient_list, html_template, context)

        # Check that send_mail was called once
        mock_send_mail.assert_called_once()

        # Check that the call was made with the correct parameters
        args, kwargs = mock_send_mail.call_args
        assert args[0] == subject
        assert args[1] == message
        assert args[2] == settings.DEFAULT_FROM_EMAIL
        assert args[3] == recipient_list
        assert kwargs["html_message"] is not None

    @patch("utils.email.send_mail")
    def test_send_email_failure(self, mock_send_mail):
        # Simulate an exception being raised
        mock_send_mail.side_effect = Exception("SMTP error")

        subject = "Test Subject"
        message = "Test Message"
        recipient_list = ["<EMAIL>"]

        # Call the send_email function and expect an exception
        with pytest.raises(Exception) as excinfo:
            send_email(subject, message, recipient_list)

        assert "SMTP error" in str(excinfo.value)

        # Check that send_mail was called once
        mock_send_mail.assert_called_once()
