from django.db import transaction
from decimal import Decimal
from rest_framework import generics, status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework import viewsets, status
from django.contrib.auth import get_user_model
from django.shortcuts import get_object_or_404
from django.http import HttpResponse
from api.appointments.models import (
    PackageOption,
    Discount,
    UserPackage,
    SharedPackage,  #
    SharedPackageUser,
    UnlimitedPackage,
    Sale,
)
from rest_framework.decorators import action
from api.appointments.serializers import (
    DirectPackageSaleSerializer,
    SaleSerializer,
    SaleUpdateSerializer,
)
from api.core.permissions import IsOwnerOrReceptionist
from utils.logging import api_logger, log_request_data, log_response_data, log_error

from django.utils import timezone
import csv

User = get_user_model()


class DirectPackageSaleView(APIView):
    """
    POST endpoint to create a package sale without an appointment.
    Creates the appropriate package and a corresponding Sale object.
    """

    permission_classes = [IsAuthenticated, IsOwnerOrReceptionist]

    def post(self, request):
        log_request_data(request, "📥 Direct Package Sale Request")
        api_logger.info("🔄 Processing direct package sale")
        if request.data["discount_percentage"] is None:
            request.data["discount_percentage"] = Decimal("0")
        api_logger.info(request.data["discount_percentage"])

        serializer = DirectPackageSaleSerializer(data=request.data)
        if not serializer.is_valid():
            api_logger.warning(f"❌ Invalid package sale data: {serializer.errors}")
            response = Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
            log_response_data(response, "📤 Direct Package Sale Error Response")
            return response

        try:
            with transaction.atomic():
                user_id = serializer.validated_data["user_id"]
                package_option_id = serializer.validated_data["package_option_id"]
                package_type = serializer.validated_data["package_type"]
                discount_id = serializer.validated_data.get("discount_id")
                discount_percentage = serializer.validated_data.get(
                    "discount_percentage", Decimal("0")
                )
                if discount_percentage is None:
                    discount_percentage = Decimal("0")
                total_price = serializer.validated_data["total_price"]
                location = serializer.validated_data["location"]
                payment_method = serializer.validated_data["payment_method"]

                # Get required objects
                user = get_object_or_404(User, pk=user_id)
                package_option = get_object_or_404(PackageOption, pk=package_option_id)
                discount = None
                if discount_id:
                    discount = get_object_or_404(Discount, pk=discount_id)

                api_logger.info(
                    f"🔍 Creating {package_type} for User:{user_id} with PackageOption:{package_option_id}"
                )

                # Create the appropriate package based on type
                if package_type == "user_package":
                    package = UserPackage.objects.create(
                        user=user,
                        package_option=package_option,
                        total_time=package_option.time,
                        remaining_time=package_option.time,
                    )
                    sale_kwargs = {"user_package": package}
                    api_logger.info(f"✅ Created UserPackage:{package.id}")

                elif package_type == "shared_package":
                    package = SharedPackage.objects.create(
                        package_option=package_option,
                        total_time=package_option.time,
                        remaining_time=package_option.time,
                    )
                    SharedPackageUser.objects.create(shared_package=package, user=user)
                    sale_kwargs = {"shared_package": package}
                    api_logger.info(f"✅ Created SharedPackage:{package.id}")

                else:  # unlimited_package
                    package = UnlimitedPackage.objects.create(
                        user=user,
                        package_option=package_option,
                    )
                    sale_kwargs = {"unlimited_package": package}
                    api_logger.info(f"✅ Created UnlimitedPackage:{package.id}")
                api_logger.info(f"✅ discount_percentage:{discount_percentage}")
                # Create the Sale record
                sale = Sale.objects.create(
                    user=user,
                    sale_type="package",
                    package_option=package_option,
                    total_price=total_price,
                    discount=discount,
                    location=location,
                    discount_percentage=discount_percentage,
                    payment_method=payment_method,
                    **sale_kwargs,
                )
                api_logger.info(
                    f"✅ Created Sale:{sale.id} with total price: ${total_price}"
                )

                response_data = {
                    "id": sale.id,
                    "status": "success",
                    "package_id": package.id,
                    "package_type": package_type,
                    "total_price": total_price,
                }
                response = Response(response_data, status=status.HTTP_201_CREATED)
                log_response_data(response, "📤 Direct Package Sale Success Response")
                return response

        except Exception as e:
            log_error(e, "Error creating direct package sale")
            response = Response(
                {"detail": "An error occurred while creating the package sale."},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
            log_response_data(response, "📤 Direct Package Sale Error Response")
            return response


# pagination.py (or within your views file)
from rest_framework.pagination import PageNumberPagination


class StandardResultsSetPagination(PageNumberPagination):
    page_size = 10  # Default page size
    page_size_query_param = "page_size"  # Allow client to override by adding ?page_size=
    max_page_size = 100  # Maximum limit for page size


class SaleListView(generics.ListAPIView):
    """
    GET endpoint to list all sales with optional filtering, pagination,
    and CSV export functionality.
    """

    permission_classes = [IsAuthenticated, IsOwnerOrReceptionist]
    serializer_class = SaleSerializer
    pagination_class = StandardResultsSetPagination

    def get_queryset(self):
        log_request_data(self.request, "📥 Sales List Request")
        api_logger.info("🔍 Retrieving sales list")

        queryset = Sale.objects.all().order_by("-created_at")

        # Filter by user if provided
        user_id = self.request.query_params.get("user_id")
        if user_id:
            queryset = queryset.filter(user_id=user_id)
            api_logger.info(f"🔍 Filtering sales by User:{user_id}")

        # Filter by sale type if provided
        sale_type = self.request.query_params.get("sale_type")
        if sale_type:
            queryset = queryset.filter(sale_type=sale_type)
            api_logger.info(f"🔍 Filtering sales by type:{sale_type}")

        # Filter by date range if provided
        start_date = self.request.query_params.get("start_date")
        end_date = self.request.query_params.get("end_date")
        if start_date and end_date:
            queryset = queryset.filter(created_at__date__range=[start_date, end_date])
            api_logger.info(
                f"🔍 Filtering sales by date range: {start_date} to {end_date}"
            )

        api_logger.info(f"✅ Found {queryset.count()} sales")
        return queryset

    def list(self, request, *args, **kwargs):
        try:
            queryset = self.get_queryset()

            # Check if CSV export is requested
            if request.query_params.get("export") == "csv":
                api_logger.info("🔄 Exporting sales data to CSV")
                return self._export_csv(queryset)

            # Use the pagination mechanism provided by DRF
            page = self.paginate_queryset(queryset)
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                response = self.get_paginated_response(serializer.data)
                log_response_data(response, "📤 Sales List Response")
                return response

            serializer = self.get_serializer(queryset, many=True)
            response = Response(serializer.data)
            log_response_data(response, "📤 Sales List Response")
            return response

        except Exception as e:
            log_error(e, "Retrieving sales list", log_full_trace=True)
            return Response(
                {"detail": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def _export_csv(self, queryset):
        """
        Export sales data to CSV file
        """
        api_logger.info("🔄 Creating CSV export for sales data")

        response = HttpResponse(content_type="text/csv")

        # Set filename with date
        filename = f"sales_export_{timezone.now().strftime('%Y%m%d')}.csv"
        response["Content-Disposition"] = f'attachment; filename="{filename}"'
        api_logger.debug(f"Setting filename to: {filename}")

        # Create CSV writer
        writer = csv.writer(response)

        # Write headers
        writer.writerow(
            [
                "ID",
                "Date",
                "Time",
                "Customer",
                "Sale Type",
                "Items",
                "Total Price",
                # "Payment Method",
                # "Status",
            ]
        )

        # Write data rows
        row_count = 0
        for sale in queryset:
            # Prepare items list as comma-joined string
            customer_name = ""
            if sale.user:
                customer_name = f"{sale.user.first_name} {sale.user.last_name}"

            writer.writerow(
                [
                    sale.id,
                    sale.created_at.date(),
                    sale.created_at.time(),
                    customer_name,
                    sale.sale_type,
                    sale.total_price,
                    # sale.payment_method,
                    # sale.status,
                ]
            )
            row_count += 1

        api_logger.info(f"✅ Successfully exported {row_count} sales to CSV")
        return response


class SaleDetailView(generics.RetrieveAPIView):
    """
    GET endpoint to retrieve details of a specific sale.
    """

    permission_classes = [IsAuthenticated, IsOwnerOrReceptionist]
    serializer_class = SaleSerializer
    queryset = Sale.objects.all()

    def retrieve(self, request, *args, **kwargs):
        log_request_data(request, f"📥 Sale Detail Request for ID: {kwargs.get('pk')}")
        try:
            instance = self.get_object()
            api_logger.info(
                f"🔍 Retrieved Sale:{instance.id} for User:{instance.user.id}"
            )
            serializer = self.get_serializer(instance)
            response = Response(serializer.data)
            log_response_data(response, "📤 Sale Detail Response")
            return response
        except Exception as e:
            log_error(e, f"Error retrieving sale with ID: {kwargs.get('pk')}")
            response = Response(
                {"detail": "Sale not found or an error occurred."},
                status=status.HTTP_404_NOT_FOUND,
            )
            log_response_data(response, "📤 Sale Detail Error Response")
            return response


class SaleViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing sales.
    Limited to update and delete only, no create operations.
    """

    queryset = Sale.objects.select_related(
        "user",
        "appointment",
        "user_package",
        "shared_package",
        "unlimited_package",
        "package_option",
        "discount",
    ).all()
    permission_classes = [IsOwnerOrReceptionist]

    def get_serializer_class(self):
        if self.action in ["update", "partial_update"]:
            return SaleUpdateSerializer
        return SaleSerializer

    def create(self, request, *args, **kwargs):
        """
        Block creation through this endpoint - use the main sales API instead
        """
        return Response(
            {"detail": "Direct creation not allowed through this endpoint"},
            status=status.HTTP_405_METHOD_NOT_ALLOWED,
        )

    @action(detail=False, methods=["get"])
    def by_user(self, request):
        """
        Filter sales by user ID
        """
        user_id = request.query_params.get("user_id")
        if user_id:
            sales = self.queryset.filter(user_id=user_id)
            serializer = self.get_serializer(sales, many=True)
            return Response(serializer.data)
        return Response({"error": "user_id parameter is required"}, status=400)

    @action(detail=False, methods=["get"])
    def by_location(self, request):
        """
        Filter sales by location
        """
        location = request.query_params.get("location")
        if location:
            sales = self.queryset.filter(location=location)
            serializer = self.get_serializer(sales, many=True)
            return Response(serializer.data)
        return Response({"error": "location parameter is required"}, status=400)
