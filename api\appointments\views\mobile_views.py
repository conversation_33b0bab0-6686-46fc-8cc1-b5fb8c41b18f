from django.utils import timezone
from datetime import datetime, time
from rest_framework import generics
from rest_framework.permissions import IsAuthenticated
from rest_framework.pagination import PageNumberPagination
from api.appointments.models import Appointment
from api.appointments.serializers import AppointmentSerializer

from utils.logging import (
    api_logger,
    logger,
    log_request_data,
    log_response_data,
    log_error,
)


class StandardResultsSetPagination(PageNumberPagination):
    page_size = 2
    page_size_query_param = "page_size"
    max_page_size = 2


class AppointmentMobileTodayView(generics.ListAPIView):
    """
    Returns appointments for the current local date whose time has not yet passed.
    """
    serializer_class = AppointmentSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        user = self.request.user
        allowed_statuses = ["booked", "check_in"]

        # Get the current local date and time based on TIME_ZONE setting (e.g., Asia/Dubai)
        now_local = timezone.localtime(timezone.now())
        today_date = now_local.date()
        current_time = now_local.time()

        api_logger.info(
            f"🔍 User {user.id} with role {user.role} retrieving today's mobile appointments (future time)"
        )
        api_logger.debug(
            f"Filtering for date={today_date} and time__gt={current_time} in {timezone.get_current_timezone_name()}"
        )

        queryset_filters = {
            "status__in": allowed_statuses,
            "date": today_date,
            "time__gt": current_time,  # Only appointments whose time is in the future today
        }

        if user.role == "customer":
            queryset = Appointment.objects.filter(
                customer=user,
                **queryset_filters,
            ).order_by("time")
            api_logger.debug(
                f"Customer {user.id} retrieved {queryset.count()} today's future appointments"
            )
            return queryset
        elif user.role == "therapist":
            queryset = Appointment.objects.filter(
                therapist__user=user,
                **queryset_filters,
            ).order_by("time")
            api_logger.debug(
                f"Therapist {user.id} retrieved {queryset.count()} today's future appointments"
            )
            return queryset
        elif user.role in ["owner", "receptionist"]:
            # For admin roles, you might want to see all appointments for today,
            # regardless of whether their time has passed or not.
            # If you only want future appointments for admins too, keep time__gt.
            # For this example, I'm showing all today's appointments for admins.
            admin_filters = {
                "status__in": allowed_statuses,
                "date": today_date,
                # 'time__gt': current_time, # Uncomment this if admins also only see future appointments today
            }
            queryset = Appointment.objects.filter(
                **admin_filters,
            ).order_by("time")
            api_logger.debug(
                f"Admin {user.id} retrieved {queryset.count()} today's appointments (all times)"
            )
            return queryset

        api_logger.warning(
            f"User {user.id} with unsupported role {user.role} attempted to access today's appointments"
        )
        return Appointment.objects.none()


class AppointmentMobileNextView(generics.ListAPIView):
    """
    Returns appointments for dates strictly after the current local date.
    Added extensive logging for debugging purposes.
    """
    serializer_class = AppointmentSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        user = self.request.user
        allowed_statuses = ["booked", "check_in"]

        # --- IMPORTANT: Get current local date based on TIME_ZONE setting ---
        now_utc = timezone.now() # This is always UTC if USE_TZ=True
        now_local = timezone.localtime(now_utc) # Convert to the local TIME_ZONE
        today_date = now_local.date() # Extract date component from the local time

        api_logger.info(
            f"🔍 User {user.id} with role {user.role} attempting to retrieve next mobile appointments."
        )
        api_logger.info(
            f"Current UTC datetime: {now_utc.isoformat()} | Local datetime ({timezone.get_current_timezone_name()}): {now_local.isoformat()}"
        )
        api_logger.info(
            f"Determined 'today_date' (local): {today_date}"
        )
        api_logger.info(
            f"Allowed appointment statuses: {allowed_statuses}"
        )

        queryset_filters = {
            "status__in": allowed_statuses,
            "date__gt": today_date,  # Filter for dates strictly AFTER today_date
        }

        base_queryset = Appointment.objects.all()

        # Log initial filter parameters for the query
        api_logger.debug(f"Base query filters: {queryset_filters}")

        if user.role == "customer":
            api_logger.debug(f"User is a customer ({user.email}). Adding customer filter.")
            queryset = base_queryset.filter(
                customer=user,
                **queryset_filters,
            ).order_by("date", "time")
            api_logger.info(
                f"Customer {user.id} ({user.email}) query executed. Found {queryset.count()} upcoming appointments."
            )
            # Log the specific appointments found (optional, but helpful if count is 0)
            if queryset.exists():
                for appt in queryset:
                    api_logger.debug(f"  - Appt found: ID={appt.id}, Date={appt.date}, Time={appt.time}, Status={appt.status}, Customer={appt.customer.email if appt.customer else 'N/A'}")
            else:
                api_logger.debug("  - No customer upcoming appointments matched the query.")
            return queryset

        elif user.role == "therapist":
            api_logger.debug(f"User is a therapist ({user.email}). Adding therapist filter.")
            queryset = base_queryset.filter(
                therapist__user=user, # Ensure your Therapist model links to User model via a 'user' field
                **queryset_filters,
            ).order_by("date", "time")
            api_logger.info(
                f"Therapist {user.id} ({user.email}) query executed. Found {queryset.count()} upcoming appointments."
            )
            # Log the specific appointments found
            if queryset.exists():
                for appt in queryset:
                    api_logger.debug(f"  - Appt found: ID={appt.id}, Date={appt.date}, Time={appt.time}, Status={appt.status}, Therapist={appt.therapist.user.email if appt.therapist and appt.therapist.user else 'N/A'}")
            else:
                api_logger.debug("  - No therapist upcoming appointments matched the query.")
            return queryset

        elif user.role in ["owner", "receptionist"]:
            api_logger.debug(f"User is an admin ({user.email}). Fetching all upcoming appointments.")
            queryset = base_queryset.filter(
                **queryset_filters,
            ).order_by("date", "time")
            api_logger.debug(
                f"Admin {user.id} ({user.email}) query executed. Found {queryset.count()} upcoming appointments."
            )
            # Log the specific appointments found
            if queryset.exists():
                for appt in queryset:
                    api_logger.debug(f"  - Appt found: ID={appt.id}, Date={appt.date}, Time={appt.time}, Status={appt.status}, Customer={appt.customer.email if appt.customer else 'N/A'}, Therapist={appt.therapist.user.email if appt.therapist and appt.therapist.user else 'N/A'}")
            else:
                api_logger.debug("  - No admin upcoming appointments matched the query.")
            return queryset

        api_logger.warning(
            f"User {user.id} with unsupported role {user.role} attempted to access upcoming appointments. Returning empty queryset."
        )
        return Appointment.objects.none()


class AppointmentMobilePastView(generics.ListAPIView):
    """
    Returns appointments for dates strictly before the current local date.
    """
    serializer_class = AppointmentSerializer
    permission_classes = [IsAuthenticated]
    pagination_class = StandardResultsSetPagination

    def get_queryset(self):
        user = self.request.user
        allowed_statuses = ["booked", "check_in"]

        # Get the current local date based on TIME_ZONE setting (e.g., Asia/Dubai)
        today_date = timezone.localtime(timezone.now()).date()

        api_logger.info(
            f"🔍 User {user.id} with role {user.role} retrieving past mobile appointments"
        )
        api_logger.debug(
            f"Filtering for date__lt={today_date} in {timezone.get_current_timezone_name()}"
        )

        queryset_filters = {
            "status__in": allowed_statuses,
            "date__lt": today_date,  # Strictly less than today's date
        }

        if user.role == "customer":
            queryset = Appointment.objects.filter(
                customer=user,
                **queryset_filters,
            ).order_by("-date", "-time")  # Order by most recent past first
            api_logger.debug(
                f"Customer {user.id} retrieved {queryset.count()} past appointments"
            )
            return queryset
        elif user.role == "therapist":
            queryset = Appointment.objects.filter(
                therapist__user=user,
                **queryset_filters,
            ).order_by("-date", "-time")  # Order by most recent past first
            api_logger.debug(
                f"Therapist {user.id} retrieved {queryset.count()} past appointments"
            )
            return queryset
        elif user.role in ["owner", "receptionist"]:
            queryset = Appointment.objects.filter(
                **queryset_filters,
            ).order_by("-date", "-time")  # Order by most recent past first
            api_logger.debug(
                f"Admin {user.id} retrieved {queryset.count()} past appointments"
            )
            return queryset

        api_logger.warning(
            f"User {user.id} with unsupported role {user.role} attempted to access past appointments"
        )
        return Appointment.objects.none()