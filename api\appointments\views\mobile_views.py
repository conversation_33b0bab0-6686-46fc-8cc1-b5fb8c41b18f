from django.utils import timezone
from datetime import datetime, time
from rest_framework import generics
from rest_framework.permissions import IsAuthenticated
from rest_framework.pagination import PageNumberPagination
from api.appointments.models import Appointment
from api.appointments.serializers import AppointmentSerializer

from utils.logging import (
    api_logger,
    logger,
    log_request_data,
    log_response_data,
    log_error,
)


class StandardResultsSetPagination(PageNumberPagination):
    page_size = 2
    page_size_query_param = "page_size"
    max_page_size = 2


class AppointmentMobileTodayView(generics.ListAPIView):
    serializer_class = AppointmentSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        user = self.request.user
        allowed_statuses = ["booked", "check_in"]

        today = timezone.now().date()

        api_logger.info(
            f"🔍 User {user.id} with role {user.role} retrieving today's mobile appointments"
        )

        if user.role == "customer":
            queryset = Appointment.objects.filter(
                customer=user,
                status__in=allowed_statuses,
                date=today,
            ).order_by("time")
            api_logger.debug(
                f"Customer {user.id} retrieved {queryset.count()} today's appointments"
            )
            return queryset
        elif user.role == "therapist":
            queryset = Appointment.objects.filter(
                therapist__user=user,
                status__in=allowed_statuses,
                date=today,
            ).order_by("time")
            api_logger.debug(
                f"Therapist {user.id} retrieved {queryset.count()} today's appointments"
            )
            return queryset
        elif user.role in ["owner", "receptionist"]:
            queryset = Appointment.objects.filter(
                status__in=allowed_statuses,
                date=today,
            ).order_by("time")
            api_logger.debug(
                f"Admin {user.id} retrieved {queryset.count()} today's appointments"
            )
            return queryset

        api_logger.warning(
            f"User {user.id} with unsupported role {user.role} attempted to access today's appointments"
        )
        return Appointment.objects.none()


class AppointmentMobileNextView(generics.ListAPIView):
    serializer_class = AppointmentSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        user = self.request.user
        allowed_statuses = ["booked", "check_in"]

        today = timezone.now().date()

        api_logger.info(
            f"🔍 User {user.id} with role {user.role} retrieving next mobile appointments"
        )

        if user.role == "customer":
            queryset = Appointment.objects.filter(
                customer=user,
                status__in=allowed_statuses,
                date__gt=today,
            ).order_by("date", "time")
            api_logger.debug(
                f"Customer {user.id} retrieved {queryset.count()} upcoming appointments"
            )
            return queryset
        elif user.role == "therapist":
            queryset = Appointment.objects.filter(
                therapist__user=user,
                status__in=allowed_statuses,
                date__gt=today,
            ).order_by("date", "time")
            api_logger.debug(
                f"Therapist {user.id} retrieved {queryset.count()} upcoming appointments"
            )
            return queryset
        elif user.role in ["owner", "receptionist"]:
            queryset = Appointment.objects.filter(
                status__in=allowed_statuses,
                date__gt=today,
            ).order_by("date", "time")
            api_logger.debug(
                f"Admin {user.id} retrieved {queryset.count()} upcoming appointments"
            )
            return queryset

        api_logger.warning(
            f"User {user.id} with unsupported role {user.role} attempted to access upcoming appointments"
        )
        return Appointment.objects.none()


class AppointmentMobilePastView(generics.ListAPIView):
    serializer_class = AppointmentSerializer
    permission_classes = [IsAuthenticated]
    pagination_class = StandardResultsSetPagination

    def get_queryset(self):
        user = self.request.user
        allowed_statuses = ["booked", "check_in"]

        today = timezone.now().date()

        api_logger.info(
            f"🔍 User {user.id} with role {user.role} retrieving past mobile appointments"
        )

        if user.role == "customer":
            queryset = Appointment.objects.filter(
                customer=user,
                status__in=allowed_statuses,
                date__lt=today,
            ).order_by("-date", "-time")
            api_logger.debug(
                f"Customer {user.id} retrieved {queryset.count()} past appointments"
            )
            return queryset
        elif user.role == "therapist":
            queryset = Appointment.objects.filter(
                therapist__user=user,
                status__in=allowed_statuses,
                date__lt=today,
            ).order_by("-date", "-time")
            api_logger.debug(
                f"Therapist {user.id} retrieved {queryset.count()} past appointments"
            )
            return queryset
        elif user.role in ["owner", "receptionist"]:
            queryset = Appointment.objects.filter(
                status__in=allowed_statuses,
                date__lt=today,
            ).order_by("-date", "-time")
            api_logger.debug(
                f"Admin {user.id} retrieved {queryset.count()} past appointments"
            )
            return queryset

        api_logger.warning(
            f"User {user.id} with unsupported role {user.role} attempted to access past appointments"
        )
        return Appointment.objects.none()