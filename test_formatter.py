#!/usr/bin/env python3
"""
Test output formatter for readable test results
"""
import subprocess
import sys
import re
from datetime import datetime

def extract_readable_error(lines, test_name):
    """Extract a readable error message from pytest output"""
    error_patterns = [
        # Common assertion errors
        (r'E\s+assert (\d+) == (\d+)', 'Expected {1}, but got {0}'),
        (r'E\s+assert (\d+) != (\d+)', 'Expected different value, but both were {0}'),
        (r'E\s+assert.*status_code.*==.*(\d+)', 'Expected HTTP status {1}'),
        (r'E\s+assert.*"([^"]*)".*in.*str', 'Expected to find "{0}" in response'),
        (r'TypeError: (.+)', 'Type error: {0}'),
        (r'AttributeError: (.+)', 'Attribute error: {0}'),
        (r'KeyError: (.+)', 'Missing key: {0}'),
        (r'AssertionError: (.+)', 'Asser<PERSON> failed: {0}'),
    ]

    # Look for error lines - find lines with "E   assert" pattern
    for line in lines:
        if "E   assert" in line:
            clean_line = line.strip()

            # Check against patterns
            for pattern, template in error_patterns:
                match = re.search(pattern, clean_line)
                if match:
                    try:
                        return template.format(*match.groups())
                    except:
                        pass

            # If no pattern matches, return a cleaned version
            if "assert" in clean_line.lower():
                clean_error = clean_line.replace("E   assert ", "Expected: ").replace("AssertionError:", "")
                return clean_error

    # If no assertion error found, look for other error types
    for line in lines:
        if any(error_type in line for error_type in ["TypeError:", "AttributeError:", "KeyError:", "AssertionError:"]):
            for pattern, template in error_patterns:
                match = re.search(pattern, line)
                if match:
                    try:
                        return template.format(*match.groups())
                    except:
                        pass

    return "Check test logs for detailed error information"

def run_tc_p_001():
    """Run TC-P-001 tests with readable output"""
    print("🧪 StretchUp API - TC-P-001 Test Results")
    print("=" * 60)
    print("📋 Test Case: Commission Profile Creation Access Control")
    print("🎯 Requirement: Only owners should be able to create commission profiles")
    print("=" * 60)
    print()

    # Test descriptions
    tests = {
        "test_owner_can_create_commission_profile": "Owner can create commission profiles",
        "test_receptionist_cannot_create_commission_profile": "Receptionist cannot create commission profiles",
        "test_therapist_cannot_create_commission_profile": "Therapist cannot create commission profiles",
        "test_customer_cannot_create_commission_profile": "Customer cannot create commission profiles",
        "test_unauthenticated_user_cannot_create_commission_profile": "Unauthenticated user cannot create commission profiles"
    }

    return _run_test_class("TestCommissionProfileCreation", tests)



def run_tc_p_002():
    """Run TC-P-002 tests with readable output"""
    print("🧪 StretchUp API - TC-P-002 Test Results")
    print("=" * 60)
    print("📋 Test Case: Commission Profile Update Access Control")
    print("🎯 Requirement: Only owners should be able to update commission profiles")
    print("=" * 60)
    print()

    # Test descriptions
    tests = {
        "test_owner_can_update_commission_profile": "Owner can update commission profiles (PATCH)",
        "test_receptionist_cannot_update_commission_profile": "Receptionist cannot access commission profiles (404)",
        "test_therapist_cannot_update_commission_profile": "Therapist cannot update commission profiles",
        "test_customer_cannot_update_commission_profile": "Customer cannot access commission profiles (404)",
        "test_unauthenticated_user_cannot_update_commission_profile": "Unauthenticated user cannot update commission profiles",
        "test_owner_can_full_update_commission_profile": "Owner can update commission profiles (PUT)"
    }

    return _run_test_class("TestCommissionProfileUpdate", tests)

def run_tc_p_003():
    """Run TC-P-003 tests with readable output"""
    print("🧪 StretchUp API - TC-P-003 Test Results")
    print("=" * 60)
    print("📋 Test Case: Commission Profile Delete Access Control")
    print("🎯 Requirement: Only owners should be able to delete commission profiles")
    print("=" * 60)
    print()

    # Test descriptions
    tests = {
        "test_owner_can_delete_commission_profile": "Owner can delete commission profiles",
        "test_receptionist_cannot_delete_commission_profile": "Receptionist cannot access commission profiles (404)",
        "test_therapist_cannot_delete_commission_profile": "Therapist cannot delete commission profiles",
        "test_customer_cannot_delete_commission_profile": "Customer cannot access commission profiles (404)",
        "test_unauthenticated_user_cannot_delete_commission_profile": "Unauthenticated user cannot delete commission profiles"
    }

    return _run_test_class("TestCommissionProfileDelete", tests)

def run_tc_p_004():
    """Run TC-P-004 tests with readable output"""
    print("🧪 StretchUp API - TC-P-004 Test Results")
    print("=" * 60)
    print("📋 Test Case: Commission Profile List Access Control")
    print("🎯 Requirement: Only owners should be able to list all commission profiles")
    print("=" * 60)
    print()

    # Test descriptions
    tests = {
        "test_owner_can_list_all_commission_profiles": "Owner can list all commission profiles",
        "test_receptionist_cannot_list_commission_profiles": "Receptionist cannot list commission profiles",
        "test_therapist_can_only_see_own_commission_profiles": "Therapist can only see their own commission profiles",
        "test_customer_cannot_list_commission_profiles": "Customer cannot list commission profiles",
        "test_unauthenticated_user_cannot_list_commission_profiles": "Unauthenticated user cannot list commission profiles",
        "test_owner_can_retrieve_specific_commission_profile": "Owner can retrieve specific commission profiles",
        "test_receptionist_cannot_retrieve_specific_commission_profile": "Receptionist cannot retrieve specific commission profiles"
    }

    return _run_test_class("TestCommissionProfileList", tests)

def run_tc_p_005():
    """Run TC-P-005 tests with readable output"""
    print("🧪 StretchUp API - TC-P-005 Test Results")
    print("=" * 60)
    print("📋 Test Case: Commission Profile Status Management")
    print("🎯 Requirement: Only owners should be able to activate/deactivate commission profiles")
    print("=" * 60)
    print()

    # Test descriptions
    tests = {
        "test_owner_can_deactivate_commission_profile": "Owner can deactivate commission profiles",
        "test_owner_can_activate_commission_profile": "Owner can activate commission profiles",
        "test_receptionist_cannot_deactivate_commission_profile": "Receptionist cannot deactivate commission profiles (403)",
        "test_receptionist_cannot_activate_commission_profile": "Receptionist cannot activate commission profiles (403)",
        "test_therapist_cannot_deactivate_commission_profile": "Therapist cannot deactivate commission profiles (even their own)",
        "test_therapist_cannot_activate_commission_profile": "Therapist cannot activate commission profiles (even their own)",
        "test_customer_cannot_deactivate_commission_profile": "Customer cannot deactivate commission profiles (403)",
        "test_customer_cannot_activate_commission_profile": "Customer cannot activate commission profiles (403)",
        "test_unauthenticated_user_cannot_deactivate_commission_profile": "Unauthenticated user cannot deactivate commission profiles",
        "test_unauthenticated_user_cannot_activate_commission_profile": "Unauthenticated user cannot activate commission profiles"
    }

    return _run_test_class("TestCommissionProfileStatusManagement", tests)

def run_tc_p_006():
    """Run TC-P-006 tests with readable output"""
    print("🧪 StretchUp API - TC-P-006 Test Results")
    print("=" * 60)
    print("📋 Test Case: Commission Profile Visibility")
    print("🎯 Requirement: Deactivated profiles should not be visible in rule creation page")
    print("=" * 60)
    print()

    # Test descriptions
    tests = {
        "test_owner_sees_only_active_profiles_by_default": "Owner sees only active profiles by default",
        "test_owner_can_see_all_profiles_with_include_inactive_parameter": "Owner can see all profiles with include_inactive parameter",
        "test_therapist_sees_only_own_active_profiles_by_default": "Therapist sees only own active profiles by default",
        "test_therapist_can_see_own_inactive_profiles_with_include_inactive_parameter": "Therapist can see own inactive profiles with include_inactive parameter",
        "test_receptionist_sees_no_profiles_regardless_of_status": "Receptionist sees no profiles regardless of status",
        "test_customer_sees_no_profiles_regardless_of_status": "Customer sees no profiles regardless of status",
        "test_unauthenticated_user_cannot_see_any_profiles": "Unauthenticated user cannot see any profiles",
        "test_profile_becomes_invisible_after_deactivation": "Profile becomes invisible after deactivation",
        "test_profile_becomes_visible_after_activation": "Profile becomes visible after activation"
    }

    return _run_test_class("TestCommissionProfileVisibility", tests)

def run_tc_p_007():
    """Run TC-P-007 tests with readable output"""
    print("🧪 StretchUp API - TC-P-007 Test Results")
    print("=" * 60)
    print("📋 Test Case: Commission Profile Default Creation")
    print("🎯 Requirement: Only one profile can be set as default at a time with rule copying")
    print("=" * 60)
    print()

    # Test descriptions
    tests = {
        "test_create_first_default_profile_no_previous_default": "Create first default profile (no previous default)",
        "test_create_second_default_profile_copies_rules_and_unsets_previous": "Create second default profile (copies rules and unsets previous)",
        "test_create_regular_profile_does_not_affect_default": "Create regular profile (does not affect default)",
        "test_only_owner_can_create_default_profiles": "Only owner can create default profiles",
        "test_receptionist_cannot_create_default_profiles": "Receptionist cannot create default profiles",
        "test_cross_therapist_defaults_are_independent": "Cross-therapist defaults are independent",
        "test_transaction_rollback_on_rule_copy_failure": "Transaction rollback on rule copy failure",
        "test_empty_rules_copying_handles_gracefully": "Empty rules copying handles gracefully",
        "test_multiple_rules_with_different_types_copied_correctly": "Multiple rules with different types copied correctly"
    }

    return _run_test_class("TestCommissionProfileDefaultCreation", tests)

def run_tc_p_008():
    """Run TC-P-008 tests with readable output"""
    print("🧪 StretchUp API - TC-P-008 Test Results")
    print("=" * 60)
    print("📋 Test Case: Commission Profile Status Restrictions")
    print("🎯 Requirement: Active and default profiles cannot have status changed")
    print("=" * 60)
    print()

    # Test descriptions
    tests = {
        "test_owner_cannot_deactivate_active_default_profile": "Owner cannot deactivate active default profiles",
        "test_owner_cannot_deactivate_inactive_default_profile": "Owner cannot deactivate inactive default profiles",
        "test_owner_can_deactivate_active_non_default_profile": "Owner can deactivate active non-default profiles",
        "test_owner_cannot_activate_inactive_default_profile_if_another_default_exists": "Owner cannot activate inactive default if another default exists",
        "test_receptionist_cannot_deactivate_any_profile_including_default": "Receptionist cannot deactivate any profiles (including default)",
        "test_therapist_cannot_deactivate_own_default_profile": "Therapist cannot deactivate their own default profile",
        "test_customer_cannot_deactivate_any_profile": "Customer cannot deactivate any profiles",
        "test_unauthenticated_user_cannot_deactivate_any_profile": "Unauthenticated user cannot deactivate any profiles"
    }

    return _run_test_class("TestCommissionProfileStatusRestrictions", tests)

def run_tc_p_009():
    """Run TC-P-009 tests with readable output"""
    print("🧪 StretchUp API - TC-P-009 Test Results")
    print("=" * 60)
    print("📋 Test Case: Commission Profile Therapist Assignment Restrictions")
    print("🎯 Requirement: Profiles with assigned therapists cannot be deactivated until therapists are reassigned")
    print("=" * 60)
    print()

    # Test descriptions
    tests = {
        "test_owner_can_deactivate_non_default_profile_when_therapist_has_multiple_active_profiles": "Owner can deactivate non-default profile when therapist has multiple active profiles",
        "test_owner_cannot_deactivate_default_profile_of_active_therapist": "Owner cannot deactivate default profile of active therapist (TC-P-008 + TC-P-009)",
        "test_owner_can_deactivate_profile_of_inactive_therapist": "Owner can deactivate profile when therapist is inactive",
        "test_owner_can_deactivate_already_inactive_profile_of_active_therapist": "Attempting to deactivate already inactive profile gives appropriate error",
        "test_owner_cannot_deactivate_only_active_profile_of_active_therapist": "Owner cannot deactivate the only active profile of an active therapist",
        "test_therapist_assignment_check_with_multiple_profiles": "System correctly identifies therapist assignment with multiple profiles and allows deactivation",
        "test_receptionist_cannot_deactivate_assigned_profile": "Receptionist cannot deactivate assigned profiles (permission check)",
        "test_therapist_cannot_deactivate_own_assigned_profile": "Therapist cannot deactivate their own assigned profiles",
        "test_unauthenticated_user_cannot_deactivate_assigned_profile": "Unauthenticated user cannot deactivate assigned profiles"
    }

    return _run_test_class("TestCommissionProfileTherapistAssignment", tests)

def run_tc_p_010():
    """Run TC-P-010 tests with readable output"""
    print("🧪 StretchUp API - TC-P-010 Test Results")
    print("=" * 60)
    print("📋 Test Case: Commission Profile Assignment Access Control")
    print("🎯 Requirement: Only owner can assign profiles to therapists")
    print("=" * 60)
    print()

    # Test descriptions
    tests = {
        "test_owner_can_assign_inactive_profile_to_therapist": "Owner can assign inactive profiles to therapists",
        "test_owner_can_reassign_profile_to_different_therapist": "Owner can reassign profiles to different therapists",
        "test_receptionist_cannot_assign_profiles": "Receptionist cannot assign profiles",
        "test_therapist_cannot_assign_profiles": "Therapist cannot assign profiles",
        "test_customer_cannot_assign_profiles": "Customer cannot assign profiles",
        "test_unauthenticated_user_cannot_assign_profiles": "Unauthenticated user cannot assign profiles",
        "test_assign_profile_with_missing_therapist_id": "Assignment fails when therapist_id is missing",
        "test_assign_profile_with_invalid_therapist_id": "Assignment fails when therapist_id is invalid",
        "test_reassign_profile_to_same_therapist": "Reassignment fails when trying to assign to same therapist"
    }

    return _run_test_class("TestCommissionProfileAssignment", tests)

def run_tc_p_011():
    """Run TC-P-011 tests with readable output"""
    print("🧪 StretchUp API - TC-P-011 Test Results")
    print("=" * 60)
    print("📋 Test Case: Single Active Profile Constraint")
    print("🎯 Requirement: Therapist can be assigned only one active profile")
    print("=" * 60)
    print()

    # Test descriptions
    tests = {
        "test_owner_cannot_assign_active_profile_to_therapist_with_active_profile": "Owner cannot assign active profile to therapist with existing active profile",
        "test_owner_can_assign_inactive_profile_to_therapist_with_active_profile": "Owner can assign inactive profile to therapist with existing active profile"
    }

    return _run_test_class("TestCommissionProfileAssignment", tests)

def run_tc_p_012():
    """Run TC-P-012 tests with readable output"""
    print("🧪 StretchUp API - TC-P-012 Test Results")
    print("=" * 60)
    print("📋 Test Case: Automatic Default Profile Assignment")
    print("🎯 Requirement: Default profile is automatically assigned to new therapists")
    print("=" * 60)
    print()

    # Test descriptions
    tests = {
        "test_default_profile_created_for_new_therapist_via_management_command": "Default profile created for new therapist via management command",
        "test_only_one_default_profile_per_therapist": "Only one default profile per therapist"
    }

    return _run_test_class("TestCommissionProfileAutoAssignment", tests)

def _run_test_class(test_class_name, tests, test_file_name=None):
    """Helper function to run a specific test class"""
    try:
        # Determine which test file to use based on test class name or explicit file name
        if test_file_name:
            test_file = f"api/commissions/tests/{test_file_name}.py"
        elif test_class_name.startswith("TestCommissionRule"):
            test_file = "api/commissions/tests/test_commission_rules.py"
        else:
            test_file = "api/commissions/tests/test_commission_profiles.py"

        # Run the tests
        cmd = [
            "docker-compose", "exec", "web", "pytest",
            f"{test_file}::{test_class_name}",
            "-v", "--tb=short", "--no-header"
        ]

        result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)

        # Parse the output (combine stdout and stderr since pytest might output to stderr)
        all_output = result.stdout + "\n" + result.stderr
        lines = all_output.split('\n')

        passed_count = 0
        failed_count = 0

        for line in lines:
            for test_name, description in tests.items():
                if test_name in line:
                    if "PASSED" in line:
                        print(f"✅ PASSED: {description}")
                        passed_count += 1
                    elif "FAILED" in line:
                        print(f"❌ FAILED: {description}")
                        failed_count += 1
                    break

        # Show any errors in a user-friendly way
        if failed_count > 0:
            print("\n🔍 Failed Test Details:")
            print("-" * 40)

            # Extract specific error information
            failed_tests = []
            for line in lines:
                for test_name, description in tests.items():
                    if test_name in line and "FAILED" in line:
                        failed_tests.append((test_name, description))
                        break

            for test_name, description in failed_tests:
                print(f"❌ {description}")

                # Extract readable error message
                error_msg = extract_readable_error(lines, test_name)
                print(f"   💡 Issue: {error_msg}")
                print()

        print()
        print("=" * 60)
        print(f"📊 Summary: {passed_count} passed, {failed_count} failed")

        if failed_count == 0:
            print("🎉 All TC-P-001 tests passed! Access control is working correctly.")
        else:
            print("⚠️  Some tests failed. Please check the implementation.")

        print("=" * 60)

        return result.returncode == 0

    except subprocess.TimeoutExpired:
        print("❌ Tests timed out after 2 minutes")
        return False
    except Exception as e:
        print(f"❌ Error running tests: {e}")
        return False

def run_tc_r_001():
    """Run TC-R-001 tests with readable output"""
    print("🧪 StretchUp API - TC-R-001 Test Results")
    print("=" * 60)
    print("📋 Test Case: Commission Rule Creation Access Control")
    print("🎯 Requirement: Only owners should be able to create commission rules")
    print("=" * 60)
    print()

    # Test descriptions
    tests = {
        "test_owner_can_create_commission_rule": "Owner can create commission rules",
        "test_receptionist_cannot_create_commission_rule": "Receptionist cannot create commission rules",
        "test_therapist_cannot_create_commission_rule": "Therapist cannot create commission rules",
        "test_customer_cannot_create_commission_rule": "Customer cannot create commission rules",
        "test_unauthenticated_user_cannot_create_commission_rule": "Unauthenticated user cannot create commission rules"
    }

    return _run_test_class("TestCommissionRuleCreation", tests)

def run_tc_r_002():
    """Run TC-R-002 tests with readable output"""
    print("🧪 StretchUp API - TC-R-002 Test Results")
    print("=" * 60)
    print("📋 Test Case: Commission Rule Update Access Control")
    print("🎯 Requirement: Only owners should be able to update commission rules")
    print("=" * 60)
    print()

    # Test descriptions
    tests = {
        "test_owner_can_update_commission_rule": "Owner can update commission rules (PATCH)",
        "test_receptionist_cannot_update_commission_rule": "Receptionist cannot update commission rules",
        "test_therapist_cannot_update_commission_rule": "Therapist cannot update commission rules",
        "test_customer_cannot_update_commission_rule": "Customer cannot update commission rules",
        "test_unauthenticated_user_cannot_update_commission_rule": "Unauthenticated user cannot update commission rules",
        "test_owner_can_full_update_commission_rule": "Owner can update commission rules (PUT)"
    }

    return _run_test_class("TestCommissionRuleUpdate", tests)

def run_tc_r_003():
    """Run TC-R-003 tests with readable output"""
    print("🧪 StretchUp API - TC-R-003 Test Results")
    print("=" * 60)
    print("📋 Test Case: Commission Rule Delete Access Control")
    print("🎯 Requirement: Only owners should be able to delete commission rules")
    print("=" * 60)
    print()

    # Test descriptions
    tests = {
        "test_owner_can_delete_commission_rule": "Owner can delete commission rules",
        "test_receptionist_cannot_delete_commission_rule": "Receptionist cannot delete commission rules",
        "test_therapist_cannot_delete_commission_rule": "Therapist cannot delete commission rules",
        "test_customer_cannot_delete_commission_rule": "Customer cannot delete commission rules",
        "test_unauthenticated_user_cannot_delete_commission_rule": "Unauthenticated user cannot delete commission rules"
    }

    return _run_test_class("TestCommissionRuleDelete", tests)

def run_tc_r_004():
    """Run TC-R-004 tests with readable output"""
    print("🧪 StretchUp API - TC-R-004 Test Results")
    print("=" * 60)
    print("📋 Test Case: Commission Rule List Access Control")
    print("🎯 Requirement: Proper access control for listing commission rules")
    print("=" * 60)
    print()

    # Test descriptions
    tests = {
        "test_owner_can_list_all_commission_rules": "Owner can list all commission rules",
        "test_receptionist_cannot_list_commission_rules": "Receptionist cannot list commission rules",
        "test_therapist_can_only_see_own_commission_rules": "Therapist can only see own commission rules",
        "test_customer_cannot_list_commission_rules": "Customer cannot list commission rules",
        "test_unauthenticated_user_cannot_list_commission_rules": "Unauthenticated user cannot list commission rules",
        "test_owner_can_retrieve_specific_commission_rule": "Owner can retrieve specific commission rules",
        "test_receptionist_cannot_retrieve_specific_commission_rule": "Receptionist cannot retrieve specific commission rules",
        "test_owner_sees_only_active_rules_by_default": "Owner sees only active rules by default",
        "test_owner_can_see_all_rules_with_include_inactive_parameter": "Owner can see all rules with include_inactive parameter",
        "test_therapist_sees_only_own_active_rules_by_default": "Therapist sees only own active rules by default",
        "test_therapist_can_see_own_inactive_rules_with_include_inactive_parameter": "Therapist can see own inactive rules with include_inactive parameter",
        "test_rule_becomes_invisible_after_deactivation": "Rule becomes invisible after deactivation",
        "test_rule_becomes_visible_after_activation": "Rule becomes visible after activation"
    }

    return _run_test_class("TestCommissionRuleList", tests)

def run_tc_r_005():
    """Run TC-R-005 tests with readable output"""
    print("🧪 StretchUp API - TC-R-005 Test Results")
    print("=" * 60)
    print("📋 Test Case: Rule Creation with Percentage-based Commission")
    print("🎯 Requirement: Verify rule creation with percentage-based commission")
    print("=" * 60)
    print()

    # Test descriptions
    tests = {
        "test_owner_can_create_percentage_based_rule": "Owner can create percentage-based rules",
        "test_percentage_rule_validation": "Percentage values are validated correctly",
        "test_percentage_rule_with_service_type": "Percentage-based service-specific rules",
        "test_percentage_rule_with_package_type": "Percentage-based package-specific rules"
    }

    return _run_test_class("TestCommissionRuleCreationWithPercentage", tests, "test_commission_rules_advanced")

def run_tc_r_006():
    """Run TC-R-006 tests with readable output"""
    print("🧪 StretchUp API - TC-R-006 Test Results")
    print("=" * 60)
    print("📋 Test Case: Rule Creation with Fixed Amount Commission")
    print("🎯 Requirement: Verify rule creation with fixed amount commission")
    print("=" * 60)
    print()

    # Test descriptions
    tests = {
        "test_owner_can_create_fixed_amount_rule": "Owner can create fixed amount rules",
        "test_fixed_amount_rule_validation": "Fixed amount values are validated correctly",
        "test_fixed_amount_rule_with_service_type": "Fixed amount service-specific rules"
    }

    return _run_test_class("TestCommissionRuleCreationWithFixedAmount", tests, "test_commission_rules_advanced")

def run_tc_r_007():
    """Run TC-R-007 tests with readable output"""
    print("🧪 StretchUp API - TC-R-007 Test Results")
    print("=" * 60)
    print("📋 Test Case: Minimum Session Threshold Enforcement")
    print("🎯 Requirement: Verify minimum session threshold is enforced (minimum 15 sessions)")
    print("=" * 60)
    print()

    # Test descriptions
    tests = {
        "test_rule_creation_with_valid_min_sessions": "Rules with valid minimum session thresholds (15+)",
        "test_rule_creation_rejects_low_min_sessions": "Rules with less than 15 minimum sessions are rejected",
        "test_rule_creation_accepts_zero_min_sessions_for_special_cases": "Zero minimum sessions for special cases"
    }

    return _run_test_class("TestCommissionRuleMinimumSessionThreshold", tests, "test_commission_rules_advanced")

def run_tc_r_008():
    """Run TC-R-008 tests with readable output"""
    print("🧪 StretchUp API - TC-R-008 Test Results")
    print("=" * 60)
    print("📋 Test Case: Rules Applied After Minimum Session Threshold")
    print("🎯 Requirement: Verify rules are only applied after minimum session threshold is met")
    print("=" * 60)
    print()

    # Test descriptions
    tests = {
        "test_rule_not_applied_below_threshold": "Rules not applied when sessions below threshold",
        "test_rule_applied_at_threshold": "Rules applied when sessions meet threshold",
        "test_rule_applied_above_threshold": "Rules applied when sessions exceed threshold",
        "test_threshold_check_with_multiple_rules": "Threshold checking with multiple rules"
    }

    return _run_test_class("TestCommissionRuleThresholdApplication", tests, "test_commission_rules_business_logic")

def run_tc_r_009():
    """Run TC-R-009 tests with readable output"""
    print("🧪 StretchUp API - TC-R-009 Test Results")
    print("=" * 60)
    print("📋 Test Case: Rule Activation and Deactivation")
    print("🎯 Requirement: Verify rules can be activated/deactivated")
    print("=" * 60)
    print()

    # Test descriptions
    tests = {
        "test_owner_can_deactivate_commission_rule": "Owner can deactivate commission rules",
        "test_owner_can_activate_commission_rule": "Owner can activate commission rules",
        "test_receptionist_cannot_activate_commission_rule": "Receptionist cannot activate commission rules",
        "test_receptionist_cannot_deactivate_commission_rule": "Receptionist cannot deactivate commission rules"
    }

    return _run_test_class("TestCommissionRuleActivation", tests, "test_commission_rules_advanced")

def run_tc_r_010():
    """Run TC-R-010 tests with readable output"""
    print("🧪 StretchUp API - TC-R-010 Test Results")
    print("=" * 60)
    print("📋 Test Case: Rule Deactivation Restrictions")
    print("🎯 Requirement: Verify rules cannot be deactivated if it's the only rule for a profile")
    print("=" * 60)
    print()

    # Test descriptions
    tests = {
        "test_cannot_deactivate_only_rule_for_profile": "Cannot deactivate only rule for profile",
        "test_can_deactivate_rule_when_other_active_rules_exist": "Can deactivate when other active rules exist",
        "test_can_deactivate_global_rule_when_profile_rules_exist": "Can deactivate global rule when profile rules exist"
    }

    return _run_test_class("TestCommissionRuleDeactivationRestrictions", tests, "test_commission_rules_business_logic")

def run_tc_r_011():
    """Run TC-R-011 tests with readable output"""
    print("🧪 StretchUp API - TC-R-011 Test Results")
    print("=" * 60)
    print("📋 Test Case: Rule Priority Setting")
    print("🎯 Requirement: Verify rule priority can be set (at least 1)")
    print("=" * 60)
    print()

    # Test descriptions
    tests = {
        "test_rule_creation_with_valid_priority": "Rules with valid priority values (1+)",
        "test_rule_creation_rejects_zero_priority": "Rules with zero priority are rejected",
        "test_default_priority_assignment": "Rules get default priority when not specified"
    }

    return _run_test_class("TestCommissionRulePriority", tests, "test_commission_rules_advanced")

def run_tc_r_012():
    """Run TC-R-012 tests with readable output"""
    print("🧪 StretchUp API - TC-R-012 Test Results")
    print("=" * 60)
    print("📋 Test Case: Highest Priority Rule Application")
    print("🎯 Requirement: Verify highest priority rule is applied when multiple rules match")
    print("=" * 60)
    print()

    # Test descriptions
    tests = {
        "test_highest_priority_rule_selected": "Highest priority rule selected when multiple match",
        "test_priority_selection_with_global_and_specific_rules": "Priority selection between global and specific rules",
        "test_priority_selection_ignores_inactive_rules": "Inactive rules ignored in priority selection"
    }

    return _run_test_class("TestCommissionRulePriorityApplication", tests, "test_commission_rules_business_logic")

def run_tc_r_013():
    """Run TC-R-013 tests with readable output"""
    print("🧪 StretchUp API - TC-R-013 Test Results")
    print("=" * 60)
    print("📋 Test Case: Same Priority Rule Selection")
    print("🎯 Requirement: Verify most recently created rule is applied when multiple rules have same priority")
    print("=" * 60)
    print()

    # Test descriptions
    tests = {
        "test_most_recent_rule_selected_for_same_priority": "Most recent rule selected for same priority",
        "test_same_priority_selection_with_different_rule_types": "Same priority selection across different rule types",
        "test_creation_timestamp_used_for_tie_breaking": "Creation timestamp used for tie-breaking"
    }

    return _run_test_class("TestCommissionRuleSamePrioritySelection", tests, "test_commission_rules_business_logic")

def run_tc_r_014():
    """Run TC-R-014: Rule Assignment tests"""
    tests = {
        "test_rule_assigned_to_specific_profile_only_applies_to_that_profile": "Rule assigned to specific profile only applies to that profile",
        "test_multiple_rules_can_be_assigned_to_same_profile": "Multiple rules can be assigned to same profile",
        "test_owner_can_assign_rule_to_any_profile": "Owner can assign rule to any profile",
        "test_rule_assignment_validation_with_invalid_profile": "Rule assignment validation with invalid profile"
    }

    return _run_test_class("TestCommissionRuleAssignment", tests, "test_commission_rules_business_logic")

def run_commission_tests():
    """Run all commission tests with readable output"""
    print("🧪 StretchUp API - Commission Tests")
    print("=" * 60)
    print("📋 Running all commission-related tests...")
    print("=" * 60)
    print()

    # All commission test descriptions (profiles and rules)
    all_tests = {
        # TC-P-001 tests
        "test_owner_can_create_commission_profile": "✅ TC-P-001: Owner can create commission profiles",
        "test_receptionist_cannot_create_commission_profile": "✅ TC-P-001: Receptionist cannot create commission profiles",
        "test_therapist_cannot_create_commission_profile": "✅ TC-P-001: Therapist cannot create commission profiles",
        "test_customer_cannot_create_commission_profile": "✅ TC-P-001: Customer cannot create commission profiles",
        "test_unauthenticated_user_cannot_create_commission_profile": "✅ TC-P-001: Unauthenticated user cannot create commission profiles",

        # TC-P-002 tests
        "test_owner_can_update_commission_profile": "✅ TC-P-002: Owner can update commission profiles (PATCH)",
        "test_receptionist_cannot_update_commission_profile": "✅ TC-P-002: Receptionist cannot access commission profiles (404)",
        "test_therapist_cannot_update_commission_profile": "✅ TC-P-002: Therapist cannot update commission profiles",
        "test_customer_cannot_update_commission_profile": "✅ TC-P-002: Customer cannot access commission profiles (404)",
        "test_unauthenticated_user_cannot_update_commission_profile": "✅ TC-P-002: Unauthenticated user cannot update commission profiles",
        "test_owner_can_full_update_commission_profile": "✅ TC-P-002: Owner can update commission profiles (PUT)",

        # TC-P-003 tests
        "test_owner_can_delete_commission_profile": "✅ TC-P-003: Owner can delete commission profiles",
        "test_receptionist_cannot_delete_commission_profile": "✅ TC-P-003: Receptionist cannot access commission profiles (404)",
        "test_therapist_cannot_delete_commission_profile": "✅ TC-P-003: Therapist cannot delete commission profiles",
        "test_customer_cannot_delete_commission_profile": "✅ TC-P-003: Customer cannot access commission profiles (404)",
        "test_unauthenticated_user_cannot_delete_commission_profile": "✅ TC-P-003: Unauthenticated user cannot delete commission profiles",

        # TC-P-004 tests
        "test_owner_can_list_all_commission_profiles": "✅ TC-P-004: Owner can list all commission profiles",
        "test_receptionist_cannot_list_commission_profiles": "✅ TC-P-004: Receptionist cannot list commission profiles",
        "test_therapist_can_only_see_own_commission_profiles": "✅ TC-P-004: Therapist can only see their own commission profiles",
        "test_customer_cannot_list_commission_profiles": "✅ TC-P-004: Customer cannot list commission profiles",
        "test_unauthenticated_user_cannot_list_commission_profiles": "✅ TC-P-004: Unauthenticated user cannot list commission profiles",
        "test_owner_can_retrieve_specific_commission_profile": "✅ TC-P-004: Owner can retrieve specific commission profiles",
        "test_receptionist_cannot_retrieve_specific_commission_profile": "✅ TC-P-004: Receptionist cannot retrieve specific commission profiles",

        # TC-P-005 tests
        "test_owner_can_deactivate_commission_profile": "✅ TC-P-005: Owner can deactivate commission profiles",
        "test_owner_can_activate_commission_profile": "✅ TC-P-005: Owner can activate commission profiles",
        "test_receptionist_cannot_deactivate_commission_profile": "✅ TC-P-005: Receptionist cannot deactivate commission profiles (403)",
        "test_receptionist_cannot_activate_commission_profile": "✅ TC-P-005: Receptionist cannot activate commission profiles (403)",
        "test_therapist_cannot_deactivate_commission_profile": "✅ TC-P-005: Therapist cannot deactivate commission profiles (even their own)",
        "test_therapist_cannot_activate_commission_profile": "✅ TC-P-005: Therapist cannot activate commission profiles (even their own)",
        "test_customer_cannot_deactivate_commission_profile": "✅ TC-P-005: Customer cannot deactivate commission profiles (403)",
        "test_customer_cannot_activate_commission_profile": "✅ TC-P-005: Customer cannot activate commission profiles (403)",
        "test_unauthenticated_user_cannot_deactivate_commission_profile": "✅ TC-P-005: Unauthenticated user cannot deactivate commission profiles",
        "test_unauthenticated_user_cannot_activate_commission_profile": "✅ TC-P-005: Unauthenticated user cannot activate commission profiles",

        # TC-P-006 tests
        "test_owner_sees_only_active_profiles_by_default": "✅ TC-P-006: Owner sees only active profiles by default",
        "test_owner_can_see_all_profiles_with_include_inactive_parameter": "✅ TC-P-006: Owner can see all profiles with include_inactive parameter",
        "test_therapist_sees_only_own_active_profiles_by_default": "✅ TC-P-006: Therapist sees only own active profiles by default",
        "test_therapist_can_see_own_inactive_profiles_with_include_inactive_parameter": "✅ TC-P-006: Therapist can see own inactive profiles with include_inactive parameter",
        "test_receptionist_sees_no_profiles_regardless_of_status": "✅ TC-P-006: Receptionist sees no profiles regardless of status",
        "test_customer_sees_no_profiles_regardless_of_status": "✅ TC-P-006: Customer sees no profiles regardless of status",
        "test_unauthenticated_user_cannot_see_any_profiles": "✅ TC-P-006: Unauthenticated user cannot see any profiles",
        "test_profile_becomes_invisible_after_deactivation": "✅ TC-P-006: Profile becomes invisible after deactivation",
        "test_profile_becomes_visible_after_activation": "✅ TC-P-006: Profile becomes visible after activation",

        # TC-P-007 tests
        "test_create_first_default_profile_no_previous_default": "✅ TC-P-007: Create first default profile (no previous default)",
        "test_create_second_default_profile_copies_rules_and_unsets_previous": "✅ TC-P-007: Create second default profile (copies rules and unsets previous)",
        "test_create_regular_profile_does_not_affect_default": "✅ TC-P-007: Create regular profile (does not affect default)",
        "test_only_owner_can_create_default_profiles": "✅ TC-P-007: Only owner can create default profiles",
        "test_receptionist_cannot_create_default_profiles": "✅ TC-P-007: Receptionist cannot create default profiles",
        "test_cross_therapist_defaults_are_independent": "✅ TC-P-007: Cross-therapist defaults are independent",
        "test_transaction_rollback_on_rule_copy_failure": "✅ TC-P-007: Transaction rollback on rule copy failure",
        "test_empty_rules_copying_handles_gracefully": "✅ TC-P-007: Empty rules copying handles gracefully",
        "test_multiple_rules_with_different_types_copied_correctly": "✅ TC-P-007: Multiple rules with different types copied correctly",

        # TC-P-008 tests
        "test_owner_cannot_deactivate_active_default_profile": "✅ TC-P-008: Owner cannot deactivate active default profiles",
        "test_owner_cannot_deactivate_inactive_default_profile": "✅ TC-P-008: Owner cannot deactivate inactive default profiles",
        "test_owner_can_deactivate_active_non_default_profile": "✅ TC-P-008: Owner can deactivate active non-default profiles",
        "test_owner_cannot_activate_inactive_default_profile_if_another_default_exists": "✅ TC-P-008: Owner cannot activate inactive default if another default exists",
        "test_receptionist_cannot_deactivate_any_profile_including_default": "✅ TC-P-008: Receptionist cannot deactivate any profiles (including default)",
        "test_therapist_cannot_deactivate_own_default_profile": "✅ TC-P-008: Therapist cannot deactivate their own default profile",
        "test_customer_cannot_deactivate_any_profile": "✅ TC-P-008: Customer cannot deactivate any profiles",
        "test_unauthenticated_user_cannot_deactivate_any_profile": "✅ TC-P-008: Unauthenticated user cannot deactivate any profiles",

        # TC-P-009 tests
        "test_owner_can_deactivate_non_default_profile_when_therapist_has_multiple_active_profiles": "✅ TC-P-009: Owner can deactivate non-default profile when therapist has multiple active profiles",
        "test_owner_cannot_deactivate_default_profile_of_active_therapist": "✅ TC-P-009: Owner cannot deactivate default profile of active therapist (TC-P-008 + TC-P-009)",
        "test_owner_can_deactivate_profile_of_inactive_therapist": "✅ TC-P-009: Owner can deactivate profile when therapist is inactive",
        "test_owner_can_deactivate_already_inactive_profile_of_active_therapist": "✅ TC-P-009: Attempting to deactivate already inactive profile gives appropriate error",
        "test_owner_cannot_deactivate_only_active_profile_of_active_therapist": "✅ TC-P-009: Owner cannot deactivate the only active profile of an active therapist",
        "test_therapist_assignment_check_with_multiple_profiles": "✅ TC-P-009: System correctly identifies therapist assignment with multiple profiles and allows deactivation",
        "test_receptionist_cannot_deactivate_assigned_profile": "✅ TC-P-009: Receptionist cannot deactivate assigned profiles (permission check)",
        "test_therapist_cannot_deactivate_own_assigned_profile": "✅ TC-P-009: Therapist cannot deactivate their own assigned profiles",
        "test_unauthenticated_user_cannot_deactivate_assigned_profile": "✅ TC-P-009: Unauthenticated user cannot deactivate assigned profiles",

        # TC-P-010 tests
        "test_owner_can_assign_inactive_profile_to_therapist": "✅ TC-P-010: Owner can assign inactive profiles to therapists",
        "test_owner_can_reassign_profile_to_different_therapist": "✅ TC-P-010: Owner can reassign profiles to different therapists",
        "test_receptionist_cannot_assign_profiles": "✅ TC-P-010: Receptionist cannot assign profiles",
        "test_therapist_cannot_assign_profiles": "✅ TC-P-010: Therapist cannot assign profiles",
        "test_customer_cannot_assign_profiles": "✅ TC-P-010: Customer cannot assign profiles",
        "test_unauthenticated_user_cannot_assign_profiles": "✅ TC-P-010: Unauthenticated user cannot assign profiles",
        "test_assign_profile_with_missing_therapist_id": "✅ TC-P-010: Assignment fails when therapist_id is missing",
        "test_assign_profile_with_invalid_therapist_id": "✅ TC-P-010: Assignment fails when therapist_id is invalid",
        "test_reassign_profile_to_same_therapist": "✅ TC-P-010: Reassignment fails when trying to assign to same therapist",

        # TC-P-011 tests
        "test_owner_cannot_assign_active_profile_to_therapist_with_active_profile": "✅ TC-P-011: Owner cannot assign active profile to therapist with existing active profile",
        "test_owner_can_assign_inactive_profile_to_therapist_with_active_profile": "✅ TC-P-011: Owner can assign inactive profile to therapist with existing active profile",

        # TC-P-012 tests
        "test_default_profile_created_for_new_therapist_via_management_command": "✅ TC-P-012: Default profile created for new therapist via management command",
        "test_only_one_default_profile_per_therapist": "✅ TC-P-012: Only one default profile per therapist",

        # TC-R-001 tests
        "test_owner_can_create_commission_rule": "✅ TC-R-001: Owner can create commission rules",
        "test_receptionist_cannot_create_commission_rule": "✅ TC-R-001: Receptionist cannot create commission rules",
        "test_therapist_cannot_create_commission_rule": "✅ TC-R-001: Therapist cannot create commission rules",
        "test_customer_cannot_create_commission_rule": "✅ TC-R-001: Customer cannot create commission rules",
        "test_unauthenticated_user_cannot_create_commission_rule": "✅ TC-R-001: Unauthenticated user cannot create commission rules",

        # TC-R-002 tests
        "test_owner_can_update_commission_rule": "✅ TC-R-002: Owner can update commission rules (PATCH)",
        "test_owner_can_full_update_commission_rule": "✅ TC-R-002: Owner can update commission rules (PUT)",
        "test_receptionist_cannot_update_commission_rule": "✅ TC-R-002: Receptionist cannot update commission rules (404)",
        "test_therapist_cannot_update_commission_rule": "✅ TC-R-002: Therapist cannot update commission rules",
        "test_customer_cannot_update_commission_rule": "✅ TC-R-002: Customer cannot update commission rules",
        "test_unauthenticated_user_cannot_update_commission_rule": "✅ TC-R-002: Unauthenticated user cannot update commission rules",

        # TC-R-003 tests
        "test_owner_can_delete_commission_rule": "✅ TC-R-003: Owner can delete commission rules",
        "test_receptionist_cannot_delete_commission_rule": "✅ TC-R-003: Receptionist cannot delete commission rules (404)",
        "test_therapist_cannot_delete_commission_rule": "✅ TC-R-003: Therapist cannot delete commission rules",
        "test_customer_cannot_delete_commission_rule": "✅ TC-R-003: Customer cannot delete commission rules",
        "test_unauthenticated_user_cannot_delete_commission_rule": "✅ TC-R-003: Unauthenticated user cannot delete commission rules",

        # TC-R-004 tests
        "test_owner_can_list_all_commission_rules": "✅ TC-R-004: Owner can list all commission rules",
        "test_receptionist_cannot_list_commission_rules": "✅ TC-R-004: Receptionist cannot list commission rules",
        "test_therapist_can_only_see_own_commission_rules": "✅ TC-R-004: Therapist can only see own commission rules",
        "test_customer_cannot_list_commission_rules": "✅ TC-R-004: Customer cannot list commission rules",
        "test_unauthenticated_user_cannot_list_commission_rules": "✅ TC-R-004: Unauthenticated user cannot list commission rules",
        "test_owner_can_retrieve_specific_commission_rule": "✅ TC-R-004: Owner can retrieve specific commission rules",
        "test_receptionist_cannot_retrieve_specific_commission_rule": "✅ TC-R-004: Receptionist cannot retrieve specific commission rules",

        # TC-R-005 tests
        "test_owner_can_create_percentage_based_rule": "✅ TC-R-005: Owner can create percentage-based rules",
        "test_percentage_rule_validation": "✅ TC-R-005: Percentage values are validated correctly",
        "test_percentage_rule_with_service_type": "✅ TC-R-005: Percentage-based service-specific rules",
        "test_percentage_rule_with_package_type": "✅ TC-R-005: Percentage-based package-specific rules",

        # TC-R-006 tests
        "test_owner_can_create_fixed_amount_rule": "✅ TC-R-006: Owner can create fixed amount rules",
        "test_fixed_amount_rule_validation": "✅ TC-R-006: Fixed amount values are validated correctly",
        "test_fixed_amount_rule_with_service_type": "✅ TC-R-006: Fixed amount service-specific rules",

        # TC-R-007 tests
        "test_rule_creation_with_valid_min_sessions": "✅ TC-R-007: Rules with valid minimum session thresholds (15+)",
        "test_rule_creation_rejects_low_min_sessions": "✅ TC-R-007: Rules with less than 15 minimum sessions are rejected",
        "test_rule_creation_accepts_zero_min_sessions_for_special_cases": "✅ TC-R-007: Zero minimum sessions for special cases",

        # TC-R-008 tests
        "test_rule_not_applied_below_threshold": "✅ TC-R-008: Rules not applied when sessions below threshold",
        "test_rule_applied_at_threshold": "✅ TC-R-008: Rules applied when sessions meet threshold",
        "test_rule_applied_above_threshold": "✅ TC-R-008: Rules applied when sessions exceed threshold",
        "test_threshold_check_with_multiple_rules": "✅ TC-R-008: Threshold checking with multiple rules",

        # TC-R-009 tests
        "test_owner_can_deactivate_commission_rule": "✅ TC-R-009: Owner can deactivate commission rules",
        "test_owner_can_activate_commission_rule": "✅ TC-R-009: Owner can activate commission rules",
        "test_receptionist_cannot_activate_commission_rule": "✅ TC-R-009: Receptionist cannot activate commission rules (404)",
        "test_receptionist_cannot_deactivate_commission_rule": "✅ TC-R-009: Receptionist cannot deactivate commission rules (404)",

        # TC-R-010 tests
        "test_cannot_deactivate_only_rule_for_profile": "✅ TC-R-010: Cannot deactivate only rule for profile",
        "test_can_deactivate_rule_when_other_active_rules_exist": "✅ TC-R-010: Can deactivate when other active rules exist",
        "test_can_deactivate_global_rule_when_profile_rules_exist": "✅ TC-R-010: Can deactivate global rule when profile rules exist",

        # TC-R-011 tests
        "test_rule_creation_with_valid_priority": "✅ TC-R-011: Rules with valid priority values (1+)",
        "test_rule_creation_rejects_zero_priority": "✅ TC-R-011: Rules with zero priority are rejected",
        "test_default_priority_assignment": "✅ TC-R-011: Rules get default priority when not specified",

        # TC-R-012 tests
        "test_highest_priority_rule_selected": "✅ TC-R-012: Highest priority rule selected when multiple match",
        "test_priority_selection_with_global_and_specific_rules": "✅ TC-R-012: Priority selection between global and specific rules",
        "test_priority_selection_ignores_inactive_rules": "✅ TC-R-012: Inactive rules ignored in priority selection",

        # TC-R-013 tests
        "test_most_recent_rule_selected_for_same_priority": "✅ TC-R-013: Most recent rule selected for same priority",
        "test_same_priority_selection_with_different_rule_types": "✅ TC-R-013: Same priority selection across different rule types",
        "test_creation_timestamp_used_for_tie_breaking": "✅ TC-R-013: Creation timestamp used for tie-breaking",

        # TC-R-014 tests
        "test_rule_assigned_to_specific_profile_only_applies_to_that_profile": "✅ TC-R-014: Rule assigned to specific profile only applies to that profile",
        "test_multiple_rules_can_be_assigned_to_same_profile": "✅ TC-R-014: Multiple rules can be assigned to same profile",
        "test_owner_can_assign_rule_to_any_profile": "✅ TC-R-014: Owner can assign rule to any profile",
        "test_rule_assignment_validation_with_invalid_profile": "✅ TC-R-014: Rule assignment validation with invalid profile"
    }

    try:
        cmd = [
            "docker-compose", "exec", "web", "pytest",
            "api/commissions/tests/", "-v", "--tb=short", "--no-header"
        ]

        result = subprocess.run(cmd, capture_output=True, text=True, timeout=300)

        # Parse the output
        lines = result.stdout.split('\n')

        passed_count = 0
        failed_count = 0

        print("📋 Test Results:")
        print("-" * 40)

        for line in lines:
            for test_name, description in all_tests.items():
                if test_name in line:
                    if "PASSED" in line:
                        print(f"✅ PASSED: {description.replace('✅ ', '')}")
                        passed_count += 1
                    elif "FAILED" in line:
                        print(f"❌ FAILED: {description.replace('✅ ', '')}")
                        failed_count += 1
                    break

        # Show any errors in a user-friendly way
        if failed_count > 0:
            print("\n🔍 Failed Test Details:")
            print("-" * 40)

            # Extract specific error information
            failed_tests = []
            for line in lines:
                for test_name, description in all_tests.items():
                    if test_name in line and "FAILED" in line:
                        failed_tests.append((test_name, description.replace('✅ ', '')))
                        break

            for test_name, description in failed_tests:
                print(f"❌ {description}")

                # Extract readable error message
                error_msg = extract_readable_error(lines, test_name)
                print(f"   💡 Issue: {error_msg}")
                print()

        print()
        print("=" * 60)
        print(f"📊 Summary: {passed_count} passed, {failed_count} failed")

        if failed_count == 0:
            print("🎉 All commission tests passed! Your commission system is working correctly.")
        else:
            print("⚠️  Some tests failed. Please check the implementation.")

        print("=" * 60)

        return result.returncode == 0

    except Exception as e:
        print(f"❌ Error running tests: {e}")
        return False

if __name__ == "__main__":
    if len(sys.argv) > 1:
        if sys.argv[1] == "tc-p-001":
            run_tc_p_001()
        elif sys.argv[1] == "tc-p-002":
            run_tc_p_002()
        elif sys.argv[1] == "tc-p-003":
            run_tc_p_003()
        elif sys.argv[1] == "tc-p-004":
            run_tc_p_004()
        elif sys.argv[1] == "tc-p-005":
            run_tc_p_005()
        elif sys.argv[1] == "tc-p-006":
            run_tc_p_006()
        elif sys.argv[1] == "tc-p-007":
            run_tc_p_007()
        elif sys.argv[1] == "tc-p-008":
            run_tc_p_008()
        elif sys.argv[1] == "tc-p-009":
            run_tc_p_009()
        elif sys.argv[1] == "tc-p-010":
            run_tc_p_010()
        elif sys.argv[1] == "tc-p-011":
            run_tc_p_011()
        elif sys.argv[1] == "tc-p-012":
            run_tc_p_012()
        elif sys.argv[1] == "tc-r-001":
            run_tc_r_001()
        elif sys.argv[1] == "tc-r-002":
            run_tc_r_002()
        elif sys.argv[1] == "tc-r-003":
            run_tc_r_003()
        elif sys.argv[1] == "tc-r-004":
            run_tc_r_004()
        elif sys.argv[1] == "tc-r-005":
            run_tc_r_005()
        elif sys.argv[1] == "tc-r-006":
            run_tc_r_006()
        elif sys.argv[1] == "tc-r-007":
            run_tc_r_007()
        elif sys.argv[1] == "tc-r-008":
            run_tc_r_008()
        elif sys.argv[1] == "tc-r-009":
            run_tc_r_009()
        elif sys.argv[1] == "tc-r-010":
            run_tc_r_010()
        elif sys.argv[1] == "tc-r-011":
            run_tc_r_011()
        elif sys.argv[1] == "tc-r-012":
            run_tc_r_012()
        elif sys.argv[1] == "tc-r-013":
            run_tc_r_013()
        elif sys.argv[1] == "tc-r-014":
            run_tc_r_014()
        elif sys.argv[1] == "commission":
            run_commission_tests()
        else:
            print("Usage: python test_formatter.py [tc-p-001|tc-p-002|tc-p-003|tc-p-004|tc-p-005|tc-p-006|tc-p-007|tc-p-008|tc-p-009|tc-p-010|tc-p-011|tc-p-012|tc-r-001|tc-r-002|tc-r-003|tc-r-004|tc-r-005|tc-r-006|tc-r-007|tc-r-008|tc-r-009|tc-r-010|tc-r-011|tc-r-012|tc-r-013|tc-r-014|commission]")
    else:
        run_tc_p_001()
