from django.contrib import admin
from django.urls import path, include
from django.http import HttpResponse

from django.conf import settings
from django.conf.urls.static import static


def home(request):
    return HttpResponse("Hello, world!")


urlpatterns = [
    path("", home, name="home"),
    path("admin/", admin.site.urls),
    path("auth/", include("api.authentication.urls")),
    path("service/", include("api.services.urls")),
    path("staff/", include("api.staff.urls")),
    path("appointment/", include("api.appointments.urls")),
    path("retail/", include("api.retail.urls")),
    path("commission/", include("api.commissions.urls")),
]

# Add this conditional block at the end of the file
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
