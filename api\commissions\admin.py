from django.contrib import admin
from .models import (
    CommissionProfile, CommissionRule, ManualCommission,
    CommissionEarning, TherapistStats, TherapistYearStats, TherapistMonthStats
)


@admin.register(CommissionProfile)
class CommissionProfileAdmin(admin.ModelAdmin):
    list_display = ('name', 'therapist', 'base_percentage', 'is_active', 'is_default')
    list_filter = ('is_active', 'is_default')
    search_fields = ('name', 'therapist__user__first_name', 'therapist__user__last_name')


@admin.register(CommissionRule)
class CommissionRuleAdmin(admin.ModelAdmin):
    list_display = ('name', 'profile', 'rule_type', 'percentage', 'fixed_amount', 'priority', 'is_active')
    list_filter = ('rule_type', 'is_active')
    search_fields = ('name', 'profile__name')


@admin.register(ManualCommission)
class ManualCommissionAdmin(admin.ModelAdmin):
    list_display = ('therapist', 'sale', 'amount', 'percentage', 'created_by', 'created_at')
    list_filter = ('created_at',)
    search_fields = ('therapist__user__first_name', 'therapist__user__last_name', 'notes')


@admin.register(CommissionEarning)
class CommissionEarningAdmin(admin.ModelAdmin):
    list_display = ('therapist', 'sale', 'amount', 'date_earned', 'is_eligible', 'is_paid', 'payment_date', 'month_stat')
    list_filter = ('is_eligible', 'is_paid', 'date_earned')
    search_fields = ('therapist__user__first_name', 'therapist__user__last_name', 'notes')


@admin.register(TherapistStats)
class TherapistStatsAdmin(admin.ModelAdmin):
    list_display = ('therapist', 'total_sessions', 'total_earnings', 'last_updated')
    search_fields = ('therapist__user__first_name', 'therapist__user__last_name')


@admin.register(TherapistYearStats)
class TherapistYearStatsAdmin(admin.ModelAdmin):
    list_display = ('therapist', 'year', 'total_sessions', 'total_earnings', 'last_updated')
    list_filter = ('year',)
    search_fields = ('therapist__user__first_name', 'therapist__user__last_name')


@admin.register(TherapistMonthStats)
class TherapistMonthStatsAdmin(admin.ModelAdmin):
    list_display = ('therapist', 'year', 'month', 'month_name', 'total_sessions', 'total_earnings', 'is_paid', 'payment_date', 'last_updated')
    list_filter = ('year', 'month', 'is_paid')
    search_fields = ('therapist__user__first_name', 'therapist__user__last_name')
