import datetime
from django.core.management.base import BaseCommand
from django.utils import timezone
from decimal import Decimal
from api.appointments.models import Discount


class Command(BaseCommand):
    help = "Create four discount codes."

    def handle(self, *args, **kwargs):
        now = timezone.now()
        valid_from = now
        valid_until = now + datetime.timedelta(days=30)

        discounts = [
            {
                "name": "Summer Special",
                "code": "SUMMER20",
                "discount_percentage": Decimal("20.00"),
                "description": "20% off summer special discount.",
                "valid_from": valid_from,
                "valid_until": valid_until,
            },
            {
                "name": "Winter Warm-Up",
                "code": "WINTER10",
                "discount_percentage": Decimal("10.00"),
                "description": "10% off winter warm-up discount.",
                "valid_from": valid_from,
                "valid_until": valid_until,
            },
            {
                "name": "Spring Savings",
                "code": "SPRING15",
                "discount_percentage": Decimal("15.00"),
                "description": "15% off spring savings discount.",
                "valid_from": valid_from,
                "valid_until": valid_until,
            },
            {
                "name": "Fall Fest",
                "code": "FALL25",
                "discount_percentage": Decimal("25.00"),
                "description": "25% off fall fest discount.",
                "valid_from": valid_from,
                "valid_until": valid_until,
            },
        ]

        for discount_data in discounts:
            discount, created = Discount.objects.get_or_create(
                code=discount_data["code"],
                defaults=discount_data,
            )
            if created:
                self.stdout.write(
                    self.style.SUCCESS(f"Created discount code: {discount.code}")
                )
            else:
                self.stdout.write(f"Discount code {discount.code} already exists.")
