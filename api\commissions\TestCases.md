# 🏆 Commission System Test Cases

## 📋 **Test Case Status Overview**

| Category | Implemented | Total | Progress |
|----------|-------------|-------|----------|
| **Commission Profiles** | 12/12 | 12 | 🟢 100% |
| **Commission Rules** | 14/17 | 17 | 🟡 82% |
| **Manual Commissions** | 0/11 | 11 | 🔴 0% |
| **Commission Earnings** | 0/13 | 13 | 🔴 0% |
| **Therapist Statistics** | 0/8 | 8 | 🔴 0% |
| **Owner Earnings** | 0/7 | 7 | 🔴 0% |
| **Integration Tests** | 0/10 | 10 | 🔴 0% |
| **TOTAL** | **26/78** | **78** | **🟡 33%** |

---

## 1. 👥 **Commission Profiles** `[12/12 - 100% Complete]`

### 1.1 🔐 **Access Control** `[4/4 Complete]`
- ✅ **TC-P-001**: Verify only owner can create commission profiles
- ✅ **TC-P-002**: Verify only owner can update commission profiles
- ✅ **TC-P-003**: Verify only owner can delete commission profiles
- ✅ **TC-P-004**: Verify only owner can list all commission profiles

### 1.2 ⚡ **Profile Status Management** `[5/5 Complete]`
- ✅ **TC-P-005**: Verify only owner can activate/deactivate profiles
- ✅ **TC-P-006**: Verify deactivated profiles are not visible in rule creation page (get all profiles should only return active profiles)
- ✅ **TC-P-007**: Verify only one profile can be set as default at a time
- ✅ **TC-P-008**: Verify active and default profiles cannot have status changed
- ✅ **TC-P-009**: Verify profiles with assigned therapists cannot be deactivated until therapists are reassigned

### 1.3 👨‍⚕️ **Profile Assignment** `[3/3 Complete]`
- ✅ **TC-P-010**: Verify only owner can assign profiles to therapists
- ✅ **TC-P-011**: Verify therapist can be assigned only one active profile
- ✅ **TC-P-012**: Verify default profile is automatically assigned to new therapists (update python manage.py create-Therapist application to create and assign default commission profile)

## 2. ⚙️ **Commission Rules** `[14/17 - 82% Complete]`

### 2.1 🔐 **Access Control** `[4/4 Complete]`
- ✅ **TC-R-001**: Verify only owner can create commission rules
- ✅ **TC-R-002**: Verify only owner can update commission rules
- ✅ **TC-R-003**: Verify only owner can delete commission rules
- ✅ **TC-R-004**: Verify only owner can list all commission rules

### 2.2 🛠️ **Rule Creation and Configuration** `[4/4 Complete]`
- ✅ **TC-R-005**: Verify rule creation with percentage-based commission
- ✅ **TC-R-006**: Verify rule creation with fixed amount commission
- ✅ **TC-R-007**: Verify minimum session threshold is enforced (minimum 15 sessions)
- ✅ **TC-R-008**: Verify rules are only applied after minimum session threshold is met

### 2.3 ⚡ **Rule Status and Priority** `[5/5 Complete]`
- ✅ **TC-R-009**: Verify rules can be activated/deactivated
- ✅ **TC-R-010**: Verify rules cannot be deactivated if it's the only rule for a profile
- ✅ **TC-R-011**: Verify rule priority can be set (at least 1)
- ✅ **TC-R-012**: Verify highest priority rule is applied when multiple rules match
- ✅ **TC-R-013**: Verify most recently created rule is applied when multiple rules have same priority

### 2.4 🎯 **Rule Assignment** `[1/4 - 25% Complete]`
- ✅ **TC-R-014**: Verify rules can be assigned to specific profiles
- ⏳ **TC-R-015**: Verify global rules apply to all profiles
- ⏳ **TC-R-016**: Verify service-specific rules override global rules
- ⏳ **TC-R-017**: Verify package-specific rules override global rules

---

## 3. 📝 **Manual Commissions** `[0/11 - Future Implementation]`

### 3.1 📊 **Management** `[0/4 Planned]`
- ⏳ **TC-M-001**: Verify manual commissions can be created
- ⏳ **TC-M-002**: Verify manual commissions can be updated before approval
- ⏳ **TC-M-003**: Verify manual commissions can be deleted before approval
- ⏳ **TC-M-004**: Verify manual commissions can be listed

### 3.2 ✅ **Approval Process** `[0/4 Planned]`
- ⏳ **TC-M-005**: Verify manual commissions require approval
- ⏳ **TC-M-006**: Verify only owner can approve/reject manual commissions
- ⏳ **TC-M-007**: Verify approved commissions cannot be modified
- ⏳ **TC-M-008**: Verify rejected commissions can be resubmitted

### 3.3 👨‍⚕️ **Assignment** `[0/3 Planned]`
- ⏳ **TC-M-009**: Verify manual commissions can be assigned to therapists
- ⏳ **TC-M-010**: Verify manual commissions can be linked to specific sales
- ⏳ **TC-M-011**: Verify manual commissions without sales link are properly tracked

---

## 4. 💰 **Commission Earnings** `[0/13 - Future Implementation]`

### 4.1 🧮 **Calculation** `[0/5 Planned]`
- ⏳ **TC-E-001**: Verify commission is calculated correctly for standard services
- ⏳ **TC-E-002**: Verify commission is calculated correctly for packages
- ⏳ **TC-E-003**: Verify commission is calculated correctly for expert services
- ⏳ **TC-E-004**: Verify commission is calculated correctly for home services
- ⏳ **TC-E-005**: Verify commission is calculated correctly for freelancers

### 4.2 ⚖️ **Adjustments** `[0/3 Planned]`
- ⏳ **TC-E-006**: Verify payment method deductions are applied correctly
- ⏳ **TC-E-007**: Verify VAT deductions are applied correctly
- ⏳ **TC-E-008**: Verify manual adjustments are applied correctly

### 4.3 👑 **Owner's Share** `[0/5 Planned]`
- ⏳ **TC-E-009**: Verify owner's share is calculated correctly for expert services (30 AED flat)
- ⏳ **TC-E-010**: Verify owner's share is calculated correctly for expert packages (50 AED flat)
- ⏳ **TC-E-011**: Verify owner's share is calculated correctly for home services (50%)
- ⏳ **TC-E-012**: Verify owner's share is calculated correctly for home packages (40%)
- ⏳ **TC-E-013**: Verify owner receives full commission when owner is the therapist

---

## 5. 📊 **Therapist Statistics** `[0/8 - Future Implementation]`

### 5.1 🔄 **Updates** `[0/4 Planned]`
- ⏳ **TC-S-001**: Verify therapist stats are updated when sale is completed
- ⏳ **TC-S-002**: Verify therapist stats are updated when manual commission is approved
- ⏳ **TC-S-003**: Verify therapist stats are updated correctly for monthly periods
- ⏳ **TC-S-004**: Verify therapist stats are updated correctly for yearly periods

### 5.2 📈 **Retrieval** `[0/4 Planned]`
- ⏳ **TC-S-005**: Verify therapist stats can be retrieved for specific therapist
- ⏳ **TC-S-006**: Verify therapist stats can be retrieved for specific date range
- ⏳ **TC-S-007**: Verify therapist stats include correct session counts
- ⏳ **TC-S-008**: Verify therapist stats include correct earning amounts

---

## 6. 👑 **Owner Earnings** `[0/7 - Future Implementation]`

### 6.1 🧮 **Calculation** `[0/4 Planned]`
- ⏳ **TC-O-001**: Verify owner earnings are calculated correctly for each sale
- ⏳ **TC-O-002**: Verify owner earnings are calculated correctly for expert services
- ⏳ **TC-O-003**: Verify owner earnings are calculated correctly for expert packages
- ⏳ **TC-O-004**: Verify owner earnings are calculated correctly for home services

### 6.2 📊 **Retrieval** `[0/3 Planned]`
- ⏳ **TC-O-005**: Verify owner earnings can be retrieved for specific date range
- ⏳ **TC-O-006**: Verify owner earnings summary can be generated
- ⏳ **TC-O-007**: Verify owner earnings reports include all relevant sales

---

## 7. 🔗 **Integration Tests** `[0/10 - Future Implementation]`

### 7.1 🔄 **End-to-End Flows** `[0/5 Planned]`
- ⏳ **TC-I-001**: Verify complete flow from sale to commission calculation to stats update
- ⏳ **TC-I-002**: Verify complete flow for manual commission approval
- ⏳ **TC-I-003**: Verify commission calculation with multiple applicable rules
- ⏳ **TC-I-004**: Verify commission calculation with freelancer therapist
- ⏳ **TC-I-005**: Verify commission calculation with owner as therapist

### 7.2 🎯 **Edge Cases** `[0/5 Planned]`
- ⏳ **TC-I-006**: Verify handling of zero-value sales
- ⏳ **TC-I-007**: Verify handling of refunded sales
- ⏳ **TC-I-008**: Verify handling of partially refunded sales
- ⏳ **TC-I-009**: Verify handling of sales with multiple services
- ⏳ **TC-I-010**: Verify handling of sales with both services and packages

---

## 📖 **Detailed Test Case Descriptions**

> **Note**: The following sections provide detailed descriptions of child test cases for implemented features.

### TC-P-001: Profile Creation Access Control
**Requirement**: Verify only owner can create commission profiles

**Child Test Cases**:
1. **test_owner_can_create_commission_profile**: Test that owner user can successfully create commission profiles
2. **test_receptionist_cannot_create_commission_profile**: Test that receptionist user cannot create commission profiles
3. **test_therapist_cannot_create_commission_profile**: Test that therapist user cannot create commission profiles
4. **test_customer_cannot_create_commission_profile**: Test that customer user cannot create commission profiles
5. **test_unauthenticated_user_cannot_create_commission_profile**: Test that unauthenticated user cannot create commission profiles

### TC-P-002: Profile Update Access Control
**Requirement**: Verify only owner can update commission profiles

**Child Test Cases**:
1. **test_owner_can_update_commission_profile**: Test that owner user can successfully update commission profiles (PATCH)
2. **test_receptionist_cannot_update_commission_profile**: Test that receptionist user cannot update commission profiles (404)
3. **test_therapist_cannot_update_commission_profile**: Test that therapist user cannot update commission profiles
4. **test_customer_cannot_update_commission_profile**: Test that customer user cannot update commission profiles (404)
5. **test_unauthenticated_user_cannot_update_commission_profile**: Test that unauthenticated user cannot update commission profiles
6. **test_owner_can_full_update_commission_profile**: Test that owner user can perform full updates (PUT) on commission profiles

### TC-P-003: Profile Deletion Access Control
**Requirement**: Verify only owner can delete commission profiles

**Child Test Cases**:
1. **test_owner_can_delete_commission_profile**: Test that owner user can successfully delete commission profiles
2. **test_receptionist_cannot_delete_commission_profile**: Test that receptionist user cannot delete commission profiles (404)
3. **test_therapist_cannot_delete_commission_profile**: Test that therapist user cannot delete commission profiles
4. **test_customer_cannot_delete_commission_profile**: Test that customer user cannot delete commission profiles (404)
5. **test_unauthenticated_user_cannot_delete_commission_profile**: Test that unauthenticated user cannot delete commission profiles

### TC-P-004: Profile Listing and Retrieval Access Control
**Requirement**: Verify only owner can list all commission profiles

**Child Test Cases**:
1. **test_owner_can_list_all_commission_profiles**: Test that owner user can successfully list all commission profiles
2. **test_receptionist_cannot_list_commission_profiles**: Test that receptionist user cannot list commission profiles
3. **test_therapist_can_only_see_own_commission_profiles**: Test that therapist user can only see their own commission profiles
4. **test_customer_cannot_list_commission_profiles**: Test that customer user cannot list commission profiles
5. **test_unauthenticated_user_cannot_list_commission_profiles**: Test that unauthenticated user cannot list commission profiles
6. **test_owner_can_retrieve_specific_commission_profiles**: Test that owner user can retrieve specific commission profiles
7. **test_receptionist_cannot_retrieve_specific_commission_profiles**: Test that receptionist user cannot retrieve specific commission profiles

### TC-P-005: Profile Status Management Access Control
**Requirement**: Verify only owner can activate/deactivate profiles

**Child Test Cases**:
1. **test_owner_can_deactivate_commission_profile**: Test that owner user can successfully deactivate commission profiles
2. **test_owner_can_activate_commission_profile**: Test that owner user can successfully activate commission profiles
3. **test_receptionist_cannot_deactivate_commission_profile**: Test that receptionist user cannot deactivate commission profiles (403)
4. **test_receptionist_cannot_activate_commission_profile**: Test that receptionist user cannot activate commission profiles (403)
5. **test_therapist_cannot_deactivate_commission_profile**: Test that therapist user cannot deactivate commission profiles (even their own)
6. **test_therapist_cannot_activate_commission_profile**: Test that therapist user cannot activate commission profiles (even their own)
7. **test_customer_cannot_deactivate_commission_profile**: Test that customer user cannot deactivate commission profiles (403)
8. **test_customer_cannot_activate_commission_profile**: Test that customer user cannot activate commission profiles (403)
9. **test_unauthenticated_user_cannot_deactivate_commission_profile**: Test that unauthenticated user cannot deactivate commission profiles
10. **test_unauthenticated_user_cannot_activate_commission_profile**: Test that unauthenticated user cannot activate commission profiles

### TC-P-006: Profile Visibility Based on Status
**Requirement**: Verify deactivated profiles are not visible in rule creation page

**Child Test Cases**:
1. **test_owner_sees_only_active_profiles_by_default**: Test that owner user sees only active profiles by default
2. **test_owner_can_see_all_profiles_with_include_inactive_parameter**: Test that owner user can see all profiles (including inactive) when using include_inactive parameter
3. **test_therapist_sees_only_own_active_profiles_by_default**: Test that therapist user sees only their own active profiles by default
4. **test_therapist_can_see_own_inactive_profiles_with_include_inactive_parameter**: Test that therapist user can see their own inactive profiles when using include_inactive parameter
5. **test_receptionist_sees_no_profiles_regardless_of_status**: Test that receptionist user sees no profiles regardless of active status
6. **test_customer_sees_no_profiles_regardless_of_status**: Test that customer user sees no profiles regardless of active status
7. **test_unauthenticated_user_cannot_see_any_profiles**: Test that unauthenticated user cannot see any profiles
8. **test_profile_becomes_invisible_after_deactivation**: Test that a profile becomes invisible after deactivation
9. **test_profile_becomes_visible_after_activation**: Test that a profile becomes visible after activation

### TC-P-007: Default Profile Creation with Business Logic
**Requirement**: Verify default profile creation copies rules and manages assignments

**Child Test Cases**:
1. **test_create_first_default_profile_no_previous_default**: Test creating the first default profile when no previous default exists
2. **test_create_second_default_profile_copies_rules_and_unsets_previous**: Test creating a second default profile copies all rules from previous default and unsets previous
3. **test_create_regular_profile_does_not_affect_default**: Test creating a regular (non-default) profile does not affect existing default
4. **test_only_owner_can_create_default_profiles**: Test that only owners can create default profiles
5. **test_receptionist_cannot_create_default_profiles**: Test that receptionists cannot create default profiles
6. **test_cross_therapist_defaults_are_independent**: Test that default profiles are independent per therapist
7. **test_transaction_rollback_on_rule_copy_failure**: Test that transaction rolls back if rule copying fails
8. **test_empty_rules_copying_handles_gracefully**: Test creating default profile when previous default has no rules
9. **test_multiple_rules_with_different_types_copied_correctly**: Test that all rule types are copied correctly with all their attributes

### TC-P-008: Default Profile Status Restrictions
**Requirement**: Verify active and default profiles cannot have status changed

**Child Test Cases**:
1. **test_owner_cannot_deactivate_active_default_profile**: Test that owner cannot deactivate a profile that is both active and default
2. **test_owner_cannot_deactivate_inactive_default_profile**: Test that owner cannot deactivate a profile that is default (even if already inactive)
3. **test_owner_can_deactivate_active_non_default_profile**: Test that owner can still deactivate profiles that are active but not default
4. **test_owner_cannot_activate_inactive_default_if_another_default_exists**: Test that owner cannot activate an inactive default profile if another default already exists
5. **test_receptionist_cannot_deactivate_any_profile_including_default**: Test that receptionist cannot deactivate any profiles (including default ones)
6. **test_therapist_cannot_deactivate_own_default_profile**: Test that therapist cannot deactivate their own default profile
7. **test_customer_cannot_deactivate_any_profile**: Test that customer cannot deactivate any profiles
8. **test_unauthenticated_user_cannot_deactivate_any_profile**: Test that unauthenticated user cannot deactivate any profiles

### TC-P-009: Therapist Assignment Restrictions
**Requirement**: Verify profiles with assigned therapists cannot be deactivated until therapists are reassigned

**Child Test Cases**:
1. **test_owner_can_deactivate_non_default_profile_when_therapist_has_multiple_active_profiles**: Test that owner can deactivate a non-default profile when therapist has multiple active profiles
2. **test_owner_cannot_deactivate_default_profile_of_active_therapist**: Test that owner cannot deactivate a default profile of an active therapist (combines TC-P-008 and TC-P-009)
3. **test_owner_can_deactivate_profile_of_inactive_therapist**: Test that owner can deactivate a profile when the assigned therapist is inactive
4. **test_owner_can_deactivate_already_inactive_profile_of_active_therapist**: Test that attempting to deactivate an already inactive profile gives appropriate error
5. **test_owner_cannot_deactivate_only_active_profile_of_active_therapist**: Test that owner cannot deactivate the only active profile of an active therapist
6. **test_therapist_assignment_check_with_multiple_profiles**: Test that the system correctly identifies when a therapist has multiple profiles and allows deactivation
7. **test_receptionist_cannot_deactivate_assigned_profile**: Test that receptionist cannot deactivate any profiles (permission check comes first)
8. **test_therapist_cannot_deactivate_own_assigned_profile**: Test that therapist cannot deactivate their own assigned profiles
9. **test_unauthenticated_user_cannot_deactivate_assigned_profile**: Test that unauthenticated user cannot deactivate any profiles

### TC-P-010: Profile Assignment Access Control
**Requirement**: Verify only owner can assign profiles to therapists

**Child Test Cases**:
1. **test_owner_can_assign_inactive_profile_to_therapist**: Test that owner can assign inactive profiles to therapists (direct model test)
2. **test_owner_can_reassign_profile_to_different_therapist**: Test that owner can reassign profiles to different therapists (direct model test)
3. **test_receptionist_cannot_assign_profiles**: Test that receptionist cannot assign profiles (access control test)
4. **test_therapist_cannot_assign_profiles**: Test that therapist cannot assign profiles (access control test)
5. **test_customer_cannot_assign_profiles**: Test that customer cannot assign profiles (access control test)
6. **test_unauthenticated_user_cannot_assign_profiles**: Test that unauthenticated user cannot assign profiles (authentication test)
7. **test_assign_profile_with_missing_therapist_id**: Test that assignment fails when therapist_id is missing (business logic test)
8. **test_assign_profile_with_invalid_therapist_id**: Test that assignment fails when therapist_id is invalid (business logic test)
9. **test_reassign_profile_to_same_therapist**: Test that reassignment fails when trying to assign to the same therapist (business logic test)

### TC-P-011: Single Active Profile Constraint
**Requirement**: Verify therapist can be assigned only one active profile

**Child Test Cases**:
1. **test_owner_cannot_assign_active_profile_to_therapist_with_active_profile**: Test that business logic prevents assigning active profile to therapist who already has an active profile
2. **test_owner_can_assign_inactive_profile_to_therapist_with_active_profile**: Test that owner can assign inactive profile to therapist who already has an active profile

### TC-P-012: Automatic Default Profile Assignment
**Requirement**: Verify default profile is automatically assigned to new therapists

**Child Test Cases**:
1. **test_default_profile_created_for_new_therapist_via_management_command**: Test that the management command creates default commission profiles for new therapists
2. **test_only_one_default_profile_per_therapist**: Test that each therapist can have only one default profile

---

## Detailed Test Case Descriptions for Commission Rules

### TC-R-001: Commission Rule Creation Access Control
**Requirement**: Verify only owner can create commission rules

**Child Test Cases**:
1. **test_owner_can_create_commission_rule**: Test that owner user can successfully create commission rules
2. **test_receptionist_cannot_create_commission_rule**: Test that receptionist user cannot create commission rules
3. **test_therapist_cannot_create_commission_rule**: Test that therapist user cannot create commission rules
4. **test_customer_cannot_create_commission_rule**: Test that customer user cannot create commission rules
5. **test_unauthenticated_user_cannot_create_commission_rule**: Test that unauthenticated user cannot create commission rules

### TC-R-002: Commission Rule Update Access Control
**Requirement**: Verify only owner can update commission rules

**Child Test Cases**:
1. **test_owner_can_update_commission_rule**: Test that owner user can successfully update commission rules (PATCH)
2. **test_receptionist_cannot_update_commission_rule**: Test that receptionist user cannot update commission rules (404)
3. **test_therapist_cannot_update_commission_rule**: Test that therapist user cannot update commission rules
4. **test_customer_cannot_update_commission_rule**: Test that customer user cannot update commission rules
5. **test_unauthenticated_user_cannot_update_commission_rule**: Test that unauthenticated user cannot update commission rules
6. **test_owner_can_full_update_commission_rule**: Test that owner user can perform full updates (PUT) on commission rules

### TC-R-003: Commission Rule Deletion Access Control
**Requirement**: Verify only owner can delete commission rules

**Child Test Cases**:
1. **test_owner_can_delete_commission_rule**: Test that owner user can successfully delete commission rules
2. **test_receptionist_cannot_delete_commission_rule**: Test that receptionist user cannot delete commission rules (404)
3. **test_therapist_cannot_delete_commission_rule**: Test that therapist user cannot delete commission rules
4. **test_customer_cannot_delete_commission_rule**: Test that customer user cannot delete commission rules
5. **test_unauthenticated_user_cannot_delete_commission_rule**: Test that unauthenticated user cannot delete commission rules

### TC-R-004: Commission Rule Listing and Retrieval Access Control
**Requirement**: Verify only owner can list all commission rules

**Child Test Cases**:
1. **test_owner_can_list_all_commission_rules**: Test that owner user can successfully list all commission rules
2. **test_receptionist_cannot_list_commission_rules**: Test that receptionist user cannot list commission rules (404)
3. **test_therapist_can_only_see_own_commission_rules**: Test that therapist user can only see rules for their own profiles
4. **test_customer_cannot_list_commission_rules**: Test that customer user cannot list commission rules
5. **test_unauthenticated_user_cannot_list_commission_rules**: Test that unauthenticated user cannot list commission rules
6. **test_owner_can_retrieve_specific_commission_rule**: Test that owner user can retrieve specific commission rules
7. **test_receptionist_cannot_retrieve_specific_commission_rule**: Test that receptionist user cannot retrieve specific commission rules (404)
8. **test_owner_sees_only_active_rules_by_default**: Test that owner sees only active rules by default (matching Commission Profile pattern)
9. **test_owner_can_see_all_rules_with_include_inactive_parameter**: Test that owner can see all rules with include_inactive parameter (matching Commission Profile pattern)
10. **test_therapist_sees_only_own_active_rules_by_default**: Test that therapist sees only own active rules by default (matching Commission Profile pattern)
11. **test_therapist_can_see_own_inactive_rules_with_include_inactive_parameter**: Test that therapist can see own inactive rules with include_inactive parameter (matching Commission Profile pattern)
12. **test_rule_becomes_invisible_after_deactivation**: Test that rule becomes invisible after deactivation (matching Commission Profile pattern)
13. **test_rule_becomes_visible_after_activation**: Test that rule becomes visible after activation (matching Commission Profile pattern)

### TC-R-005: Rule Creation with Percentage-based Commission
**Requirement**: Verify rule creation with percentage-based commission

**Child Test Cases**:
1. **test_owner_can_create_percentage_based_rule**: Test that owner can create rules with percentage commission
2. **test_percentage_rule_validation**: Test that percentage values are validated correctly (0-100)
3. **test_percentage_rule_with_global_type**: Test creating percentage-based global rules
4. **test_percentage_rule_with_service_type**: Test creating percentage-based service-specific rules

### TC-R-006: Rule Creation with Fixed Amount Commission
**Requirement**: Verify rule creation with fixed amount commission

**Child Test Cases**:
1. **test_owner_can_create_fixed_amount_rule**: Test that owner can create rules with fixed amount commission
2. **test_fixed_amount_rule_validation**: Test that fixed amount values are validated correctly (positive numbers)
3. **test_fixed_amount_rule_with_global_type**: Test creating fixed amount global rules

### TC-R-007: Minimum Session Threshold Enforcement
**Requirement**: Verify minimum session threshold is enforced (minimum 15 sessions)

**Child Test Cases**:
1. **test_rule_creation_with_valid_min_sessions**: Test creating rules with valid minimum session thresholds (15+)
2. **test_rule_creation_rejects_low_min_sessions**: Test that rules with less than 15 minimum sessions are rejected
3. **test_rule_creation_accepts_zero_min_sessions_for_special_cases**: Test that zero minimum sessions are allowed for special rule types

### TC-R-008: Rules Applied After Minimum Session Threshold
**Requirement**: Verify rules are only applied after minimum session threshold is met

**Child Test Cases**:
1. **test_rule_not_applied_below_threshold**: Test that rules are not applied when therapist sessions are below threshold
2. **test_rule_applied_at_threshold**: Test that rules are applied when therapist sessions meet the threshold
3. **test_rule_applied_above_threshold**: Test that rules are applied when therapist sessions exceed the threshold
4. **test_threshold_check_with_multiple_rules**: Test threshold checking with multiple rules having different thresholds

### TC-R-009: Rule Activation and Deactivation
**Requirement**: Verify rules can be activated/deactivated

**Child Test Cases**:
1. **test_owner_can_activate_commission_rule**: Test that owner can activate commission rules
2. **test_owner_can_deactivate_commission_rule**: Test that owner can deactivate commission rules
3. **test_receptionist_cannot_activate_commission_rule**: Test that receptionist cannot activate commission rules (404)
4. **test_receptionist_cannot_deactivate_commission_rule**: Test that receptionist cannot deactivate commission rules (404)

### TC-R-010: Rule Deactivation Restrictions
**Requirement**: Verify rules cannot be deactivated if it's the only rule for a profile

**Child Test Cases**:
1. **test_cannot_deactivate_only_rule_for_profile**: Test that the only active rule for a profile cannot be deactivated
2. **test_can_deactivate_rule_when_other_active_rules_exist**: Test that rules can be deactivated when other active rules exist for the profile
3. **test_can_deactivate_global_rule_when_profile_rules_exist**: Test that global rules can be deactivated when profile-specific rules exist

### TC-R-011: Rule Priority Setting
**Requirement**: Verify rule priority can be set (at least 1)

**Child Test Cases**:
1. **test_rule_creation_with_valid_priority**: Test creating rules with valid priority values (1+)
2. **test_rule_creation_rejects_zero_priority**: Test that rules with zero priority are rejected
3. **test_rule_creation_rejects_negative_priority**: Test that rules with negative priority are rejected

### TC-R-012: Highest Priority Rule Application
**Requirement**: Verify highest priority rule is applied when multiple rules match

**Child Test Cases**:
1. **test_highest_priority_rule_selected**: Test that the rule with highest priority is selected when multiple rules match
2. **test_priority_selection_with_global_and_specific_rules**: Test priority selection between global and specific rules
3. **test_priority_selection_ignores_inactive_rules**: Test that inactive rules are ignored in priority selection

### TC-R-013: Same Priority Rule Selection
**Requirement**: Verify most recently created rule is applied when multiple rules have same priority

**Child Test Cases**:
1. **test_most_recent_rule_selected_for_same_priority**: Test that the most recently created rule is selected when priorities are equal
2. **test_same_priority_selection_with_different_rule_types**: Test same priority selection across different rule types
3. **test_creation_timestamp_used_for_tie_breaking**: Test that creation timestamp is used for tie-breaking in rule selection

### TC-R-014: Rule Assignment to Specific Profiles ✅
**Requirement**: Verify rules can be assigned to specific profiles

**Child Test Cases**:
1. **test_rule_assigned_to_specific_profile_only_applies_to_that_profile**: Test that rules assigned to specific profiles are only visible to therapists with those profiles
2. **test_multiple_rules_can_be_assigned_to_same_profile**: Test that multiple rules can be assigned to the same commission profile
3. **test_owner_can_assign_rule_to_any_profile**: Test that owners can assign rules to any profile during creation
4. **test_rule_assignment_validation_with_invalid_profile**: Test validation when assigning rules to non-existent profiles

**Implementation Status**: ✅ **COMPLETE** - All 4 child test cases implemented and passing

## 3. Manual Commissions (Future Implementation)

### 3.1 Management
- **TC-M-001**: Verify manual commissions can be created
- **TC-M-002**: Verify manual commissions can be updated before approval
- **TC-M-003**: Verify manual commissions can be deleted before approval
- **TC-M-004**: Verify manual commissions can be listed

### 3.2 Approval Process
- **TC-M-005**: Verify manual commissions require approval
- **TC-M-006**: Verify only owner can approve/reject manual commissions
- **TC-M-007**: Verify approved commissions cannot be modified
- **TC-M-008**: Verify rejected commissions can be resubmitted

### 3.3 Assignment
- **TC-M-009**: Verify manual commissions can be assigned to therapists
- **TC-M-010**: Verify manual commissions can be linked to specific sales
- **TC-M-011**: Verify manual commissions without sales link are properly tracked

## 4. Commission Earnings (Future Implementation)

### 4.1 Calculation
- **TC-E-001**: Verify commission is calculated correctly for standard services
- **TC-E-002**: Verify commission is calculated correctly for packages
- **TC-E-003**: Verify commission is calculated correctly for expert services
- **TC-E-004**: Verify commission is calculated correctly for home services
- **TC-E-005**: Verify commission is calculated correctly for freelancers

### 4.2 Adjustments
- **TC-E-006**: Verify payment method deductions are applied correctly
- **TC-E-007**: Verify VAT deductions are applied correctly
- **TC-E-008**: Verify manual adjustments are applied correctly

### 4.3 Owner's Share
- **TC-E-009**: Verify owner's share is calculated correctly for expert services (30 AED flat)
- **TC-E-010**: Verify owner's share is calculated correctly for expert packages (50 AED flat)
- **TC-E-011**: Verify owner's share is calculated correctly for home services (50%)
- **TC-E-012**: Verify owner's share is calculated correctly for home packages (40%)
- **TC-E-013**: Verify owner receives full commission when owner is the therapist

## 5. Therapist Statistics (Future Implementation)

### 5.1 Updates
- **TC-S-001**: Verify therapist stats are updated when sale is completed
- **TC-S-002**: Verify therapist stats are updated when manual commission is approved
- **TC-S-003**: Verify therapist stats are updated correctly for monthly periods
- **TC-S-004**: Verify therapist stats are updated correctly for yearly periods

### 5.2 Retrieval
- **TC-S-005**: Verify therapist stats can be retrieved for specific therapist
- **TC-S-006**: Verify therapist stats can be retrieved for specific date range
- **TC-S-007**: Verify therapist stats include correct session counts
- **TC-S-008**: Verify therapist stats include correct earning amounts

## 6. Owner Earnings (Future Implementation)

### 6.1 Calculation
- **TC-O-001**: Verify owner earnings are calculated correctly for each sale
- **TC-O-002**: Verify owner earnings are calculated correctly for expert services
- **TC-O-003**: Verify owner earnings are calculated correctly for expert packages
- **TC-O-004**: Verify owner earnings are calculated correctly for home services

### 6.2 Retrieval
- **TC-O-005**: Verify owner earnings can be retrieved for specific date range
- **TC-O-006**: Verify owner earnings summary can be generated
- **TC-O-007**: Verify owner earnings reports include all relevant sales

## 7. Integration Tests (Future Implementation)

### 7.1 End-to-End Flows
- **TC-I-001**: Verify complete flow from sale to commission calculation to stats update
- **TC-I-002**: Verify complete flow for manual commission approval
- **TC-I-003**: Verify commission calculation with multiple applicable rules
- **TC-I-004**: Verify commission calculation with freelancer therapist
- **TC-I-005**: Verify commission calculation with owner as therapist

### 7.2 Edge Cases
- **TC-I-006**: Verify handling of zero-value sales
- **TC-I-007**: Verify handling of refunded sales
- **TC-I-008**: Verify handling of partially refunded sales
- **TC-I-009**: Verify handling of sales with multiple services
- **TC-I-010**: Verify handling of sales with both services and packages

---

## 📊 **Implementation Progress Summary**

### 🎯 **Current Status**
- **Total Test Cases**: 78 planned
- **Implemented**: 26 test cases (33%)
- **Remaining**: 52 test cases (67%)

### 🏆 **Completed Features**
1. ✅ **Commission Profiles** - Full CRUD, access control, status management, and assignment logic
2. ✅ **Commission Rules (Core)** - CRUD, access control, validation, priority handling, and basic assignment

### 🚧 **In Progress**
- **Commission Rules (Advanced)** - Global rules, service/package-specific rules (3 test cases remaining)

### 📋 **Next Priorities**
1. **TC-R-015**: Global rules functionality
2. **TC-R-016**: Service-specific rule overrides
3. **TC-R-017**: Package-specific rule overrides
4. **Manual Commissions**: Complete management and approval workflow
5. **Commission Earnings**: Calculation engine and business logic

### 🧪 **Test Execution**
- **Command**: `python test_formatter.py commission`
- **Current Results**: 133 tests passing, 0 failing
- **Coverage**: All implemented features have comprehensive test coverage

---

## 📝 **Notes**

- **Test Pattern**: Each test case follows the pattern TC-[Category]-[Number] (e.g., TC-P-001, TC-R-014)
- **Implementation Strategy**: Incremental development with one test case at a time
- **Quality Assurance**: All tests must pass before moving to the next feature
- **Documentation**: This file serves as both specification and progress tracker

---

**Last Updated**: December 2024
**Status**: 26/78 test cases implemented (33% complete)
**Next Target**: TC-R-015 (Global Rules Functionality)
