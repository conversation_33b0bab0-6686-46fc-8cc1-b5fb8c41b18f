from django.contrib.auth import get_user_model

from rest_framework import serializers
from rest_framework_simplejwt.serializers import TokenObtainPairSerializer
from rest_framework import exceptions

from .models import User


class UserProfileSerializer(serializers.ModelSerializer):
    name = serializers.SerializerMethodField()
    rewards = serializers.SerializerMethodField()
    reward_balance = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = [
            "id",
            "email",
            "phone_number",
            "name",
            "first_name",
            "last_name",
            "gender",
            "role",
            "address",
            "date_of_birth",
            "profile_picture",
            "medical_issues",
            "goal",
            "rewards",
            "reward_balance",
            "expo_push_token",  # NEW FIELD ADDED
        ]
        extra_kwargs = {
            "expo_push_token": {
                "write_only": True
            },  # Don't return in responses for security
        }

    def get_name(self, obj):
        """Get the full name of the User"""
        return f"{obj.first_name} {obj.last_name}"

    def get_rewards(self, obj):
        request = self.context.get("request")
        from api.appointments.serializers import (
            CustomerRewardSerializer,
            AdminRewardSerializer,
        )

        if request and request.user:
            if request.user.role in ["owner", "receptionist"]:
                return AdminRewardSerializer(
                    obj.rewards.order_by("-created_at")[:10], many=True
                ).data
            else:
                return CustomerRewardSerializer(
                    obj.rewards.order_by("-created_at")[:10], many=True
                ).data
        return []

    def get_reward_balance(self, obj):
        request = self.context.get("request")
        from api.appointments.models import RewardBalance
        from api.appointments.serializers import (
            RewardBalanceSerializer,
        )

        try:
            rb = obj.reward_balance
        except RewardBalance.DoesNotExist:
            return None
        if request and request.user:
            return RewardBalanceSerializer(rb).data
        return None

    def validate_expo_push_token(self, value):
        """
        Validate that the expo push token has the correct format.
        """
        if value and not value.startswith("ExponentPushToken["):
            raise serializers.ValidationError(
                "Invalid Expo push token format. Token should start with 'ExponentPushToken['"
            )
        return value


class UserProfileAppointmentSerializer(serializers.ModelSerializer):
    name = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = [
            "id",
            "email",
            "phone_number",
            "name",
            "first_name",
            "last_name",
            "gender",
            "role",
            "address",
            "date_of_birth",
            "profile_picture",
            "medical_issues",
            "goal",
        ]

    def get_name(self, obj):
        """Get the full name of the User"""
        return f"{obj.first_name} {obj.last_name}"


class CustomTokenObtainPairSerializer(TokenObtainPairSerializer):
    @classmethod
    def get_token(cls, user):
        token = super().get_token(user)
        token["email"] = user.email
        return token

    def validate(self, attrs):
        email = attrs.get("email", "").lower()
        if email:
            attrs["email"] = email
        else:
            raise exceptions.ValidationError("Email is required for login.")

        return super().validate(attrs)

    @property
    def username_field(self):
        return get_user_model().USERNAME_FIELD
