<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>Daily Sales Report - {{ date }}</title>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css" rel="stylesheet" />
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet" />
  <style>
    body { padding: 10px; font-family: "Roboto", sans-serif; font-size: 0.9rem; }
    .card { margin-bottom: 15px; border-radius: 6px; box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1); }
    .card-title { font-size: 1.2rem; font-weight: 500; color: #37474f; margin-bottom: 10px; }
    .report-header { background-color: #37474f; color: white; padding: 15px; border-radius: 6px; margin-bottom: 15px; }
    .report-header h3 { margin: 5px 0; font-size: 1.5rem; }
    .report-header h5 { margin: 5px 0; font-size: 1.1rem; }
    .stats-box { text-align: center; padding: 8px; border-radius: 4px; background-color: #f5f5f5; margin-bottom: 10px; }
    .stats-box h5 { margin: 0; font-size: 0.9rem; color: #666; }
    .stats-box h4 { margin: 5px 0; font-size: 1.2rem; }
    .positive { color: #4caf50; }
    .negative { color: #f44336; }
    .section-header { 
      background-color: #e3f2fd; 
      padding: 8px 15px; 
      margin-bottom: 15px; 
      border-radius: 4px; 
      font-weight: 500;
      color: #1565c0;
      border-left: 4px solid #1565c0;
    }
    .data-table { font-size: 0.85rem; }
    .data-table th { font-weight: 600; background-color: #f5f5f5; }
    .data-table td { padding: 8px; }
    .summary-row { background-color: #e8f5e9; font-weight: 600; }
    .deduction-row { color: #d32f2f; }
    .total-row { background-color: #1976d2; color: white; font-weight: 600; }
    @media print {
      body { background-color: white; font-size: 9pt; }
      .card { box-shadow: none; margin-bottom: 8px; page-break-inside: avoid; }
      @page { margin: 0.5cm; }
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- Header -->
    <div class="report-header center-align">
      <h3>Daily Sales Report</h3>
      <h5>{{ location_display }} - {{ date }}</h5>
    </div>

    <!-- SECTION 1: DAILY SUMMARY -->
    <div class="section-header">
      <i class="material-icons small left">today</i> DAILY SUMMARY
    </div>
    
    <!-- Sales Summary -->
    <div class="card">
      <div class="card-content">
        <span class="card-title">Daily Sales Overview</span>
        <h6 style="color: #666; margin-top: 0;">Gross Sales Breakdown</h6>
        <table class="data-table striped">
          <thead>
            <tr>
              <th>Category</th>
              <th style="text-align: right;">Amount</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Service Sales</td>
              <td style="text-align: right;">{{ daily.summary.service_sales }}</td>
            </tr>
            <tr>
              <td>Package Sales</td>
              <td style="text-align: right;">{{ daily.summary.package_sales }}</td>
            </tr>
            <tr>
              <td>Product Sales</td>
              <td style="text-align: right;">{{ daily.summary.product_sales }}</td>
            </tr>
            <tr class="summary-row">
              <td><strong>GROSS SALES</strong></td>
              <td style="text-align: right;"><strong>{{ daily.summary.gross_sales }}</strong></td>
            </tr>
          </tbody>
        </table>
        
        <!-- Payment Methods Breakdown -->
        <div style="margin-top: 20px;">
          <h6 style="color: #666; margin-top: 0;">Payment Methods Breakdown</h6>
          <table class="data-table striped">
            <thead>
              <tr>
                <th>Payment Method</th>
                <th style="text-align: right;">Amount</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>Cash</td>
                <td style="text-align: right;">{{ daily.summary.payment_totals.cash }}</td>
              </tr>
              <tr>
                <td>Card</td>
                <td style="text-align: right;">{{ daily.summary.payment_totals.card }}</td>
              </tr>
              <tr>
                <td>Link</td>
                <td style="text-align: right;">{{ daily.summary.payment_totals.link }}</td>
              </tr>
              <tr class="summary-row">
                <td><strong>Total</strong></td>
                <td style="text-align: right;"><strong>{{ daily.summary.gross_sales }}</strong></td>
              </tr>
            </tbody>
          </table>
        </div>
        
        <!-- Net Sales Table -->
        <div style="margin-top: 20px;">
          <h6 style="color: #666; margin-top: 0;">Net Sales Calculation</h6>
          <table class="data-table striped">
            <thead>
              <tr>
                <th>Description</th>
                <th style="text-align: right;">Amount</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>Gross Sales</td>
                <td style="text-align: right;">{{ daily.summary.gross_sales }}</td>
              </tr>
              <tr class="deduction-row">
                <td>Less: VAT (5%)</td>
                <td style="text-align: right;">-{{ daily.summary.deductions.vat }}</td>
              </tr>
              <tr class="deduction-row">
                <td>Less: Card Charges (2.1% of {{ daily.summary.payment_totals.card }})</td>
                <td style="text-align: right;">-{{ daily.summary.deductions.card_charges }}</td>
              </tr>
              <tr class="deduction-row">
                <td>Less: Link Charges (3% of {{ daily.summary.payment_totals.link }})</td>
                <td style="text-align: right;">-{{ daily.summary.deductions.link_charges }}</td>
              </tr>
              <tr class="deduction-row summary-row">
                <td><strong>Total Deductions</strong></td>
                <td style="text-align: right;"><strong>-{{ daily.summary.deductions.total }}</strong></td>
              </tr>
              <tr class="total-row">
                <td><strong>NET SALES</strong></td>
                <td style="text-align: right;"><strong>{{ daily.summary.net_sales }}</strong></td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Expenses & Withdrawals Summary -->
    <div class="row">
      <div class="col s6">
        <div class="card">
          <div class="card-content">
            <span class="card-title">Total Expenses</span>
            <div class="stats-box">
              <h4 class="negative">{{ daily.summary.expenses_total }}</h4>
            </div>
          </div>
        </div>
      </div>
      <div class="col s6">
        <div class="card">
          <div class="card-content">
            <span class="card-title">Total Withdrawals</span>
            <div class="stats-box">
              <h4 class="negative">{{ daily.summary.withdrawals_total }}</h4>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- SECTION 2: DAILY DETAILED ITEMS -->
    <div class="section-header">
      <i class="material-icons small left">list</i> DAILY DETAILED ITEMS
    </div>

    <!-- All Sales -->
    <div class="card">
      <div class="card-content">
        <span class="card-title">Service & Package Sales</span>
        <table class="data-table striped">
          <thead>
            <tr>
              <th>Time</th>
              <th>Type</th>
              <th>Customer</th>
              <th>Item</th>
              <th>Therapist</th>
              <th>Payment</th>
              <th style="text-align: right;">Amount</th>
            </tr>
          </thead>
          <tbody>
            {% for sale in daily.items.sales %}
            <tr>
              <td>{{ sale.created_at_formatted|default:"No date" }}</td>
              <td>{{ sale.sale_type|title }}</td>
              <td>{{ sale.customer_name }}</td>
              <td>
                {% if sale.sale_type == "service" %}
                  {% if sale.services %}
                    {% for service in sale.services %}
                      {{ service.name }} ({{ service.duration }}min){% if not forloop.last %}, {% endif %}
                    {% endfor %}
                  {% else %}
                    Service
                  {% endif %}
                {% else %}
                  {{ sale.package_name|default:"Package" }} {% if sale.package_time %}({{ sale.package_time }} min){% endif %}
                {% endif %}
              </td>
              <td>{{ sale.therapist_name|default:sale.therapist_email|default:"-" }}</td>
              <td>{{ sale.payment_method|title }}</td>
              <td style="text-align: right;">{{ sale.total_price }}</td>
            </tr>
            {% empty %}
            <tr><td colspan="7" class="center-align">No sales found</td></tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>

    <!-- Product Sales -->
    <div class="card">
      <div class="card-content">
        <span class="card-title">Product Sales</span>
        <table class="data-table striped">
          <thead>
            <tr>
              <th>Time</th>
              <th>Invoice #</th>
              <th>Items</th>
              <th>Payment</th>
              <th style="text-align: right;">Amount</th>
            </tr>
          </thead>
          <tbody>
            {% for sale in daily.items.product_sales %}
            <tr>
              <td>{{ sale.created_at_formatted|default:"No date" }}</td>
              <td>{{ sale.invoice_number }}</td>
              <td>
                {% for item in sale.items %}
                  {{ item.product_name }} ({{ item.quantity }}){% if not forloop.last %}, {% endif %}
                {% endfor %}
              </td>
              <td>
                {% if sale.payment_method == "CASH" %}Cash
                {% elif sale.payment_method == "CARD" %}Card
                {% elif sale.payment_method == "ONLINE_LINK" %}Online Link
                {% else %}{{ sale.payment_method }}{% endif %}
              </td>
              <td style="text-align: right;">{{ sale.total_amount }}</td>
            </tr>
            {% empty %}
            <tr><td colspan="5" class="center-align">No product sales found</td></tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>

    <!-- Expenses -->
    <div class="card">
      <div class="card-content">
        <span class="card-title">Expenses</span>
        <table class="data-table striped">
          <thead>
            <tr>
              <th>Time</th>
              <th>Category</th>
              <th>Description</th>
              <th>Created By</th>
              <th style="text-align: right;">Amount</th>
            </tr>
          </thead>
          <tbody>
            {% for expense in daily.items.expenses %}
            <tr>
              <td>{{ expense.created_at_formatted|default:"No date" }}</td>
              <td>{{ expense.category_name }}</td>
              <td>{{ expense.description }}</td>
              <td>{{ expense.created_by_name }}</td>
              <td style="text-align: right;">{{ expense.amount }}</td>
            </tr>
            {% empty %}
            <tr><td colspan="5" class="center-align">No expenses found</td></tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>

    <!-- Withdrawals -->
    <div class="card">
      <div class="card-content">
        <span class="card-title">Cash Withdrawals</span>
        <table class="data-table striped">
          <thead>
            <tr>
              <th>Time</th>
              <th>Description</th>
              <th>Withdrawn By</th>
              <th style="text-align: right;">Amount</th>
            </tr>
          </thead>
          <tbody>
            {% for withdrawal in daily.items.withdrawals %}
            <tr>
              <td>{{ withdrawal.created_at_formatted|default:"No date" }}</td>
              <td>{{ withdrawal.description }}</td>
              <td>{{ withdrawal.withdrawn_by_name }}</td>
              <td style="text-align: right;">{{ withdrawal.amount }}</td>
            </tr>
            {% empty %}
            <tr><td colspan="4" class="center-align">No withdrawals found</td></tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>

    <!-- SECTION 3: MONTHLY CONTEXT -->
    <div class="section-header">
      <i class="material-icons small left">date_range</i> MONTHLY CONTEXT - {{ monthly.meta.month_name }} {{ monthly.meta.year }} - {{ location_display }}
    </div>

    <!-- Monthly Summary -->
    <div class="card">
      <div class="card-content">
        <span class="card-title">Monthly Sales Overview ({{ monthly.meta.start_date|date:"M d" }} - {{ monthly.meta.end_date|date:"M d" }})</span>
        <h6 style="color: #666; margin-top: 0;">Gross Sales Breakdown</h6>
        <table class="data-table striped">
          <thead>
            <tr>
              <th>Category</th>
              <th style="text-align: right;">Amount</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Service Sales</td>
              <td style="text-align: right;">{{ monthly.summary.service_sales }}</td>
            </tr>
            <tr>
              <td>Package Sales</td>
              <td style="text-align: right;">{{ monthly.summary.package_sales }}</td>
            </tr>
            <tr>
              <td>Product Sales</td>
              <td style="text-align: right;">{{ monthly.summary.product_sales }}</td>
            </tr>
            <tr class="summary-row">
              <td><strong>GROSS SALES</strong></td>
              <td style="text-align: right;"><strong>{{ monthly.summary.gross_sales }}</strong></td>
            </tr>
          </tbody>
        </table>
        
        <!-- Monthly Payment Methods Breakdown -->
        <div style="margin-top: 20px;">
          <h6 style="color: #666; margin-top: 0;">Monthly Payment Methods Breakdown</h6>
          <table class="data-table striped">
            <thead>
              <tr>
                <th>Payment Method</th>
                <th style="text-align: right;">Amount</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>Cash</td>
                <td style="text-align: right;">{{ monthly.summary.payment_totals.cash }}</td>
              </tr>
              <tr>
                <td>Card</td>
                <td style="text-align: right;">{{ monthly.summary.payment_totals.card }}</td>
              </tr>
              <tr>
                <td>Link</td>
                <td style="text-align: right;">{{ monthly.summary.payment_totals.link }}</td>
              </tr>
              <tr class="summary-row">
                <td><strong>Total</strong></td>
                <td style="text-align: right;"><strong>{{ monthly.summary.gross_sales }}</strong></td>
              </tr>
            </tbody>
          </table>
        </div>
        
        <!-- Monthly Net Sales Table -->
        <div style="margin-top: 20px;">
          <h6 style="color: #666; margin-top: 0;">Monthly Net Sales Calculation</h6>
          <table class="data-table striped">
            <thead>
              <tr>
                <th>Description</th>
                <th style="text-align: right;">Amount</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td>Gross Sales</td>
                <td style="text-align: right;">{{ monthly.summary.gross_sales }}</td>
              </tr>
              <tr class="deduction-row">
                <td>Less: VAT (5%)</td>
                <td style="text-align: right;">-{{ monthly.summary.deductions.vat }}</td>
              </tr>
              <tr class="deduction-row">
                <td>Less: Card Charges (2.1% of {{ monthly.summary.payment_totals.card }})</td>
                <td style="text-align: right;">-{{ monthly.summary.deductions.card_charges }}</td>
              </tr>
              <tr class="deduction-row">
                <td>Less: Link Charges (3% of {{ monthly.summary.payment_totals.link }})</td>
                <td style="text-align: right;">-{{ monthly.summary.deductions.link_charges }}</td>
              </tr>
              <tr class="deduction-row summary-row">
                <td><strong>Total Deductions</strong></td>
                <td style="text-align: right;"><strong>-{{ monthly.summary.deductions.total }}</strong></td>
              </tr>
              <tr class="total-row">
                <td><strong>NET SALES</strong></td>
                <td style="text-align: right;"><strong>{{ monthly.summary.net_sales }}</strong></td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Monthly Expenses & Withdrawals -->
    <div class="row">
      <div class="col s6">
        <div class="card">
          <div class="card-content">
            <span class="card-title">Monthly Total Expenses</span>
            <div class="stats-box">
              <h4 class="negative">{{ monthly.summary.expenses_total }}</h4>
            </div>
          </div>
        </div>
      </div>
      <div class="col s6">
        <div class="card">
          <div class="card-content">
            <span class="card-title">Monthly Total Withdrawals</span>
            <div class="stats-box">
              <h4 class="negative">{{ monthly.summary.withdrawals_total }}</h4>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Monthly All Sales -->
    <div class="card">
      <div class="card-content">
        <span class="card-title">Monthly Service & Package Sales (All Items)</span>
        <table class="data-table striped">
          <thead>
            <tr>
              <th>Date</th>
              <th>Type</th>
              <th>Customer</th>
              <th>Item</th>
              <th>Therapist</th>
              <th>Payment</th>
              <th style="text-align: right;">Amount</th>
            </tr>
          </thead>
          <tbody>
            {% for sale in monthly.items.sales %}
            <tr>
              <td>{{ sale.created_at_formatted|default:"No date" }}</td>
              <td>{{ sale.sale_type|title }}</td>
              <td>{{ sale.customer_name }}</td>
              <td>
                {% if sale.sale_type == "service" %}
                  {% if sale.services %}
                    {% for service in sale.services %}
                      {{ service.name }} ({{ service.duration }}min){% if not forloop.last %}, {% endif %}
                    {% endfor %}
                  {% else %}
                    Service
                  {% endif %}
                {% else %}
                  {{ sale.package_name|default:"Package" }} {% if sale.package_time %}({{ sale.package_time }} min){% endif %}
                {% endif %}
              </td>
              <td>{{ sale.therapist_name|default:sale.therapist_email|default:"-" }}</td>
              <td>{{ sale.payment_method|title }}</td>
              <td style="text-align: right;">{{ sale.total_price }}</td>
            </tr>
            {% empty %}
            <tr><td colspan="7" class="center-align">No sales found</td></tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>

    <!-- Monthly Product Sales -->
    <div class="card">
      <div class="card-content">
        <span class="card-title">Monthly Product Sales (All Items)</span>
        <table class="data-table striped">
          <thead>
            <tr>
              <th>Date</th>
              <th>Invoice #</th>
              <th>Items</th>
              <th>Payment</th>
              <th style="text-align: right;">Amount</th>
            </tr>
          </thead>
          <tbody>
            {% for sale in monthly.items.product_sales %}
            <tr>
              <td>{{ sale.created_at_formatted|default:"No date" }}</td>
              <td>{{ sale.invoice_number }}</td>
              <td>
                {% for item in sale.items %}
                  {{ item.product_name }} ({{ item.quantity }}){% if not forloop.last %}, {% endif %}
                {% endfor %}
              </td>
              <td>
                {% if sale.payment_method == "CASH" %}Cash
                {% elif sale.payment_method == "CARD" %}Card
                {% elif sale.payment_method == "ONLINE_LINK" %}Online Link
                {% else %}{{ sale.payment_method }}{% endif %}
              </td>
              <td style="text-align: right;">{{ sale.total_amount }}</td>
            </tr>
            {% empty %}
            <tr><td colspan="5" class="center-align">No product sales found</td></tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>

    <!-- Monthly Expenses -->
    <div class="card">
      <div class="card-content">
        <span class="card-title">Monthly Expenses (All Items)</span>
        <table class="data-table striped">
          <thead>
            <tr>
              <th>Date</th>
              <th>Category</th>
              <th>Description</th>
              <th>Created By</th>
              <th style="text-align: right;">Amount</th>
            </tr>
          </thead>
          <tbody>
            {% for expense in monthly.items.expenses %}
            <tr>
              <td>{{ expense.created_at_formatted|default:"No date" }}</td>
              <td>{{ expense.category_name }}</td>
              <td>{{ expense.description }}</td>
              <td>{{ expense.created_by_name }}</td>
              <td style="text-align: right;">{{ expense.amount }}</td>
            </tr>
            {% empty %}
            <tr><td colspan="5" class="center-align">No expenses found</td></tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>

    <!-- Monthly Withdrawals -->
    <div class="card">
      <div class="card-content">
        <span class="card-title">Monthly Cash Withdrawals (All Items)</span>
        <table class="data-table striped">
          <thead>
            <tr>
              <th>Date</th>
              <th>Description</th>
              <th>Withdrawn By</th>
              <th style="text-align: right;">Amount</th>
            </tr>
          </thead>
          <tbody>
            {% for withdrawal in monthly.items.withdrawals %}
            <tr>
              <td>{{ withdrawal.created_at_formatted|default:"No date" }}</td>
              <td>{{ withdrawal.description }}</td>
              <td>{{ withdrawal.withdrawn_by_name }}</td>
              <td style="text-align: right;">{{ withdrawal.amount }}</td>
            </tr>
            {% empty %}
            <tr><td colspan="4" class="center-align">No withdrawals found</td></tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>

    <!-- Report Footer -->
    <div class="card">
      <div class="card-content">
        <div class="row" style="margin-bottom: 0;">
          <div class="col s6">
            <p><strong>Generated By:</strong> {{ report.created_by.first_name }} {{ report.created_by.last_name }}</p>
          </div>
          <div class="col s6">
            <p><strong>Notes:</strong> {{ report.notes }}</p>
          </div>
        </div>
      </div>
    </div>

    <div class="center-align hide-print" style="margin: 20px 0">
      <a class="waves-effect waves-light btn" onclick="window.print()">
        <i class="material-icons left">print</i>Print Report
      </a>
    </div>
  </div>

  <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
</body>
</html>