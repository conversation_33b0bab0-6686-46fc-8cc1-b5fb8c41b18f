from django.db import models
from django.conf import settings


class Service(models.Model):
    """
    Represents an individual service offered.
    """

    name = models.CharField(max_length=100, unique=True)
    description = models.TextField()
    is_public = models.BooleanField(
        default=False,
        help_text="If True, the service is visible to unauthenticated users",
    )
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name="created_services",
    )
    therapist_specific = models.ForeignKey(
        "staff.TherapistProfile",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="special_services",
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name


class ServiceDuration(models.Model):
    """
    Represents the duration and price of a service.
    Linked to the Service model.
    """

    service = models.ForeignKey(
        Service, related_name="durations", on_delete=models.CASCADE
    )
    time = models.PositiveIntegerField(help_text="Duration in minutes")
    price = models.DecimalField(max_digits=8, decimal_places=2)

    def __str__(self):
        return f"{self.service.name} - {self.time} minutes"


class ServicePackage(models.Model):
    """
    Represents a package of services.
    """

    name = models.CharField(max_length=100)
    description = models.TextField()
    services_included = models.ManyToManyField(Service, related_name="packages")
    benefits = models.JSONField(help_text="List of benefits for the package")
    is_public = models.BooleanField(
        default=False,
        help_text="If True, the package is visible to unauthenticated users",
    )
    shared = models.BooleanField(
        default=False,
        help_text="Indicates if the package is a shared package",
    )
    unlimited = models.BooleanField(
        default=False,
        help_text="Indicates if the package is a unlimited package",
    )
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name="created_packages",
    )
    therapist_specific = models.ForeignKey(
        "staff.TherapistProfile",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="special_packages",
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name


class PackageOption(models.Model):
    """
    Represents individual options (time and price) for a ServicePackage.
    """

    package = models.ForeignKey(
        ServicePackage, related_name="options", on_delete=models.CASCADE
    )
    time = models.PositiveIntegerField(help_text="Duration in minutes")
    price = models.DecimalField(max_digits=8, decimal_places=2)

    def __str__(self):
        return f"{self.package.name} - {self.time} minutes"
