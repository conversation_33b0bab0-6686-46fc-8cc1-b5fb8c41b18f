# Commission System

The Commission System manages therapist earnings calculations, tracking, and reporting for the StretchUp API with advanced business logic including <PERSON>'s special commission structure.

## Overview

This module handles all aspects of commission management:
- Commission profiles for therapists
- Rule-based commission calculations with priority logic
- <PERSON>'s special commission structure for expert and home services
- Automatic commission calculation via Django signals
- Manual commission entries
- Commission earnings tracking with detailed statistics
- Therapist performance statistics (monthly, yearly, overall)
- VAT calculations and payment method fee deductions
- Dual commission logic for home services

## Models

### CommissionProfile

Defines the base commission structure for a therapist.

```python
CommissionProfile(models.Model):
    therapist = ForeignKey(TherapistProfile)
    name = <PERSON><PERSON><PERSON><PERSON>(max_length=100)
    base_percentage = DecimalField(max_digits=5, decimal_places=2)
    sessions_threshold = PositiveIntegerField(default=0)
    is_active = BooleanField(default=True)
    is_default = BooleanField(default=False)
    is_freelancer = BooleanField(default=False)
    freelancer_service_rate = DecimalField(max_digits=5, decimal_places=2, default=0)
    freelancer_package_rate = DecimalField(max_digits=5, decimal_places=2, default=0)
    owner_share_percentage = DecimalField(max_digits=5, decimal_places=2, default=0)
```

### CommissionRule

Defines specific commission rules that can override base rates.

```python
CommissionRule(models.Model):
    profile = ForeignKey(CommissionProfile, null=True, blank=True)
    name = CharField(max_length=100)
    rule_type = CharField(choices=RULE_TYPES)
    service = ForeignKey(Service, null=True, blank=True)
    package = ForeignKey(ServicePackage, null=True, blank=True)
    percentage = DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
    fixed_amount = DecimalField(max_digits=8, decimal_places=2, null=True, blank=True)
    min_sessions = PositiveIntegerField(default=0)
    priority = PositiveIntegerField(default=1)
    is_active = BooleanField(default=True)
    owner_share_percentage = DecimalField(max_digits=5, decimal_places=2, null=True, blank=True)
```

### OwnerEarning

Tracks the owner's share of sales.

```python
OwnerEarning(models.Model):
    sale = ForeignKey(Sale)
    amount = DecimalField(max_digits=8, decimal_places=2)
    percentage_used = DecimalField(max_digits=5, decimal_places=2)
    calculation_notes = TextField(blank=True)
    created_at = DateTimeField(auto_now_add=True)
```

### ManualCommission

Allows for manual commission entries (bonuses, adjustments).

```python
ManualCommission(models.Model):
    therapist = ForeignKey(User)
    sale = ForeignKey(Sale, null=True, blank=True)
    amount = DecimalField(max_digits=8, decimal_places=2)
    reason = TextField()
    is_approved = BooleanField(default=False)
    approved_by = ForeignKey(User, related_name="approved_commissions", null=True)
    approved_at = DateTimeField(null=True)
```

### CommissionEarning

Records all commission earnings with detailed tracking.

```python
CommissionEarning(models.Model):
    therapist = ForeignKey(User)
    sale = ForeignKey(Sale)
    amount = DecimalField(max_digits=8, decimal_places=2)
    percentage_used = DecimalField(max_digits=5, decimal_places=2, null=True)
    commission_type = CharField(choices=COMMISSION_TYPES)  # 'standard', 'omar_expert', 'omar_home', 'freelancer'
    commission_rule = ForeignKey(CommissionRule, null=True, blank=True)
    manual_commission = ForeignKey(ManualCommission, null=True, blank=True)
    is_primary = BooleanField(default=True)  # Primary vs secondary commission
    is_eligible = BooleanField(default=True)
    date_earned = DateField()
    month_stat = ForeignKey(TherapistMonthStats, null=True, blank=True)
    created_at = DateTimeField(auto_now_add=True)
```

### TherapistStats

Tracks overall therapist performance metrics.

```python
TherapistStats(models.Model):
    therapist = OneToOneField(User)
    total_sessions = PositiveIntegerField(default=0)
    total_earnings = DecimalField(max_digits=10, decimal_places=2, default=0)
    last_updated = DateTimeField(auto_now=True)
```

### TherapistYearStats

Tracks yearly therapist performance metrics.

```python
TherapistYearStats(models.Model):
    therapist = ForeignKey(User)
    year = PositiveIntegerField()
    total_sessions = PositiveIntegerField(default=0)
    total_earnings = DecimalField(max_digits=10, decimal_places=2, default=0)
    last_updated = DateTimeField(auto_now=True)
```

### TherapistMonthStats

Tracks monthly therapist performance metrics.

```python
TherapistMonthStats(models.Model):
    therapist = ForeignKey(User)
    year = PositiveIntegerField()
    month = PositiveIntegerField()
    total_sessions = PositiveIntegerField(default=0)
    total_earnings = DecimalField(max_digits=10, decimal_places=2, default=0)
    last_updated = DateTimeField(auto_now=True)
```

## Omar's Special Commission Structure

### Expert Services & Packages
Omar receives commission on **ALL** expert services and packages regardless of who performs them:
- **Expert Sessions**: 30 AED flat rate
- **Expert Packages**: 50 AED flat rate

### Home Services
Omar receives commission on **ALL** home services regardless of who performs them:
- **Home Sessions**: 50% + 5% VAT
- **Home Packages**: 40% + 5% VAT

### Dual Commission Logic
- **When Omar is NOT the therapist**: Both Omar and the therapist get commissions
- **When Omar IS the therapist**: Only Omar gets commission

## Commission Calculation Logic

### Priority System
1. **Threshold Check**: Commission only applies if total sessions exceed minimum threshold
2. **Rule Priority**: Highest priority rule is selected
3. **Commission Type Priority**: Percentage commission takes priority over fixed amount
4. **VAT Addition**: 5% VAT added to percentage-based commissions
5. **Fixed Amount Fallback**: Use fixed amount if no percentage is set
6. **Zero Commission**: Calculate zero if neither percentage nor fixed amount is set

### Payment Method Fees
- **Card Payments**: 2.1% fee deducted from commission
- **Link Payments**: 3% fee deducted from commission
- **Cash Payments**: No fees deducted

## Services

### CommissionCalculator

Handles advanced commission calculation logic with Omar's special rules.

```python
class CommissionCalculator:
    @staticmethod
    def calculate_commissions(sale):
        # Main entry point for commission calculation
        # Handles Omar's special rules and standard therapist commissions
        # Returns list of CommissionEarning objects

    @staticmethod
    def calculate_for_sale(sale):
        # Called by Django signals when a sale is created
        # Automatically calculates and saves all commissions

    @staticmethod
    def _calculate_standard_commission(sale, profile, month_stats):
        # Calculates standard therapist commission based on rules and thresholds
        # Follows priority logic: threshold → rule priority → percentage vs fixed → VAT

    @staticmethod
    def _calculate_omar_expert_commission(sale):
        # Omar's commission for expert sessions/packages (30/50 AED)

    @staticmethod
    def _calculate_omar_home_commission(sale):
        # Omar's commission for home services (50%/40% + VAT)

    @staticmethod
    def _is_expert_service(sale):
        # Detects if sale is for expert service/package

    @staticmethod
    def _is_home_service(sale):
        # Detects if sale is for home service

    @staticmethod
    def _find_applicable_rule(sale, profile):
        # Finds highest priority rule for the sale

    @staticmethod
    def _apply_vat_addition(amount):
        # Adds 5% VAT to percentage-based commissions

    @staticmethod
    def _update_therapist_stats(therapist):
        # Updates overall, yearly, and monthly statistics
```

## API Endpoints

### Commission Profiles

- `GET /commission/profiles/` - List all commission profiles (active only by default)
- `GET /commission/profiles/?include_inactive=true` - List all commission profiles including inactive ones
- `POST /commission/profiles/` - Create a new commission profile
- `GET /commission/profiles/{id}/` - Get a specific profile
- `PUT /commission/profiles/{id}/` - Update a profile
- `DELETE /commission/profiles/{id}/` - Delete a profile
- `POST /commission/profiles/{id}/activate/` - Activate a commission profile (Owner only)
- `POST /commission/profiles/{id}/deactivate/` - Deactivate a commission profile (Owner only)

### Commission Rules

- `GET /commission/rules/` - List all commission rules
- `POST /commission/rules/` - Create a new rule
- `GET /commission/rules/{id}/` - Get a specific rule
- `PUT /commission/rules/{id}/` - Update a rule
- `DELETE /commission/rules/{id}/` - Delete a rule

### Manual Commissions

- `GET /commission/manual/` - List manual commissions
- `POST /commission/manual/` - Create a manual commission
- `GET /commission/manual/{id}/` - Get a specific manual commission
- `POST /commission/manual/{id}/approve/` - Approve a manual commission

### Commission Earnings

- `GET /commission/earnings/` - List commission earnings
- `GET /commission/earnings/therapist/{id}/` - Get earnings for a therapist
- `GET /commission/earnings/date-range/` - Get earnings for a date range

### Owner Earnings

- `GET /commission/owner-earnings/` - List owner earnings
- `GET /commission/owner-earnings/date-range/` - Get owner earnings for a date range
- `GET /commission/owner-earnings/summary/` - Get summary of owner earnings

### Therapist Statistics

- `GET /commission/stats/` - List all therapist statistics
- `GET /commission/stats/{id}/` - Get statistics for a therapist
- `GET /commission/stats/monthly/{id}/` - Get monthly statistics
- `GET /commission/stats/yearly/{id}/` - Get yearly statistics

## Automatic Commission Calculation Flow

### Django Signals Integration
Commission calculation is **completely automatic** via Django signals:

1. **Sale Created**: Appointment or package sale is created via API
2. **Signal Triggered**: `post_save` signal automatically triggers `calculate_commission_on_sale()`
3. **Commission Calculation**: `CommissionCalculator.calculate_for_sale()` is called
4. **Omar's Special Rules Applied**:
   - Expert services: Omar gets 30/50 AED automatically
   - Home services: Omar gets 50%/40% + VAT automatically
5. **Therapist Commission Calculated**:
   - Threshold check (minimum sessions required)
   - Rule priority selection (highest priority rule)
   - Commission type priority (percentage > fixed amount)
   - VAT addition (5% for percentage commissions)
   - Payment method fees deducted
6. **Dual Commission Logic**:
   - Home services with non-Omar therapist: Both Omar and therapist get commissions
   - Home services with Omar as therapist: Only Omar gets commission
   - Expert services: Omar gets commission + therapist gets commission (if threshold met)
7. **Database Records Created**:
   - `CommissionEarning` records for all applicable commissions
   - `TherapistMonthStats` updated automatically
   - `TherapistYearStats` updated automatically
   - `TherapistStats` updated automatically

### Frontend Integration
- **No special API calls needed** - commissions calculated automatically
- **Use existing sale endpoints**: `/api/appointments/`, `/api/appointments/sales/direct-package/`, etc.
- **View commissions**: Use `/api/commissions/earnings/` endpoint

## Usage Examples

### Frontend: Create Sale (Automatic Commission Calculation)

```javascript
// Expert session - Omar gets 30 AED automatically
const createExpertSession = async () => {
  const response = await fetch('/api/appointments/', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({
      user: 123,
      therapist: 456,  // Basil
      services: [{ service_id: 789 }],  // Expert Stretching Session
      total_price: 150.00,
      location: "A"
    })
  });
  // Commission automatically calculated: Omar gets 30 AED, Basil gets standard commission
};

// Home session - Omar gets 50% + VAT automatically
const createHomeSession = async () => {
  const response = await fetch('/api/appointments/', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    },
    body: JSON.stringify({
      user: 124,
      therapist: 456,  // Basil
      services: [{ service_id: 101 }],  // Regular Session
      total_price: 200.00,
      location: "home"
    })
  });
  // Commission automatically calculated: Omar gets 105 AED (50% + VAT), Basil gets standard commission
};
```

### Backend: Manual Commission Calculation

```python
from api.commissions.services import CommissionCalculator

# Manual calculation (usually not needed - automatic via signals)
sale = Sale.objects.get(id=1)
CommissionCalculator.calculate_for_sale(sale)

# View commission earnings
earnings = CommissionEarning.objects.filter(sale=sale)
for earning in earnings:
    print(f"Therapist: {earning.therapist.get_full_name()}")
    print(f"Amount: {earning.amount}")
    print(f"Type: {earning.commission_type}")
    print(f"Primary: {earning.is_primary}")
```

### View Commission Data

```javascript
// Get commission earnings for Omar
const getOmarCommissions = async () => {
  const response = await fetch('/api/commissions/earnings/?therapist=1', {
    headers: { 'Authorization': `Bearer ${token}` }
  });
  const data = await response.json();

  data.results.forEach(commission => {
    console.log(`${commission.commission_type}: ${commission.amount} AED`);
    // omar_expert: 30.00 AED
    // omar_home: 105.00 AED
    // standard: 15.75 AED
  });
};
```

### Create Manual Commission

```python
from api.commissions.models import ManualCommission

# Create a bonus commission
manual_commission = ManualCommission.objects.create(
    therapist=therapist_user,
    amount=Decimal("50.00"),
    reason="Performance bonus for excellent customer feedback",
    is_approved=False
)

# Later, approve the commission
manual_commission.is_approved = True
manual_commission.approved_by = admin_user
manual_commission.approved_at = timezone.now()
manual_commission.save()
```

## Testing

### Run Commission Tests

```bash
# Run all commission tests
docker-compose exec web pytest api/commissions/tests/ -v

# Run specific test files
docker-compose exec web pytest api/commissions/tests/test_commission_calculation_logic.py -v
docker-compose exec web pytest api/commissions/tests/test_commission_profiles.py -v
docker-compose exec web pytest api/commissions/tests/test_commission_rules.py -v

# Run Omar's special commission tests
docker-compose exec web pytest api/commissions/tests/test_commission_calculation_logic.py::TestOmarSpecialCommissionStructure -v
docker-compose exec web pytest api/commissions/tests/test_commission_calculation_logic.py::TestOmarCommissionExamples -v

# Run commission calculation logic tests
docker-compose exec web pytest api/commissions/tests/test_commission_calculation_logic.py::TestCommissionCalculationThresholds -v
```

### Test Coverage
- **Commission Profiles**: 29+ tests covering CRUD operations and access control
- **Commission Rules**: 53+ tests covering rule creation, priority, and business logic
- **Commission Calculation**: 22+ tests covering calculation logic, thresholds, VAT, and priority
- **Omar's Special Structure**: 11+ tests covering expert and home service commissions
- **Total**: 115+ comprehensive tests

## Key Features

### ✅ Automatic Commission Calculation
- Zero frontend changes required
- Commissions calculated via Django signals
- Real-time processing when sales are created

### ✅ Omar's Special Commission Structure
- Expert services: 30/50 AED flat rates
- Home services: 50%/40% + VAT rates
- Dual commission logic for home services
- Automatic detection of expert/home services

### ✅ Advanced Business Logic
- Priority-based rule selection
- Threshold enforcement
- VAT calculations
- Payment method fee deductions
- Percentage vs fixed amount priority

### ✅ Comprehensive Statistics
- Monthly, yearly, and overall tracking
- Real-time updates
- Detailed commission breakdowns
- Performance metrics
