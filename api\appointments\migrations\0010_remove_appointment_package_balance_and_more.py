# Generated by Django 4.0 on 2025-02-04 15:35

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('authentication', '0005_historicaluser_gender_user_gender'),
        ('services', '0001_initial'),
        ('appointments', '0009_remove_packagebalance_appointment_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='appointment',
            name='package_balance',
        ),
        migrations.AlterField(
            model_name='appointment',
            name='location',
            field=models.CharField(choices=[('A', 'Studio Al Warqa Mall'), ('B', 'Studio Al mizhar branch')], max_length=1),
        ),
        migrations.CreateModel(
            name='UserPackage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_time', models.PositiveIntegerField(help_text='Total minutes purchased with this package')),
                ('remaining_time', models.PositiveIntegerField(help_text='Remaining minutes available in the package')),
                ('time_deducted', models.PositiveIntegerField(default=0, help_text='Total minutes that have been used')),
                ('active', models.BooleanField(default=True, help_text='Indicates if the package is still active')),
                ('package_option', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='user_packages', to='services.packageoption')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='user_package', to='authentication.user')),
            ],
        ),
        migrations.AddField(
            model_name='appointment',
            name='user_package',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='appointments', to='appointments.userpackage'),
        ),
    ]
