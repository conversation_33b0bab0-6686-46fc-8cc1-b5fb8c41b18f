from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    CommissionProfileViewSet, CommissionRuleViewSet,
    ManualCommissionViewSet, CommissionEarningViewSet,
    TherapistStatsViewSet, TherapistYearStatsViewSet, TherapistMonthStatsViewSet,
    EarningsExportView
)

router = DefaultRouter()
router.register(r'profiles', CommissionProfileViewSet)
router.register(r'rules', CommissionRuleViewSet)
router.register(r'manual', ManualCommissionViewSet)
router.register(r'earnings', CommissionEarningViewSet)
router.register(r'stats', TherapistStatsViewSet)
router.register(r'year-stats', TherapistYearStatsViewSet)
router.register(r'month-stats', TherapistMonthStatsViewSet)

urlpatterns = [
    path('', include(router.urls)),
    path('export/', EarningsExportView.as_view(), name='earnings-export'),
]
