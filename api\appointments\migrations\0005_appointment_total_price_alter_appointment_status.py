# Generated by Django 4.0 on 2024-12-24 02:19

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('appointments', '0004_appointment_payment_method'),
    ]

    operations = [
        migrations.AddField(
            model_name='appointment',
            name='total_price',
            field=models.PositiveIntegerField(default=100, help_text='Duration in minutes'),
        ),
        migrations.AlterField(
            model_name='appointment',
            name='status',
            field=models.CharField(choices=[('booked', 'Booked'), ('check_in', 'check_in'), ('cancelled', 'Cancelled'), ('no_show', 'No-Show')], default='booked', max_length=10),
        ),
    ]
