# Generated by Django 4.2.19 on 2025-03-04 18:16

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('appointments', '0016_alter_activity_activity_type_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='UnlimitedPackage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_time', models.PositiveIntegerField(default=99999, help_text='Total minutes available (always 99999 for unlimited packages)')),
                ('remaining_time', models.PositiveIntegerField(default=99999, help_text='Remaining minutes available (always starts at 99999)')),
                ('time_deducted', models.PositiveIntegerField(default=0, help_text='Total minutes that have been used (for reporting purposes)')),
                ('active', models.<PERSON><PERSON>anField(default=True, help_text='Indicates if the package is active (until expiry or manually deactivated)')),
                ('expiry_date', models.DateTimeField(blank=True, help_text='The date when the package will expire (1 month from creation)', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='unlimited_package', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.AddField(
            model_name='appointment',
            name='unlimited_package',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='appointments', to='appointments.unlimitedpackage'),
        ),
    ]
