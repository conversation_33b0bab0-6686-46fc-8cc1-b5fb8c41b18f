from django.core.management.base import BaseCommand
from api.services.models import Service, ServiceDuration, ServicePackage, PackageOption


class Command(BaseCommand):
    help = "Populate services, durations, and packages with initial data"

    def handle(self, *args, **kwargs):

        Service.objects.all().delete()
        ServicePackage.objects.all().delete()

        # Services Data
        services_data = [
            {
                "name": "Stretching",
                "description": "Improve flexibility and relieve tension.",
                "durations": [
                    {"time": 20, "price": 159},
                    {"time": 30, "price": 221},
                    {"time": 60, "price": 369},
                ],
            },
            {
                "name": "Massage",
                "description": "Relaxation and pain relief.",
                "durations": [
                    {"time": 20, "price": 180},
                    {"time": 30, "price": 270},
                    {"time": 60, "price": 315},
                ],
            },
            {
                "name": "Physio",
                "description": "Physiotherapy for recovery and performance enhancement.",
                "durations": [{"time": 20, "price": 180}, {"time": 50, "price": 400}],
            },
            {
                "name": "Compression",
                "description": "Improve blood flow and promote recovery.",
                "durations": [{"time": 20, "price": 80}, {"time": 60, "price": 210}],
            },
            {
                "name": "Red-Light Therapy",
                "description": "A session using red-light technology to stimulate healing and reduce pain.",
                "durations": [{"time": 20, "price": 200}],
            },
        ]

        # Add Services and Durations
        for service_data in services_data:
            service = Service.objects.create(
                name=service_data["name"], description=service_data["description"]
            )
            for duration in service_data["durations"]:
                ServiceDuration.objects.create(
                    service=service, time=duration["time"], price=duration["price"]
                )

        # Packages Data
        package_data = [
            {
                "name": "Light Recovery Package",
                "description": "A perfect choice for quick recovery.",
                "options": [
                    {"time": 100, "price": 525},
                    {"time": 300, "price": 1530},
                    {"time": 500, "price": 2600},
                ],
                "services_included": ["Massage", "Compression"],
                "benefits": [
                    "Reduces muscle soreness",
                    "Improves recovery time",
                    "Includes Red-Light Therapy",
                ],
            },
            {
                "name": "Medium Recovery Package",
                "description": "Designed for those who want comprehensive recovery sessions.",
                "options": [
                    {"time": 100, "price": 615},
                    {"time": 300, "price": 1833},
                    {"time": 500, "price": 3045},
                ],
                "services_included": ["Stretching", "Massage", "Compression"],
                "benefits": [
                    "Extended recovery benefits",
                    "Ideal for athletes and fitness enthusiasts",
                ],
            },
            {
                "name": "Extreme Recovery Package",
                "description": "The ultimate package for complete recovery and performance enhancement.",
                "options": [
                    {"time": 100, "price": 1100},
                    {"time": 300, "price": 3299},
                    {"time": 500, "price": 5450},
                ],
                "services_included": [
                    "Red-Light Therapy",
                    "Stretching",
                    "Massage",
                    "Physio",
                    "Compression",
                ],
                "benefits": [
                    "All-inclusive recovery experience",
                    "Maximum benefits for athletes and fitness professionals",
                ],
            },
        ]

        # Add Packages and Options
        for package in package_data:
            services = Service.objects.filter(name__in=package["services_included"])
            package_obj = ServicePackage.objects.create(
                name=package["name"],
                description=package["description"],
                benefits=package["benefits"],
            )
            package_obj.services_included.set(services)

            for option in package["options"]:
                PackageOption.objects.create(
                    package=package_obj, time=option["time"], price=option["price"]
                )

        shared_packages = [
            {
                "name": "Stretching and Massage",
                "description": "You can share minutes with your friends or family.",
                "options": [
                    {"time": 500, "price": 2980},
                    {"time": 1000, "price": 9800},
                ],
                "services_included": [
                    "Stretching",
                    "Massage",
                ],
                "benefits": [
                    "Reduces muscle soreness",
                    "Improves recovery time",
                    "Ideal for athletes and fitness enthusiasts",
                ],
            },
            {
                "name": "All Services",
                "description": "You can share minutes with your friends or family.",
                "options": [
                    {"time": 500, "price": 2750},
                    {"time": 1000, "price": 5400},
                ],
                "services_included": [
                    "Red-Light Therapy",
                    "Stretching",
                    "Massage",
                    "Physio",
                    "Compression",
                ],
                "benefits": [
                    "All-inclusive recovery experience",
                    "Maximum benefits for athletes and fitness professionals",
                ],
            },
        ]

        for package in shared_packages:
            services = Service.objects.filter(name__in=package["services_included"])
            package_obj = ServicePackage.objects.create(
                name=package["name"],
                description=package["description"],
                benefits=package["benefits"],
                shared=True,
            )
            package_obj.services_included.set(services)

            for option in package["options"]:
                PackageOption.objects.create(
                    package=package_obj, time=option["time"], price=option["price"]
                )

        unlimited_packages = [
            {
                "name": "Unlimited Sessions",
                "description": "You get access to use all of our services for 30 days!",
                "options": [
                    {"time": 20000, "price": 3300},
                ],
                "services_included": [
                    "Red-Light Therapy",
                    "Stretching",
                    "Massage",
                    "Physio",
                    "Compression",
                ],
                "benefits": [
                    "All-inclusive recovery experience",
                    "Maximum benefits for athletes and fitness professionals",
                ],
            },
        ]

        for package in unlimited_packages:
            services = Service.objects.filter(name__in=package["services_included"])
            package_obj = ServicePackage.objects.create(
                name=package["name"],
                description=package["description"],
                benefits=package["benefits"],
                unlimited=True,
            )
            package_obj.services_included.set(services)

            for option in package["options"]:
                PackageOption.objects.create(
                    package=package_obj, time=option["time"], price=option["price"]
                )
