from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    ProductViewSet,
    ProductManagementViewSet,
    ProductSaleViewSet,
    CashRegisterViewSet,
    CashWithdrawalViewSet,
    CashDepositViewSet,
    ExpenseViewSet,
    DailySalesReportViewSet,
)

router = DefaultRouter()
router.register(r"products", ProductViewSet)
router.register(r"products-management", ProductManagementViewSet)
router.register(r"sales", ProductSaleViewSet)
router.register(r"cash-registers", CashRegisterViewSet)
router.register(r"withdrawals", CashWithdrawalViewSet)
router.register(r"deposits", CashDepositViewSet)
router.register(r"expenses", ExpenseViewSet)
router.register(r"reports", DailySalesReportViewSet)

urlpatterns = [
    path("", include(router.urls)),
]
