from django.core.management.base import BaseCommand
from django.utils import timezone
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from django.core.mail import send_mail
from django.conf import settings
from datetime import datetime
import copy
import logging

# Import your Appointment model and serializer
from api.appointments.models import Appointment
from api.appointments.serializers import AppointmentSerializer

# Import the existing command to reuse functions
from api.appointments.management.commands.send_appointment_reminders import (
    Command as ReminderCommand,
)

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "Sends a test reminder email for an <NAME_EMAIL>"

    def add_arguments(self, parser):
        # Optional argument to specify appointment ID
        parser.add_argument(
            "--id",
            type=int,
            help="Specify an appointment ID to use for the test email",
            required=False,
        )

    def handle(self, *args, **options):
        self.stdout.write("Sending a test appointment reminder email...")

        # Get appointment ID from options if specified
        appointment_id = options.get("id")

        # Try to get the specified appointment or the most recent booked appointment
        try:
            if appointment_id:
                appointment = Appointment.objects.filter(id=appointment_id).first()
                if not appointment:
                    self.stdout.write(
                        self.style.ERROR(
                            f"Appointment with ID {appointment_id} not found"
                        )
                    )
                    return
            else:
                # Get the most recent booked appointment
                appointment = (
                    Appointment.objects.filter(status="booked")
                    .order_by("-date", "-time")
                    .first()
                )

                if not appointment:
                    self.stdout.write(
                        self.style.ERROR("No booked appointments found in the database")
                    )
                    return

            self.stdout.write(f"Using appointment ID: {appointment.id}")

            # Serialize the appointment
            serialized_appointment = AppointmentSerializer(appointment).data

            # Send the test email
            self.send_test_reminder_email(serialized_appointment)

            self.stdout.write(
                self.style.SUCCESS(
                    f"Successfully sent test reminder email for appointment {appointment.id} to <EMAIL>"
                )
            )

        except Exception as e:
            logger.error(
                f"Failed to send test reminder email: {str(e)}",
                exc_info=True,
            )
            self.stdout.write(
                self.style.ERROR(f"Failed to send test reminder email: {str(e)}")
            )

    def send_test_reminder_email(self, serialized_appointment):
        """
        Send a test reminder email with the appointment data.
        This reuses the date formatting from the main reminder command.
        """

        # Format dates for better readability (copied from the main reminder command)
        def format_serialized_appointment_dates(data):
            """Format dates in the serialized appointment data for email templates."""
            # Deep copy to avoid modifying the original
            formatted_data = copy.deepcopy(data)

            # Format the main appointment date
            if "date" in formatted_data:
                try:
                    date_obj = datetime.strptime(formatted_data["date"], "%Y-%m-%d")
                    formatted_data["date"] = date_obj.strftime("%B %d, %Y")
                except (ValueError, TypeError):
                    pass

            # Format appointment time
            if "time" in formatted_data:
                try:
                    time_obj = datetime.strptime(formatted_data["time"], "%H:%M:%S")
                    formatted_data["time"] = time_obj.strftime("%I:%M %p")
                except (ValueError, TypeError):
                    pass

            # Format package expiry dates
            # User Package
            if (
                "user_package_obj" in formatted_data
                and formatted_data["user_package_obj"]
            ):
                if "expiry_date" in formatted_data["user_package_obj"]:
                    try:
                        date_str = formatted_data["user_package_obj"]["expiry_date"]
                        date_obj = datetime.strptime(date_str, "%Y-%m-%d")
                        formatted_data["user_package_obj"]["expiry_date"] = (
                            date_obj.strftime("%B %d, %Y")
                        )
                    except (ValueError, TypeError):
                        pass

            # Shared Package
            if (
                "shared_package_obj" in formatted_data
                and formatted_data["shared_package_obj"]
            ):
                if "expiry_date" in formatted_data["shared_package_obj"]:
                    try:
                        date_str = formatted_data["shared_package_obj"]["expiry_date"]
                        date_obj = datetime.strptime(date_str, "%Y-%m-%d")
                        formatted_data["shared_package_obj"]["expiry_date"] = (
                            date_obj.strftime("%B %d, %Y")
                        )
                    except (ValueError, TypeError):
                        pass

            # Unlimited Package
            if (
                "unlimited_package_obj" in formatted_data
                and formatted_data["unlimited_package_obj"]
            ):
                if "expiry_date" in formatted_data["unlimited_package_obj"]:
                    try:
                        date_str = formatted_data["unlimited_package_obj"]["expiry_date"]
                        date_obj = datetime.strptime(date_str, "%Y-%m-%d")
                        formatted_data["unlimited_package_obj"]["expiry_date"] = (
                            date_obj.strftime("%B %d, %Y")
                        )
                    except (ValueError, TypeError):
                        pass

            return formatted_data

        # Format dates in the appointment data
        formatted_appointment = format_serialized_appointment_dates(
            serialized_appointment
        )

        # Get formatted time for subject line
        time_for_subject = formatted_appointment.get("time", "")

        subject = f"[TEST] Reminder: Your appointment tomorrow at {time_for_subject}"

        # Prepare context for email template
        context = {"appointment": formatted_appointment}

        # Render HTML email template
        html_message = render_to_string("email/app_reminder.html", context)
        plain_message = strip_tags(html_message)

        # Override the recipient email
        recipient_email = "<EMAIL>"
        # recipient_email = "<EMAIL>"

        # Send the email
        send_mail(
            subject=subject,
            message=plain_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[recipient_email],
            html_message=html_message,
            fail_silently=False,
        )
