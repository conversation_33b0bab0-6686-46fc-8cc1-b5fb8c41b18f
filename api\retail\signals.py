from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver
from django.utils import timezone
from django.db.models import Sum
from django.db import transaction

from .models import (
    ProductSale,
    CashRegister,
    CashWithdrawal,
    CashDeposit,
    Expense,
    DailySalesReport,
)
from api.appointments.models import Appointment
import logging

logger = logging.getLogger(__name__)
