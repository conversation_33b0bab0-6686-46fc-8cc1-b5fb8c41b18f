from decimal import Decimal
from django.db import transaction
from django.utils import timezone
from dateutil.relativedelta import relativedelta
import logging
from api.appointments.models import Sale, Appointment, AppointmentService
from api.staff.models import TherapistProfile
from .models import (
    CommissionProfile, CommissionRule, ManualCommission,
    CommissionEarning, TherapistStats, TherapistYearStats, TherapistMonthStats
)

# Set up logger for commission calculations
logger = logging.getLogger('commission_calculator')
logger.setLevel(logging.INFO)


class CommissionCalculator:
    """
    Service for calculating commissions based on sales and appointments.
    """

    @staticmethod
    def calculate_commissions(sale):
        """
        Calculate commissions for a sale, potentially creating multiple commission records
        if multiple people should receive commissions for the same sale.
        """
        logger.info(f"   🔍 Analyzing sale for commission calculation...")

        # Get the therapist profile from the appointment
        therapist_profile = sale.appointment.therapist

        # Debug: Check what type of object therapist_profile is
        logger.info(f"   🔍 Therapist profile object type: {type(therapist_profile)}")
        logger.info(f"   🔍 Therapist profile object value: {therapist_profile}")

        # Ensure we have a valid TherapistProfile object
        if not isinstance(therapist_profile, TherapistProfile):
            logger.error(f"   ❌ Invalid therapist profile object: {therapist_profile}")
            return []

        # Get the actual User object from the profile
        therapist = therapist_profile.user
        profile = therapist_profile

        logger.info(f"   👨‍⚕️ Therapist: {therapist.get_full_name()} (ID: {therapist.id})")
        logger.info(f"   📋 Profile: Freelancer={profile.is_freelancer}")

        # Check if service is eligible for commission
        if not CommissionCalculator._is_eligible_for_commission(sale):
            logger.info(f"   ❌ Sale not eligible for commission")
            return []

        logger.info(f"   ✅ Sale eligible for commission")
        commissions = []

        # Check if this is an expert service/package
        is_expert = CommissionCalculator._is_expert_service(sale)
        is_home = CommissionCalculator._is_home_service(sale)

        logger.info(f"   🎯 Service Type: Expert={is_expert}, Home={is_home}")

        # Omar gets commission on ALL expert sessions/packages regardless of who performs them
        if is_expert:
            logger.info(f"   🌟 Expert service detected - calculating Omar's commission...")
            omar_commission = CommissionCalculator._calculate_omar_expert_commission(sale)
            if omar_commission:
                commissions.append(omar_commission)
                logger.info(f"   ✅ Omar expert commission: {omar_commission.amount} AED")

        # Omar gets commission on ALL home services regardless of who performs them
        if is_home:
            logger.info(f"   🏠 Home service detected - calculating Omar's commission...")
            omar_home_commission = CommissionCalculator._calculate_omar_home_commission(sale)
            if omar_home_commission:
                commissions.append(omar_home_commission)
                logger.info(f"   ✅ Omar home commission: {omar_home_commission.amount} AED")

        # Get current session count for threshold checking
        current_year = sale.created_at.year
        current_month = sale.created_at.month

        # Count current monthly sessions for threshold checking
        monthly_sessions = Appointment.objects.filter(
            therapist=profile,
            date__year=current_year,
            date__month=current_month,
            status__in=['check_in', 'completed']
        ).count()

        logger.info(f"   📊 Month Stats: {monthly_sessions} sessions this month")

        # Calculate therapist commission (if not Omar and not home service, or if Omar and not home service)
        is_omar = profile.user.email == "<EMAIL>"  # TODO: Fetch this email from .env file

        logger.info(f"   👑 Is Omar: {is_omar}")

        # If therapist is freelancer, use freelancer rates
        if profile.is_freelancer:
            logger.info(f"   💼 Freelancer detected - calculating freelancer commission...")
            freelancer_commission = CommissionCalculator._calculate_freelancer_commission(sale, profile)
            commissions.append(freelancer_commission)
            logger.info(f"   ✅ Freelancer commission: {freelancer_commission.amount} AED")
        # For home services: only calculate therapist commission if therapist is NOT Omar
        elif is_home and not is_omar:
            logger.info(f"   🏠 Home service with non-Omar therapist - calculating standard commission...")
            therapist_commission = CommissionCalculator._calculate_standard_commission(sale, profile, monthly_sessions)
            if therapist_commission:
                commissions.append(therapist_commission)
                logger.info(f"   ✅ Therapist commission: {therapist_commission.amount} AED")
            else:
                logger.info(f"   ❌ No therapist commission (threshold not met or no rules)")
        # For non-home services: calculate therapist commission if not Omar
        elif not is_home and not is_omar:
            logger.info(f"   🏢 Regular service with non-Omar therapist - calculating standard commission...")
            therapist_commission = CommissionCalculator._calculate_standard_commission(sale, profile, monthly_sessions)
            if therapist_commission:
                commissions.append(therapist_commission)
                logger.info(f"   ✅ Therapist commission: {therapist_commission.amount} AED")
            else:
                logger.info(f"   ❌ No therapist commission (threshold not met or no rules)")
        else:
            logger.info(f"   ℹ️ No additional therapist commission (Omar performing home service)")

        return commissions

    @staticmethod
    def calculate_for_sale(sale):
        """
        Calculate and save commissions for a sale.
        This method is called by signals when a sale is created.
        """
        logger.info(f"🎯 Starting commission calculation for Sale ID: {sale.id}")
        logger.info(f"   Sale Details: Type={sale.sale_type}, Price={sale.total_price}, Payment={getattr(sale, 'payment_method', 'N/A')}")

        try:
            # Calculate commissions
            commissions = CommissionCalculator.calculate_commissions(sale)

            logger.info(f"   📊 Calculated {len(commissions)} commission(s) for Sale ID: {sale.id}")

            # Save commission earnings to database
            saved_count = 0
            for commission in commissions:
                if commission:  # Only save if commission is not None
                    commission.save()
                    saved_count += 1
                    logger.info(f"   ✅ Saved commission: {commission.therapist.user.get_full_name()} - {commission.amount} AED ({commission.commission_type})")

            logger.info(f"🎉 Commission calculation completed for Sale ID: {sale.id} - {saved_count} commissions saved")

            # Update therapist stats AFTER commissions are saved to get accurate earnings
            if sale.appointment and sale.appointment.therapist:
                CommissionCalculator._update_therapist_stats(sale.appointment.therapist)
                logger.info(f"   📊 Updated therapist stats after commission calculation")

        except Exception as e:
            # Log error but don't raise to avoid breaking sale creation
            logger.error(f"❌ Error calculating commission for sale {sale.id}: {e}")
            print(f"Error calculating commission for sale {sale.id}: {e}")

    @staticmethod
    def _calculate_standard_commission(sale, profile, monthly_sessions):
        """
        Calculate standard commission for a therapist based on commission rules.
        Follows the priority logic:
        1. Check if total sessions meet minimum threshold
        2. Get rule with highest priority
        3. Prioritize percentage commission over fixed amount
        4. Add 5% VAT to percentage commissions
        5. Use fixed amount if no percentage is set
        6. Calculate zero commission if neither is set
        """
        # Get the therapist's commission profile
        commission_profile = profile.commission_profiles.filter(
            is_active=True, is_default=True
        ).first()

        if not commission_profile:
            logger.info(f"      ❌ No active commission profile found")
            return None

        logger.info(f"      📋 Commission Profile: {commission_profile.name} (Threshold: {commission_profile.sessions_threshold})")

        # Find the applicable rule with highest priority
        rule = CommissionCalculator._find_applicable_rule(sale, commission_profile)

        if rule:
            logger.info(f"      📏 Rule found: {rule.name} (Priority: {rule.priority}, Min Sessions: {rule.min_sessions})")
        else:
            logger.info(f"      📏 No specific rule found, using profile base percentage: {commission_profile.base_percentage}%")

        # Check minimum sessions threshold
        if rule and rule.min_sessions > 0:
            # Check if therapist has met the rule's minimum sessions requirement
            if monthly_sessions < rule.min_sessions:
                logger.info(f"      ❌ Threshold not met: {monthly_sessions} < {rule.min_sessions} (rule threshold)")
                return None
            else:
                logger.info(f"      ✅ Rule threshold met: {monthly_sessions} >= {rule.min_sessions}")
        elif commission_profile.sessions_threshold > 0:
            # Check profile's base threshold if no rule-specific threshold
            if monthly_sessions < commission_profile.sessions_threshold:
                logger.info(f"      ❌ Threshold not met: {monthly_sessions} < {commission_profile.sessions_threshold} (profile threshold)")
                return None
            else:
                logger.info(f"      ✅ Profile threshold met: {monthly_sessions} >= {commission_profile.sessions_threshold}")
        else:
            logger.info(f"      ℹ️ No threshold requirements")

        # Calculate commission amount based on priority logic
        commission_amount = Decimal('0')
        percentage_used = None
        commission_type = 'standard'

        if rule:
            # Rule found - prioritize percentage over fixed amount
            if rule.percentage is not None and rule.percentage > 0:
                # Use percentage commission (priority)
                base_price = sale.total_price
                if sale.discount_percentage and sale.discount_percentage > 0:
                    # Calculate original price before discount
                    discount_multiplier = Decimal('1') - (sale.discount_percentage / Decimal('100'))
                    base_price = sale.total_price / discount_multiplier

                commission_amount = (base_price * rule.percentage / Decimal('100')).quantize(Decimal('0.01'))
                percentage_used = rule.percentage

                # Add 5% VAT to percentage commissions
                commission_amount = CommissionCalculator._apply_vat_addition(commission_amount)

            elif rule.fixed_amount is not None and rule.fixed_amount > 0:
                # Use fixed amount commission (secondary priority)
                commission_amount = rule.fixed_amount
                percentage_used = None
            else:
                # Neither percentage nor fixed amount set - zero commission
                commission_amount = Decimal('0')
                percentage_used = None
        else:
            # No rule found - use profile base percentage if available
            if commission_profile.base_percentage > 0:
                base_price = sale.total_price
                if sale.discount_percentage and sale.discount_percentage > 0:
                    # Calculate original price before discount
                    discount_multiplier = Decimal('1') - (sale.discount_percentage / Decimal('100'))
                    base_price = sale.total_price / discount_multiplier

                commission_amount = (base_price * commission_profile.base_percentage / Decimal('100')).quantize(Decimal('0.01'))
                percentage_used = commission_profile.base_percentage

                # Add 5% VAT to percentage commissions
                commission_amount = CommissionCalculator._apply_vat_addition(commission_amount)
            else:
                # No commission rules or base percentage - zero commission
                return None

        # Subtract payment processing charges if applicable
        if commission_amount > 0 and hasattr(sale, 'payment_method'):
            if sale.payment_method == 'card':
                # 2.1% fee for card payments
                card_fee_rate = Decimal('0.021')
                card_fee = (sale.total_price * card_fee_rate).quantize(Decimal('0.01'))
                commission_amount -= card_fee
            elif sale.payment_method == 'link':
                # 3% fee for link payments
                link_fee_rate = Decimal('0.03')
                link_fee = (sale.total_price * link_fee_rate).quantize(Decimal('0.01'))
                commission_amount -= link_fee

        # Ensure commission amount is not negative
        if commission_amount < 0:
            commission_amount = Decimal('0')

        # Return None if commission is zero (no commission for this sale)
        if commission_amount == 0:
            return None

        return CommissionEarning(
            sale=sale,
            therapist=profile,  # Use profile (TherapistProfile) instead of profile.user (User)
            amount=commission_amount,
            percentage_used=percentage_used,
            commission_rule=rule,
            commission_type=commission_type,
            is_primary=True,
            date_earned=sale.created_at.date()
        )

    @staticmethod
    def _calculate_freelancer_commission(sale, profile):
        """Calculate commission for freelancers (40% for packages, 50% for services)"""
        if sale.sale_type == 'service':
            rate = profile.freelancer_service_rate / 100
            commission_amount = (sale.total_price * rate).quantize(Decimal('0.01'))
        else:
            rate = profile.freelancer_package_rate / 100
            commission_amount = (sale.total_price * rate).quantize(Decimal('0.01'))

        # Apply VAT addition for percentage-based freelancer commissions
        commission_amount = CommissionCalculator._apply_vat_addition(commission_amount)

        return CommissionEarning(
            sale=sale,
            therapist=profile,  # Use profile (TherapistProfile) instead of profile.user (User)
            amount=commission_amount,
            percentage_used=rate * 100,  # Use percentage_used instead of percentage
            commission_type='freelancer',
            is_primary=True,
            date_earned=sale.created_at.date()
        )

    @staticmethod
    def _process_manual_commission(manual_commission):
        """Process a manual commission record."""
        sale = manual_commission.sale
        therapist = manual_commission.therapist

        if manual_commission.amount:
            commission_amount = manual_commission.amount
            percentage_used = None
        else:
            # Calculate from percentage
            commission_amount = (sale.total_price * manual_commission.percentage / Decimal('100')).quantize(Decimal('0.01'))
            percentage_used = manual_commission.percentage

        # Get or create monthly stats
        sale_date = sale.created_at.date()
        sale_year = sale_date.year
        sale_month = sale_date.month

        month_stats, created = TherapistMonthStats.objects.get_or_create(
            therapist=therapist,
            year=sale_year,
            month=sale_month
        )

        # Create commission earning
        with transaction.atomic():
            earning = CommissionEarning.objects.create(
                therapist=therapist,
                sale=sale,
                amount=commission_amount,
                percentage_used=percentage_used,
                commission_rule=None,
                manual_commission=manual_commission,
                date_earned=sale_date,
                is_eligible=True,  # Manual commissions are always eligible
                month_stat=month_stats
            )

            # Update therapist statistics
            CommissionCalculator._update_therapist_stats(therapist)

            return earning

    @staticmethod
    def _is_eligible_for_commission(sale):
        """
        Check if the sale is eligible for commission.
        Excludes red light sessions, compression sessions, and consultations.
        Also checks monthly thresholds.
        """
        # First check service/package eligibility
        is_eligible = False

        if not sale.appointment:
            # This is a direct package sale, check package
            if sale.user_package or sale.shared_package or sale.unlimited_package:
                is_eligible = True
        else:
            # This is an appointment sale
            # Check if it's excluded service type
            excluded_services = ["Red-Light Therapy", "Compression", "Assessment", "Consultation"]

            # Get appointment services
            services = AppointmentService.objects.filter(appointment=sale.appointment)

            # If all services are excluded, return False
            if all(service.service.name in excluded_services for service in services):
                is_eligible = False
            else:
                is_eligible = True

        # If not eligible based on service/package type, return False
        if not is_eligible:
            return False

        # If eligible, check monthly thresholds
        # This will be handled in the main calculate_for_sale method
        # since we need the therapist profile
        return True

    @staticmethod
    def _find_applicable_rule(sale, profile):
        """Find the most applicable commission rule for this sale."""
        # Check for specific service/package rules first (higher priority)
        rule = None

        if sale.sale_type == 'service' and sale.appointment:
            # Find service-specific rules
            services = AppointmentService.objects.filter(appointment=sale.appointment)
            for service in services:
                # Try to find a rule for this service in the profile
                service_rule = CommissionRule.objects.filter(
                    profile=profile,
                    rule_type='service',
                    service=service.service,
                    is_active=True
                ).order_by('-priority').first()

                if service_rule:
                    rule = service_rule
                    break

                # Try global service rule
                service_rule = CommissionRule.objects.filter(
                    profile=None,
                    rule_type='service',
                    service=service.service,
                    is_active=True
                ).order_by('-priority').first()

                if service_rule:
                    rule = service_rule
                    break

        elif sale.sale_type == 'package':
            # Find package-specific rules
            package = None

            if sale.user_package and sale.user_package.package_option:
                package = sale.user_package.package_option.package
            elif sale.shared_package and sale.shared_package.package_option:
                package = sale.shared_package.package_option.package
            elif sale.unlimited_package and sale.unlimited_package.package_option:
                package = sale.unlimited_package.package_option.package

            if package:
                # Try to find a rule for this package in the profile
                package_rule = CommissionRule.objects.filter(
                    profile=profile,
                    rule_type='package',
                    package=package,
                    is_active=True
                ).order_by('-priority').first()

                if package_rule:
                    rule = package_rule

                # Try global package rule
                if not rule:
                    package_rule = CommissionRule.objects.filter(
                        profile=None,
                        rule_type='package',
                        package=package,
                        is_active=True
                    ).order_by('-priority').first()

                    if package_rule:
                        rule = package_rule

        # If no specific rule found, try global rules
        if not rule:
            rule = CommissionRule.objects.filter(
                profile=profile,
                rule_type='global',
                is_active=True
            ).order_by('-priority').first()

            if not rule:
                # Try completely global rule
                rule = CommissionRule.objects.filter(
                    profile=None,
                    rule_type='global',
                    is_active=True
                ).order_by('-priority').first()

        return rule

    @staticmethod
    def _calculate_amount(sale, profile, rule):
        """
        Calculate commission amount based on rule or profile.
        Subtracts payment processing charges based on payment method.
        """
        # Special case for Omar (therapist ID will need to be configured)
        if profile.therapist.id == 1:  # Assuming Omar's ID is 1, adjust as needed
            return CommissionCalculator._calculate_omar_commission(sale, profile)

        # Calculate base commission amount
        commission_amount = Decimal('0')
        percentage_used = Decimal('0')

        if rule:
            # Prioritize percentage commission over fixed amount
            if rule.percentage is not None and rule.percentage > 0:
                # Apply percentage to sale price (priority)
                # Take into account any discount
                base_price = sale.total_price
                if sale.discount_percentage and sale.discount_percentage > 0:
                    # Calculate original price before discount
                    discount_multiplier = Decimal('1') - (sale.discount_percentage / Decimal('100'))
                    base_price = sale.total_price / discount_multiplier

                commission_amount = (base_price * rule.percentage / Decimal('100')).quantize(Decimal('0.01'))
                percentage_used = rule.percentage

                # Add 5% VAT to percentage commissions
                commission_amount = CommissionCalculator._apply_vat_addition(commission_amount)

            elif rule.fixed_amount is not None and rule.fixed_amount > 0:
                # Use fixed amount commission (secondary priority)
                commission_amount = rule.fixed_amount
                percentage_used = None
            else:
                # Neither percentage nor fixed amount set - zero commission
                commission_amount = Decimal('0')
                percentage_used = None
        # Fallback to profile base percentage
        elif profile.base_percentage > 0:
            base_price = sale.total_price
            if sale.discount_percentage and sale.discount_percentage > 0:
                # Calculate original price before discount
                discount_multiplier = Decimal('1') - (sale.discount_percentage / Decimal('100'))
                base_price = sale.total_price / discount_multiplier

            commission_amount = (base_price * profile.base_percentage / Decimal('100')).quantize(Decimal('0.01'))
            percentage_used = profile.base_percentage

            # Add 5% VAT to percentage commissions
            commission_amount = CommissionCalculator._apply_vat_addition(commission_amount)
        else:
            # No commission rules or base percentage - zero commission
            commission_amount = Decimal('0')
            percentage_used = None

        # Subtract payment processing charges
        if commission_amount > 0 and hasattr(sale, 'payment_method'):
            if sale.payment_method == 'card':
                # 2.1% fee for card payments
                card_fee_rate = Decimal('0.021')
                card_fee = (sale.total_price * card_fee_rate).quantize(Decimal('0.01'))
                commission_amount -= card_fee
            elif sale.payment_method == 'link':
                # 3% fee for link payments
                link_fee_rate = Decimal('0.03')
                link_fee = (sale.total_price * link_fee_rate).quantize(Decimal('0.01'))
                commission_amount -= link_fee

        # Ensure commission amount is not negative
        if commission_amount < 0:
            commission_amount = Decimal('0')

        return commission_amount, percentage_used

    @staticmethod
    def _apply_vat_addition(amount):
        """Add 5% VAT to the given amount for percentage-based commissions"""
        vat_rate = Decimal('0.05')
        vat_amount = (amount * vat_rate).quantize(Decimal('0.01'))
        return amount + vat_amount

    @staticmethod
    def _apply_vat_deduction(amount):
        """Deduct 5% VAT from the given amount (legacy method for specific cases)"""
        vat_rate = Decimal('0.05')
        vat_amount = (amount * vat_rate).quantize(Decimal('0.01'))
        return amount - vat_amount

    @staticmethod
    def _is_expert_service(sale):
        """Check if the sale is for an expert service or package"""
        if sale.appointment:
            # Check appointment services
            services = AppointmentService.objects.filter(appointment=sale.appointment)
            for service in services:
                if 'expert' in service.service.name.lower():
                    return True
        elif sale.user_package or sale.shared_package or sale.unlimited_package:
            # Check package name
            package = None
            if sale.user_package and sale.user_package.package_option:
                package = sale.user_package.package_option.package
            elif sale.shared_package and sale.shared_package.package_option:
                package = sale.shared_package.package_option.package
            elif sale.unlimited_package and sale.unlimited_package.package_option:
                package = sale.unlimited_package.package_option.package

            if package and 'expert' in package.name.lower():
                return True
        return False

    @staticmethod
    def _is_home_service(sale):
        """Check if the sale is for a home service"""
        if sale.appointment and sale.appointment.location == 'home':
            return True
        return False

    @staticmethod
    def _calculate_omar_expert_commission(sale):
        """
        Calculate Omar's commission for expert sessions/packages.
        Omar gets commission on ALL expert sessions/packages regardless of who performs them.
        - Expert sessions: 30 AED flat
        - Expert packages: 50 AED flat
        """
        from django.contrib.auth import get_user_model
        User = get_user_model()

        try:
            # Get Omar's user account by email
            omar_user = User.objects.get(email="<EMAIL>")
            omar_profile = TherapistProfile.objects.get(user=omar_user)
        except (User.DoesNotExist, TherapistProfile.DoesNotExist):
            return None

        if sale.sale_type == 'service':
            # Expert session: 30 AED flat
            commission_amount = Decimal('30.00')
        else:
            # Expert package: 50 AED flat
            commission_amount = Decimal('50.00')

        return CommissionEarning(
            sale=sale,
            therapist=omar_profile,  # Use omar_profile (TherapistProfile) instead of omar_user (User)
            amount=commission_amount,
            percentage_used=None,
            commission_rule=None,
            commission_type='omar_expert',
            is_primary=False,  # Secondary commission
            date_earned=sale.created_at.date()
        )

    @staticmethod
    def _calculate_omar_home_commission(sale):
        """
        Calculate Omar's commission for ALL home services regardless of who performs them.
        - Home sessions: 50% commission + 5% VAT
        - Home packages: 40% commission + 5% VAT
        """
        from django.contrib.auth import get_user_model
        User = get_user_model()

        try:
            # Get Omar's user account by email
            omar_user = User.objects.get(email="<EMAIL>")
            omar_profile = TherapistProfile.objects.get(user=omar_user)
        except (User.DoesNotExist, TherapistProfile.DoesNotExist):
            return None

        if sale.sale_type == 'service':
            # Home session: 50%
            commission_amount = (sale.total_price * Decimal('0.5')).quantize(Decimal('0.01'))
            percentage_used = Decimal('50.00')
        else:
            # Home package: 40%
            commission_amount = (sale.total_price * Decimal('0.4')).quantize(Decimal('0.01'))
            percentage_used = Decimal('40.00')

        # Add 5% VAT to percentage commissions
        commission_amount = CommissionCalculator._apply_vat_addition(commission_amount)

        return CommissionEarning(
            sale=sale,
            therapist=omar_profile,  # Use omar_profile (TherapistProfile) instead of omar_user (User)
            amount=commission_amount,
            percentage_used=percentage_used,
            commission_rule=None,
            commission_type='omar_home',
            is_primary=True,  # Primary commission for home services
            date_earned=sale.created_at.date()
        )

    @staticmethod
    def _update_therapist_stats(therapist):
        """
        Update therapist statistics after commission processing.
        Updates overall, yearly, and monthly stats.
        """
        from django.db.models import Sum, Count

        # Update overall stats
        stats, created = TherapistStats.objects.get_or_create(therapist=therapist)

        # Count sessions (only completed/checked-in appointments)
        total_sessions = Appointment.objects.filter(
            therapist=therapist,
            status__in=['check_in', 'completed']
        ).count()

        # Sum earnings
        earnings_sum = CommissionEarning.objects.filter(
            therapist=therapist
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0')

        # Update overall stats
        stats.total_sessions = total_sessions
        stats.total_earnings = earnings_sum
        stats.save()

        # Update yearly stats
        current_year = timezone.now().year
        year_stats, created = TherapistYearStats.objects.get_or_create(
            therapist=therapist,
            year=current_year
        )

        # Count yearly sessions (only completed/checked-in appointments)
        yearly_sessions = Appointment.objects.filter(
            therapist=therapist,
            date__year=current_year,
            status__in=['check_in', 'completed']
        ).count()

        # Sum yearly earnings
        yearly_earnings = CommissionEarning.objects.filter(
            therapist=therapist,
            date_earned__year=current_year
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0')

        # Update yearly stats
        year_stats.total_sessions = yearly_sessions
        year_stats.total_earnings = yearly_earnings
        year_stats.save()

        # Update monthly stats
        current_month = timezone.now().month
        month_stats, created = TherapistMonthStats.objects.get_or_create(
            therapist=therapist,
            year=current_year,
            month=current_month
        )

        # Count monthly sessions (only completed/checked-in appointments)
        monthly_sessions = Appointment.objects.filter(
            therapist=therapist,
            date__year=current_year,
            date__month=current_month,
            status__in=['check_in', 'completed']  # Only count completed sessions
        ).count()

        # Sum monthly earnings
        monthly_earnings = CommissionEarning.objects.filter(
            therapist=therapist,
            date_earned__year=current_year,
            date_earned__month=current_month
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0')

        # Update monthly stats
        month_stats.total_sessions = monthly_sessions
        month_stats.total_earnings = monthly_earnings
        month_stats.save()

        # Update month_stat field in CommissionEarning objects
        CommissionEarning.objects.filter(
            therapist=therapist,
            date_earned__year=current_year,
            date_earned__month=current_month,
            month_stat__isnull=True
        ).update(month_stat=month_stats)

        return stats

    @staticmethod
    def _is_expert_service(sale):
        """
        Determine if a sale is for an expert service or package
        """
        if sale.sale_type == 'service':
            # Check if any of the services in the appointment are expert services
            services = AppointmentService.objects.filter(appointment=sale.appointment)
            return any('expert' in service.service.name.lower() for service in services)
        else:
            # Check if the package is an expert package
            package = sale.package
            return package and 'expert' in package.name.lower()

    @staticmethod
    def _is_home_service(sale):
        """
        Determine if a sale is for a home service
        """
        # Check if appointment location is marked as home
        return sale.appointment.location == 'home'
