from decimal import Decimal
from django.db import transaction
from django.utils import timezone
from dateutil.relativedelta import relativedelta
import logging
from api.appointments.models import Sale, Appointment, AppointmentService
from api.staff.models import TherapistProfile
from .models import (
    CommissionProfile, CommissionRule, ManualCommission,
    CommissionEarning, TherapistStats, TherapistYearStats, TherapistMonthStats
)

# Set up logger for commission calculations
logger = logging.getLogger('commission_calculator')
logger.setLevel(logging.INFO)


class CommissionCalculator:
    """
    Service for calculating commissions based on sales and appointments.
    """

    @staticmethod
    def calculate_commissions(sale):
        """
        Calculate commissions for a sale, potentially creating multiple commission records
        if multiple people should receive commissions for the same sale.

        For package purchases (sale_type='package' with no appointment):
        - No commissions are calculated immediately
        - Commissions are calculated when sessions are completed

        For service sales and package sessions (sale_type='service' or sale with appointment):
        - Normal commission calculation applies
        """
        logger.info(f"   🔍 Analyzing sale for commission calculation...")

        # Check if this is a package purchase (no commission until sessions are completed)
        if sale.sale_type == 'package' and not sale.appointment:
            logger.info(f"   📦 Package purchase detected - no commission calculated until sessions are completed")
            return []

        # Get the therapist profile from the appointment
        if not sale.appointment:
            logger.error(f"   ❌ No appointment found for commission calculation")
            return []

        therapist_profile = sale.appointment.therapist

        # Debug: Check what type of object therapist_profile is
        logger.info(f"   🔍 Therapist profile object type: {type(therapist_profile)}")
        logger.info(f"   🔍 Therapist profile object value: {therapist_profile}")

        # Ensure we have a valid TherapistProfile object
        if not isinstance(therapist_profile, TherapistProfile):
            logger.error(f"   ❌ Invalid therapist profile object: {therapist_profile}")
            return []

        # Get the actual User object from the profile
        therapist = therapist_profile.user
        profile = therapist_profile

        logger.info(f"   👨‍⚕️ Therapist: {therapist.get_full_name()} (ID: {therapist.id})")
        logger.info(f"   📋 Profile: Freelancer={profile.is_freelancer}")

        # Check if service is eligible for commission
        if not CommissionCalculator._is_eligible_for_commission(sale):
            logger.info(f"   ❌ Sale not eligible for commission")
            return []

        logger.info(f"   ✅ Sale eligible for commission")
        commissions = []

        # Check if this is an expert service/package
        is_expert = CommissionCalculator._is_expert_service(sale)
        is_home = CommissionCalculator._is_home_service(sale)

        logger.info(f"   🎯 Service Type: Expert={is_expert}, Home={is_home}")

        # Omar gets commission on ALL expert sessions/packages regardless of who performs them
        if is_expert:
            logger.info(f"   🌟 Expert service detected - calculating Omar's commission...")
            omar_commission = CommissionCalculator._calculate_omar_expert_commission(sale)
            if omar_commission:
                commissions.append(omar_commission)
                logger.info(f"   ✅ Omar expert commission: {omar_commission.amount} AED")

        # Omar gets commission on ALL home services regardless of who performs them
        if is_home:
            logger.info(f"   🏠 Home service detected - calculating Omar's commission...")
            omar_home_commission = CommissionCalculator._calculate_omar_home_commission(sale)
            if omar_home_commission:
                commissions.append(omar_home_commission)
                logger.info(f"   ✅ Omar home commission: {omar_home_commission.amount} AED")

        # Get current session count for threshold checking
        current_year = sale.created_at.year
        current_month = sale.created_at.month

        # Count current monthly sessions for threshold checking
        monthly_sessions = Appointment.objects.filter(
            therapist=profile,
            date__year=current_year,
            date__month=current_month,
            status__in=['check_in', 'completed']
        ).count()

        logger.info(f"   📊 Month Stats: {monthly_sessions} sessions this month")

        # Calculate therapist commission (if not Omar and not home service, or if Omar and not home service)
        is_omar = profile.user.email == "<EMAIL>"  # TODO: Fetch this email from .env file

        logger.info(f"   👑 Is Omar: {is_omar}")

        # If therapist is freelancer, use freelancer rates
        if profile.is_freelancer:
            logger.info(f"   💼 Freelancer detected - calculating freelancer commission...")
            freelancer_commission = CommissionCalculator._calculate_freelancer_commission(sale, profile)
            commissions.append(freelancer_commission)
            logger.info(f"   ✅ Freelancer commission: {freelancer_commission.amount} AED")
        # For home services: only calculate therapist commission if therapist is NOT Omar
        elif is_home and not is_omar:
            logger.info(f"   🏠 Home service with non-Omar therapist - calculating standard commission...")
            therapist_commission = CommissionCalculator._calculate_standard_commission(sale, profile, monthly_sessions)
            if therapist_commission:
                commissions.append(therapist_commission)
                logger.info(f"   ✅ Therapist commission: {therapist_commission.amount} AED")
            else:
                logger.info(f"   ❌ No therapist commission (threshold not met or no rules)")
        # For non-home services: calculate therapist commission if not Omar
        elif not is_home and not is_omar:
            logger.info(f"   🏢 Regular service with non-Omar therapist - calculating standard commission...")
            therapist_commission = CommissionCalculator._calculate_standard_commission(sale, profile, monthly_sessions)
            if therapist_commission:
                commissions.append(therapist_commission)
                logger.info(f"   ✅ Therapist commission: {therapist_commission.amount} AED")
            else:
                logger.info(f"   ❌ No therapist commission (threshold not met or no rules)")
        else:
            logger.info(f"   ℹ️ No additional therapist commission (Omar performing home service)")

        return commissions

    @staticmethod
    def calculate_for_sale(sale):
        """
        Calculate and save commissions for a sale.
        This method is called by signals when a sale is created.
        """
        logger.info(f"🎯 Starting commission calculation for Sale ID: {sale.id}")
        logger.info(f"   Sale Details: Type={sale.sale_type}, Price={sale.total_price}, Payment={getattr(sale, 'payment_method', 'N/A')}")

        try:
            # Calculate commissions
            commissions = CommissionCalculator.calculate_commissions(sale)

            logger.info(f"   📊 Calculated {len(commissions)} commission(s) for Sale ID: {sale.id}")

            # Save commission earnings to database
            saved_count = 0
            for commission in commissions:
                if commission:  # Only save if commission is not None
                    commission.save()
                    saved_count += 1
                    logger.info(f"   ✅ Saved commission: {commission.therapist.user.get_full_name()} - {commission.amount} AED ({commission.commission_type})")

            logger.info(f"🎉 Commission calculation completed for Sale ID: {sale.id} - {saved_count} commissions saved")

            # Update therapist stats AFTER commissions are saved to get accurate earnings
            # Update stats for ALL therapists who received commissions (not just appointment therapist)
            therapists_to_update = set()

            # Add appointment therapist if exists
            if sale.appointment and sale.appointment.therapist:
                therapists_to_update.add(sale.appointment.therapist)

            # Add all therapists who received commissions from this sale
            for commission in commissions:
                if commission and commission.therapist:
                    therapists_to_update.add(commission.therapist)

            # Update stats for all affected therapists
            for therapist in therapists_to_update:
                CommissionCalculator._update_therapist_stats(therapist)
                logger.info(f"   📊 Updated therapist stats for {therapist.user.get_full_name()}")

        except Exception as e:
            # Log error but don't raise to avoid breaking sale creation
            logger.error(f"❌ Error calculating commission for sale {sale.id}: {e}")
            print(f"Error calculating commission for sale {sale.id}: {e}")

    @staticmethod
    def _calculate_standard_commission(sale, profile, monthly_sessions):
        """
        Calculate standard commission for a therapist based on commission rules.
        For package sales with rule_type='package', uses session-based calculation.

        Follows the priority logic:
        1. Check if total sessions meet minimum threshold
        2. Get rule with highest priority
        3. For package rules: Calculate based on session duration vs package duration
        4. Prioritize percentage commission over fixed amount
        5. Add 5% VAT to percentage commissions
        6. Use fixed amount if no percentage is set
        7. Calculate zero commission if neither is set
        """
        # Get the therapist's commission profile
        commission_profile = profile.commission_profiles.filter(
            is_active=True, is_default=True
        ).first()

        if not commission_profile:
            logger.info(f"      ❌ No active commission profile found")
            return None

        logger.info(f"      📋 Commission Profile: {commission_profile.name} (Threshold: {commission_profile.sessions_threshold})")

        # Find the applicable rule with highest priority
        rule = CommissionCalculator._find_applicable_rule(sale, commission_profile)

        if rule:
            logger.info(f"      📏 Rule found: {rule.name} (Priority: {rule.priority}, Min Sessions: {rule.min_sessions})")
        else:
            logger.info(f"      📏 No specific rule found, using profile base percentage: {commission_profile.base_percentage}%")

        # Check minimum sessions threshold
        if rule and rule.min_sessions > 0:
            # Check if therapist has met the rule's minimum sessions requirement
            if monthly_sessions < rule.min_sessions:
                logger.info(f"      ❌ Threshold not met: {monthly_sessions} < {rule.min_sessions} (rule threshold)")
                return None
            else:
                logger.info(f"      ✅ Rule threshold met: {monthly_sessions} >= {rule.min_sessions}")
        elif commission_profile.sessions_threshold > 0:
            # Check profile's base threshold if no rule-specific threshold
            if monthly_sessions < commission_profile.sessions_threshold:
                logger.info(f"      ❌ Threshold not met: {monthly_sessions} < {commission_profile.sessions_threshold} (profile threshold)")
                return None
            else:
                logger.info(f"      ✅ Profile threshold met: {monthly_sessions} >= {commission_profile.sessions_threshold}")
        else:
            logger.info(f"      ℹ️ No threshold requirements")

        # Calculate commission amount based on priority logic
        commission_amount = Decimal('0')
        percentage_used = None
        commission_type = 'standard'

        if rule:
            # Rule found - prioritize percentage over fixed amount
            if rule.percentage is not None and rule.percentage > 0:
                # Check if this is a package rule and we need session-based calculation
                if rule.rule_type == 'package' and sale.appointment:
                    # Session-based package commission calculation
                    logger.info(f"      📦 Package rule detected - calculating session-based commission")
                    base_price = CommissionCalculator._calculate_session_based_package_price(sale, rule)
                    logger.info(f"      💰 Session-based price: {base_price} AED")
                else:
                    # Standard calculation for non-package rules or package purchases
                    base_price = sale.total_price
                    if sale.discount_percentage and sale.discount_percentage > 0:
                        # Calculate original price before discount
                        discount_multiplier = Decimal('1') - (sale.discount_percentage / Decimal('100'))
                        base_price = sale.total_price / discount_multiplier

                commission_amount = (base_price * rule.percentage / Decimal('100')).quantize(Decimal('0.01'))
                percentage_used = rule.percentage

                # Add 5% VAT to percentage commissions
                commission_amount = CommissionCalculator._apply_vat_addition(commission_amount)

            elif rule.fixed_amount is not None and rule.fixed_amount > 0:
                # Use fixed amount commission (secondary priority)
                commission_amount = rule.fixed_amount
                percentage_used = None
            else:
                # Neither percentage nor fixed amount set - zero commission
                commission_amount = Decimal('0')
                percentage_used = None
        else:
            # No rule found - use profile base percentage if available
            if commission_profile.base_percentage > 0:
                base_price = sale.total_price
                if sale.discount_percentage and sale.discount_percentage > 0:
                    # Calculate original price before discount
                    discount_multiplier = Decimal('1') - (sale.discount_percentage / Decimal('100'))
                    base_price = sale.total_price / discount_multiplier

                commission_amount = (base_price * commission_profile.base_percentage / Decimal('100')).quantize(Decimal('0.01'))
                percentage_used = commission_profile.base_percentage

                # Add 5% VAT to percentage commissions
                commission_amount = CommissionCalculator._apply_vat_addition(commission_amount)
            else:
                # No commission rules or base percentage - zero commission
                return None

        # Subtract payment processing charges if applicable
        if commission_amount > 0 and hasattr(sale, 'payment_method'):
            if sale.payment_method == 'card':
                # 2.1% fee for card payments
                card_fee_rate = Decimal('0.021')
                card_fee = (sale.total_price * card_fee_rate).quantize(Decimal('0.01'))
                commission_amount -= card_fee
            elif sale.payment_method == 'link':
                # 3% fee for link payments
                link_fee_rate = Decimal('0.03')
                link_fee = (sale.total_price * link_fee_rate).quantize(Decimal('0.01'))
                commission_amount -= link_fee

        # Ensure commission amount is not negative
        if commission_amount < 0:
            commission_amount = Decimal('0')

        # Return None if commission is zero (no commission for this sale)
        if commission_amount == 0:
            return None

        return CommissionEarning(
            sale=sale,
            therapist=profile,  # Use profile (TherapistProfile) instead of profile.user (User)
            amount=commission_amount,
            percentage_used=percentage_used,
            commission_rule=rule,
            commission_type=commission_type,
            is_primary=True,
            date_earned=sale.created_at.date()
        )

    @staticmethod
    def _calculate_freelancer_commission(sale, profile):
        """
        Calculate commission for freelancers.
        For package sessions, uses session-based calculation.
        - Service sessions: freelancer_service_rate (usually 50%)
        - Package sessions: freelancer_package_rate (usually 40%) applied to session value
        """
        if sale.sale_type == 'service':
            rate = profile.freelancer_service_rate / 100
            commission_amount = (sale.total_price * rate).quantize(Decimal('0.01'))
        else:
            # Package sale - check if this is a session or package purchase
            if sale.appointment:
                # This is a session from a package - calculate based on session value
                logger.info(f"      📦 Freelancer package session - calculating session-based commission")
                session_price = CommissionCalculator._calculate_session_based_package_price(sale, None)
                rate = profile.freelancer_package_rate / 100
                commission_amount = (session_price * rate).quantize(Decimal('0.01'))
                logger.info(f"      🧮 Freelancer package session: {session_price} × {rate*100}% = {commission_amount} AED")
            else:
                # Package purchase - use full price (this shouldn't happen with new logic)
                rate = profile.freelancer_package_rate / 100
                commission_amount = (sale.total_price * rate).quantize(Decimal('0.01'))

        # Apply VAT addition for percentage-based freelancer commissions
        commission_amount = CommissionCalculator._apply_vat_addition(commission_amount)

        return CommissionEarning(
            sale=sale,
            therapist=profile,  # Use profile (TherapistProfile) instead of profile.user (User)
            amount=commission_amount,
            percentage_used=rate * 100,  # Use percentage_used instead of percentage
            commission_type='freelancer',
            is_primary=True,
            date_earned=sale.created_at.date()
        )

    @staticmethod
    def _process_manual_commission(manual_commission):
        """Process a manual commission record."""
        sale = manual_commission.sale
        therapist = manual_commission.therapist

        if manual_commission.amount:
            commission_amount = manual_commission.amount
            percentage_used = None
        else:
            # Calculate from percentage
            commission_amount = (sale.total_price * manual_commission.percentage / Decimal('100')).quantize(Decimal('0.01'))
            percentage_used = manual_commission.percentage

        # Get or create monthly stats
        sale_date = sale.created_at.date()
        sale_year = sale_date.year
        sale_month = sale_date.month

        month_stats, created = TherapistMonthStats.objects.get_or_create(
            therapist=therapist,
            year=sale_year,
            month=sale_month
        )

        # Create commission earning
        with transaction.atomic():
            earning = CommissionEarning.objects.create(
                therapist=therapist,
                sale=sale,
                amount=commission_amount,
                percentage_used=percentage_used,
                commission_rule=None,
                manual_commission=manual_commission,
                date_earned=sale_date,
                is_eligible=True,  # Manual commissions are always eligible
                month_stat=month_stats
            )

            # Update therapist statistics
            CommissionCalculator._update_therapist_stats(therapist)

            return earning

    @staticmethod
    def _is_eligible_for_commission(sale):
        """
        Check if the sale is eligible for commission.
        Excludes red light sessions, compression sessions, and consultations.
        Checks both appointment services and notes field for excluded services.
        """
        # First check service/package eligibility
        is_eligible = False

        if not sale.appointment:
            # This is a direct package sale, check package
            if sale.user_package or sale.shared_package or sale.unlimited_package:
                is_eligible = True
        else:
            # This is an appointment sale
            # Check if it's excluded service type in appointment services
            excluded_services = ["Red-Light Therapy", "Compression", "Assessment", "Consultation"]

            # Get appointment services
            services = AppointmentService.objects.filter(appointment=sale.appointment)

            # Check appointment services for exclusions
            if services.exists():
                if all(service.service.name in excluded_services for service in services):
                    is_eligible = False
                else:
                    is_eligible = True
            else:
                # No appointment services, check notes for commission-eligible duration
                commission_duration = CommissionCalculator._extract_commission_duration_from_notes(sale.appointment)
                is_eligible = commission_duration > 0

        # If not eligible based on service/package type, return False
        if not is_eligible:
            return False

        # If eligible, check monthly thresholds
        # This will be handled in the main calculate_for_sale method
        # since we need the therapist profile
        return True

    @staticmethod
    def _find_applicable_rule(sale, profile):
        """Find the most applicable commission rule for this sale."""
        # Check for specific service/package rules first (higher priority)
        rule = None

        if sale.sale_type == 'service' and sale.appointment:
            # Find service-specific rules
            services = AppointmentService.objects.filter(appointment=sale.appointment)
            for service in services:
                # Try to find a rule for this service in the profile
                service_rule = CommissionRule.objects.filter(
                    profile=profile,
                    rule_type='service',
                    service=service.service,
                    is_active=True
                ).order_by('-priority').first()

                if service_rule:
                    rule = service_rule
                    break

                # Try global service rule
                service_rule = CommissionRule.objects.filter(
                    profile=None,
                    rule_type='service',
                    service=service.service,
                    is_active=True
                ).order_by('-priority').first()

                if service_rule:
                    rule = service_rule
                    break

        elif sale.sale_type == 'package':
            # Find package-specific rules
            package = None

            if sale.user_package and sale.user_package.package_option:
                package = sale.user_package.package_option.package
            elif sale.shared_package and sale.shared_package.package_option:
                package = sale.shared_package.package_option.package
            elif sale.unlimited_package and sale.unlimited_package.package_option:
                package = sale.unlimited_package.package_option.package

            if package:
                # Try to find a rule for this package in the profile
                package_rule = CommissionRule.objects.filter(
                    profile=profile,
                    rule_type='package',
                    package=package,
                    is_active=True
                ).order_by('-priority').first()

                if package_rule:
                    rule = package_rule

                # Try global package rule
                if not rule:
                    package_rule = CommissionRule.objects.filter(
                        profile=None,
                        rule_type='package',
                        package=package,
                        is_active=True
                    ).order_by('-priority').first()

                    if package_rule:
                        rule = package_rule

        # If no specific rule found, try global rules
        if not rule:
            rule = CommissionRule.objects.filter(
                profile=profile,
                rule_type='global',
                is_active=True
            ).order_by('-priority').first()

            if not rule:
                # Try completely global rule
                rule = CommissionRule.objects.filter(
                    profile=None,
                    rule_type='global',
                    is_active=True
                ).order_by('-priority').first()

        return rule

    @staticmethod
    def _calculate_session_based_package_price(sale, rule):
        """
        Calculate the proportional price for a session based on package duration.
        Uses duration from appointment notes field, excluding red light and compression sessions.

        Formula: (Package Price ÷ Package Duration) × Session Duration

        Example:
        - Package: 615 AED for 100 minutes
        - Session: 20 minutes (from notes: "Stretching: 20 min")
        - Session Price: (615 ÷ 100) × 20 = 123 AED
        """
        if not sale.appointment:
            logger.warning(f"      ⚠️ No appointment found for session-based calculation, using full sale price")
            return sale.total_price

        # Get session duration from appointment notes (excluding non-commission services)
        session_duration = CommissionCalculator._extract_commission_duration_from_notes(sale.appointment)
        logger.info(f"      ⏱️ Commission-eligible session duration: {session_duration} minutes")

        # If no commission-eligible duration, return 0 (no commission)
        if session_duration == 0:
            logger.info(f"      💰 No commission-eligible duration - session price: 0 AED")
            return Decimal('0.00')

        # Get package details
        package_price = None
        package_duration = None

        if sale.user_package and sale.user_package.package_option:
            package_price = sale.user_package.package_option.price
            package_duration = sale.user_package.package_option.time
            logger.info(f"      📦 UserPackage: {package_price} AED for {package_duration} minutes")
        elif sale.shared_package and sale.shared_package.package_option:
            package_price = sale.shared_package.package_option.price
            package_duration = sale.shared_package.package_option.time
            logger.info(f"      📦 SharedPackage: {package_price} AED for {package_duration} minutes")
        elif sale.unlimited_package and sale.unlimited_package.package_option:
            package_price = sale.unlimited_package.package_option.price
            package_duration = sale.unlimited_package.package_option.time
            logger.info(f"      📦 UnlimitedPackage: {package_price} AED for {package_duration} minutes")

        if not package_price or not package_duration:
            logger.warning(f"      ⚠️ Could not determine package price/duration, using full sale price")
            return sale.total_price

        # Calculate price per minute
        price_per_minute = Decimal(str(package_price)) / Decimal(str(package_duration))
        logger.info(f"      💰 Price per minute: {price_per_minute:.4f} AED")

        # Calculate session-based price
        session_price = (price_per_minute * Decimal(str(session_duration))).quantize(Decimal('0.01'))
        logger.info(f"      🧮 Session price calculation: {price_per_minute:.4f} × {session_duration} = {session_price} AED")

        return session_price

    @staticmethod
    def _extract_commission_duration_from_notes(appointment):
        """
        Extract commission-eligible duration from appointment notes.
        Excludes red light therapy and compression sessions.

        NEW LOGIC: If no notes or no commission-eligible services found, return 0 (no commission).
        This enforces proper documentation of services to receive commission.

        Expected format in notes: "Service: XX min"
        Examples:
        - "Stretching: 30 min" → 30 minutes
        - "Massage: 45 min, Red-Light: 15 min" → 45 minutes (excludes red light)
        - "Compression: 20 min" → 0 minutes (excluded service)
        - "" (empty notes) → 0 minutes (no commission)
        - None (no notes) → 0 minutes (no commission)
        """
        import re

        if not appointment.notes or appointment.notes.strip() == "":
            logger.warning(f"      ⚠️ No notes found - no commission will be calculated (requires service documentation)")
            return 0

        logger.info(f"      📝 Parsing notes: {appointment.notes}")

        # Services that don't get commission
        excluded_services = [
            'red-light', 'red light', 'redlight',
            'compression', 'compress',
            'assessment', 'consultation'
        ]

        # Pattern to match "Service: XX min" format
        pattern = r'([^:,]+):\s*(\d+)\s*min'
        matches = re.findall(pattern, appointment.notes, re.IGNORECASE)

        if not matches:
            logger.warning(f"      ⚠️ No service duration pattern found in notes - no commission will be calculated")
            logger.info(f"      💡 Expected format: 'Service: XX min' (e.g., 'Massage: 30 min')")
            return 0

        total_commission_duration = 0

        for service_name, duration_str in matches:
            service_name = service_name.strip().lower()
            duration = int(duration_str)

            # Check if this service is excluded
            is_excluded = any(excluded in service_name for excluded in excluded_services)

            if is_excluded:
                logger.info(f"      ❌ Excluding {service_name}: {duration} min (non-commission service)")
            else:
                logger.info(f"      ✅ Including {service_name}: {duration} min")
                total_commission_duration += duration

        if total_commission_duration == 0:
            logger.warning(f"      ⚠️ No commission-eligible services found in notes - only excluded services documented")
            logger.info(f"      💡 All documented services were excluded (red light, compression, etc.)")
            return 0

        logger.info(f"      🧮 Total commission-eligible duration: {total_commission_duration} minutes")
        return total_commission_duration

    @staticmethod
    def _calculate_amount(sale, profile, rule):
        """
        Calculate commission amount based on rule or profile.
        Subtracts payment processing charges based on payment method.
        """
        # Special case for Omar (therapist ID will need to be configured)
        if profile.therapist.id == 1:  # Assuming Omar's ID is 1, adjust as needed
            return CommissionCalculator._calculate_omar_commission(sale, profile)

        # Calculate base commission amount
        commission_amount = Decimal('0')
        percentage_used = Decimal('0')

        if rule:
            # Prioritize percentage commission over fixed amount
            if rule.percentage is not None and rule.percentage > 0:
                # Apply percentage to sale price (priority)
                # Take into account any discount
                base_price = sale.total_price
                if sale.discount_percentage and sale.discount_percentage > 0:
                    # Calculate original price before discount
                    discount_multiplier = Decimal('1') - (sale.discount_percentage / Decimal('100'))
                    base_price = sale.total_price / discount_multiplier

                commission_amount = (base_price * rule.percentage / Decimal('100')).quantize(Decimal('0.01'))
                percentage_used = rule.percentage

                # Add 5% VAT to percentage commissions
                commission_amount = CommissionCalculator._apply_vat_addition(commission_amount)

            elif rule.fixed_amount is not None and rule.fixed_amount > 0:
                # Use fixed amount commission (secondary priority)
                commission_amount = rule.fixed_amount
                percentage_used = None
            else:
                # Neither percentage nor fixed amount set - zero commission
                commission_amount = Decimal('0')
                percentage_used = None
        # Fallback to profile base percentage
        elif profile.base_percentage > 0:
            base_price = sale.total_price
            if sale.discount_percentage and sale.discount_percentage > 0:
                # Calculate original price before discount
                discount_multiplier = Decimal('1') - (sale.discount_percentage / Decimal('100'))
                base_price = sale.total_price / discount_multiplier

            commission_amount = (base_price * profile.base_percentage / Decimal('100')).quantize(Decimal('0.01'))
            percentage_used = profile.base_percentage

            # Add 5% VAT to percentage commissions
            commission_amount = CommissionCalculator._apply_vat_addition(commission_amount)
        else:
            # No commission rules or base percentage - zero commission
            commission_amount = Decimal('0')
            percentage_used = None

        # Subtract payment processing charges
        if commission_amount > 0 and hasattr(sale, 'payment_method'):
            if sale.payment_method == 'card':
                # 2.1% fee for card payments
                card_fee_rate = Decimal('0.021')
                card_fee = (sale.total_price * card_fee_rate).quantize(Decimal('0.01'))
                commission_amount -= card_fee
            elif sale.payment_method == 'link':
                # 3% fee for link payments
                link_fee_rate = Decimal('0.03')
                link_fee = (sale.total_price * link_fee_rate).quantize(Decimal('0.01'))
                commission_amount -= link_fee

        # Ensure commission amount is not negative
        if commission_amount < 0:
            commission_amount = Decimal('0')

        return commission_amount, percentage_used

    @staticmethod
    def _apply_vat_addition(amount):
        """Add 5% VAT to the given amount for percentage-based commissions"""
        vat_rate = Decimal('0.05')
        vat_amount = (amount * vat_rate).quantize(Decimal('0.01'))
        return amount + vat_amount

    @staticmethod
    def _apply_vat_deduction(amount):
        """Deduct 5% VAT from the given amount (legacy method for specific cases)"""
        vat_rate = Decimal('0.05')
        vat_amount = (amount * vat_rate).quantize(Decimal('0.01'))
        return amount - vat_amount

    @staticmethod
    def _is_expert_service(sale):
        """Check if the sale is for an expert service or package"""
        if sale.appointment:
            # Check appointment services
            services = AppointmentService.objects.filter(appointment=sale.appointment)
            for service in services:
                if 'expert' in service.service.name.lower():
                    return True
        elif sale.user_package or sale.shared_package or sale.unlimited_package:
            # Check package name
            package = None
            if sale.user_package and sale.user_package.package_option:
                package = sale.user_package.package_option.package
            elif sale.shared_package and sale.shared_package.package_option:
                package = sale.shared_package.package_option.package
            elif sale.unlimited_package and sale.unlimited_package.package_option:
                package = sale.unlimited_package.package_option.package

            if package and 'expert' in package.name.lower():
                return True
        return False

    @staticmethod
    def _is_home_service(sale):
        """Check if the sale is for a home service"""
        if sale.appointment and sale.appointment.location == 'home':
            return True
        return False

    @staticmethod
    def _calculate_omar_expert_commission(sale):
        """
        Calculate Omar's commission for expert sessions/packages.
        Omar gets commission on ALL expert sessions/packages regardless of who performs them.
        - Expert sessions: 30 AED flat
        - Expert packages: 50 AED flat (but calculated per session for package appointments)
        """
        from django.contrib.auth import get_user_model
        User = get_user_model()

        try:
            # Get Omar's user account by email
            omar_user = User.objects.get(email="<EMAIL>")
            omar_profile = TherapistProfile.objects.get(user=omar_user)
        except (User.DoesNotExist, TherapistProfile.DoesNotExist):
            return None

        if sale.sale_type == 'service':
            # Expert session: 30 AED flat
            commission_amount = Decimal('30.00')
        else:
            # Expert package: Check if this is a session from a package or package purchase
            if sale.appointment:
                # This is a session from an expert package - calculate proportional commission
                logger.info(f"      📦 Expert package session detected - calculating session-based Omar commission")

                # Get session-based price for the expert package
                session_price = CommissionCalculator._calculate_session_based_package_price(sale, None)

                # Omar gets a percentage of the session value for expert packages
                # Using the same logic as the original 50 AED for full package
                # We need to calculate what percentage 50 AED represents of a typical expert package
                # For now, let's use a fixed rate per minute for expert packages
                session_duration = CommissionCalculator._extract_commission_duration_from_notes(sale.appointment)
                omar_rate_per_minute = Decimal('0.50')  # 50 AED for 100 minutes = 0.5 AED per minute
                commission_amount = (omar_rate_per_minute * Decimal(str(session_duration))).quantize(Decimal('0.01'))

                logger.info(f"      🧮 Omar expert package session: {omar_rate_per_minute} × {session_duration} = {commission_amount} AED")
            else:
                # Expert package purchase: 50 AED flat (original logic)
                commission_amount = Decimal('50.00')

        return CommissionEarning(
            sale=sale,
            therapist=omar_profile,  # Use omar_profile (TherapistProfile) instead of omar_user (User)
            amount=commission_amount,
            percentage_used=None,
            commission_rule=None,
            commission_type='omar_expert',
            is_primary=False,  # Secondary commission
            date_earned=sale.created_at.date()
        )

    @staticmethod
    def _calculate_omar_home_commission(sale):
        """
        Calculate Omar's commission for ALL home services regardless of who performs them.
        - Home sessions: 50% commission + 5% VAT
        - Home packages: 40% commission + 5% VAT
        """
        from django.contrib.auth import get_user_model
        User = get_user_model()

        try:
            # Get Omar's user account by email
            omar_user = User.objects.get(email="<EMAIL>")
            omar_profile = TherapistProfile.objects.get(user=omar_user)
        except (User.DoesNotExist, TherapistProfile.DoesNotExist):
            return None

        if sale.sale_type == 'service':
            # Home session: 50%
            commission_amount = (sale.total_price * Decimal('0.5')).quantize(Decimal('0.01'))
            percentage_used = Decimal('50.00')
        else:
            # Home package: 40%
            commission_amount = (sale.total_price * Decimal('0.4')).quantize(Decimal('0.01'))
            percentage_used = Decimal('40.00')

        # Add 5% VAT to percentage commissions
        commission_amount = CommissionCalculator._apply_vat_addition(commission_amount)

        return CommissionEarning(
            sale=sale,
            therapist=omar_profile,  # Use omar_profile (TherapistProfile) instead of omar_user (User)
            amount=commission_amount,
            percentage_used=percentage_used,
            commission_rule=None,
            commission_type='omar_home',
            is_primary=True,  # Primary commission for home services
            date_earned=sale.created_at.date()
        )

    @staticmethod
    def _update_therapist_stats(therapist):
        """
        Update therapist statistics after commission processing.
        Updates overall, yearly, and monthly stats.
        """
        from django.db.models import Sum, Count

        # Update overall stats
        stats, created = TherapistStats.objects.get_or_create(therapist=therapist)

        # Count sessions (only completed/checked-in appointments)
        total_sessions = Appointment.objects.filter(
            therapist=therapist,
            status__in=['check_in', 'completed']
        ).count()

        # Sum earnings
        earnings_sum = CommissionEarning.objects.filter(
            therapist=therapist
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0')

        # Update overall stats
        stats.total_sessions = total_sessions
        stats.total_earnings = earnings_sum
        stats.save()

        # Update yearly stats
        current_year = timezone.now().year
        year_stats, created = TherapistYearStats.objects.get_or_create(
            therapist=therapist,
            year=current_year
        )

        # Count yearly sessions (only completed/checked-in appointments)
        yearly_sessions = Appointment.objects.filter(
            therapist=therapist,
            date__year=current_year,
            status__in=['check_in', 'completed']
        ).count()

        # Sum yearly earnings
        yearly_earnings = CommissionEarning.objects.filter(
            therapist=therapist,
            date_earned__year=current_year
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0')

        # Update yearly stats
        year_stats.total_sessions = yearly_sessions
        year_stats.total_earnings = yearly_earnings
        year_stats.save()

        # Update monthly stats
        current_month = timezone.now().month
        month_stats, created = TherapistMonthStats.objects.get_or_create(
            therapist=therapist,
            year=current_year,
            month=current_month
        )

        # Count monthly sessions (only completed/checked-in appointments)
        monthly_sessions = Appointment.objects.filter(
            therapist=therapist,
            date__year=current_year,
            date__month=current_month,
            status__in=['check_in', 'completed']  # Only count completed sessions
        ).count()

        # Sum monthly earnings
        monthly_earnings = CommissionEarning.objects.filter(
            therapist=therapist,
            date_earned__year=current_year,
            date_earned__month=current_month
        ).aggregate(total=Sum('amount'))['total'] or Decimal('0')

        # Update monthly stats
        month_stats.total_sessions = monthly_sessions
        month_stats.total_earnings = monthly_earnings
        month_stats.save()

        # Update month_stat field in CommissionEarning objects
        CommissionEarning.objects.filter(
            therapist=therapist,
            date_earned__year=current_year,
            date_earned__month=current_month,
            month_stat__isnull=True
        ).update(month_stat=month_stats)

        return stats


