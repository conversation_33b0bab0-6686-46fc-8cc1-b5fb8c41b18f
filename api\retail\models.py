from django.db import models
from django.conf import settings
from django.utils import timezone
from django.core.validators import MinValueValidator
import uuid
from django.utils import timezone
from dateutil.relativedelta import relativedelta
from django.core.files.base import ContentFile

from weasyprint import HTML
from django.template.loader import render_to_string
import datetime
import base64
import os
from decimal import Decimal


class Product(models.Model):
    """
    Represents a product that can be sold in either of the studio locations.
    """

    CATEGORY_CHOICES = [
        ("REVIVE", "REVIVE"),
        ("HUMANTRA", "HUMANTRA"),
        ("RUSH_CLOTHING", "RUSH CLOTHING"),
        ("STRETCH_UP", "STRETCH UP PRODUCTS"),
    ]

    LOCATION_CHOICES = [
        ("A", "Studio Al Warqa Mall"),
        ("B", "Studio Al mizhar branch"),
    ]

    name = models.CharField(max_length=255)
    category = models.CharField(max_length=20, choices=CATEGORY_CHOICES)
    description = models.TextField(blank=True, null=True)
    price = models.DecimalField(max_digits=10, decimal_places=2)
    vat_percentage = models.DecimalField(
        max_digits=5, decimal_places=2, default=5.0, validators=[MinValueValidator(0)]
    )
    quantity_in_stock = models.PositiveIntegerField(default=0)
    location = models.CharField(max_length=4, choices=LOCATION_CHOICES)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} - {self.category} ({self.get_location_display()})"


class ProductSale(models.Model):
    """
    Represents a sales transaction that can include multiple products.
    """

    PAYMENT_METHOD_CHOICES = [
        ("CASH", "Cash"),
        ("CARD", "Card"),
        ("ONLINE_LINK", "Online Link"),
    ]

    STATUS_CHOICES = [
        ("COMPLETED", "Completed"),
        ("REFUNDED", "Refunded"),
        ("CANCELLED", "Cancelled"),
    ]

    LOCATION_CHOICES = [
        ("A", "Studio Al Warqa Mall"),
        ("B", "Studio Al mizhar branch"),
    ]

    invoice_number = models.CharField(max_length=20, unique=True, editable=False)
    customer = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="product_sales",
    )
    staff = models.ForeignKey(
        settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name="processed_sales"
    )
    subtotal_amount = models.DecimalField(max_digits=10, decimal_places=2)
    vat_amount = models.DecimalField(max_digits=10, decimal_places=2)
    total_amount = models.DecimalField(max_digits=10, decimal_places=2)
    payment_method = models.CharField(max_length=20, choices=PAYMENT_METHOD_CHOICES)
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default="COMPLETED")
    location = models.CharField(max_length=1, choices=LOCATION_CHOICES)
    notes = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    # Add invoice field
    invoice = models.FileField(upload_to="product_invoices/", null=True, blank=True)

    def save(self, *args, **kwargs):
        # Only generate invoice number if not set
        if not self.invoice_number:
            self.generate_invoice_number()

        # Save the model without generating the invoice
        super().save(*args, **kwargs)

    def generate_invoice_number(self):
        """Generate a unique invoice number using location-based format"""
        # Determine location code
        location_code = "AW" if self.location == "A" else "AM"

        # Get current year (2-digit)
        year = datetime.datetime.now().strftime("%y")

        # Count existing invoices for this year and location
        existing_count = ProductSale.objects.filter(
            invoice_number__startswith=f"PROD-{location_code}-{year}"
        ).count()

        # Generate sequential number (1-based, padded to 4 digits)
        seq_number = f"{existing_count + 1:04d}"

        # Create the invoice number
        self.invoice_number = f"PROD-{location_code}-{year}{seq_number}"

    def generate_invoice(self):
        """Generate a PDF invoice and save it to the invoice field"""
        try:
            # Prepare the context data
            context = self.get_invoice_context()

            # Render HTML template with context
            template_name = "invoice.html"  # Using existing template
            html_string = render_to_string(template_name, context)

            # Generate PDF from HTML
            html = HTML(string=html_string)
            pdf_bytes = html.write_pdf()

            # Save to the invoice field
            filename = f"{self.invoice_number}.pdf"
            self.invoice.save(filename, ContentFile(pdf_bytes), save=False)

            # Update just the invoice field to avoid recursive save
            self.__class__.objects.filter(pk=self.pk).update(invoice=self.invoice)

            return True
        except Exception as e:
            from utils.logging import logger

            logger.error(f"Error generating invoice for ProductSale:{self.id} - {str(e)}")
            return False

    def get_invoice_context(self):
        """Prepare the context data for the invoice template"""
        # Calculate VAT (5% of total) - important fix for subtotal calculation
        subtotal = self.total_amount / Decimal("1.05")  # Remove VAT from total
        vat_amount = self.total_amount - subtotal

        # Get product items
        items = []
        for item in self.sale_items.all():
            # Format the description with product name, unit price, and quantity
            item_description = f"{item.product.name} (AED {float(item.unit_price / Decimal('1.05') ):.2f} × {item.quantity} pieces)"
            items.append(
                {
                    "description": item_description,
                    "total": f"{float(item.total_price / Decimal('1.05') ):.2f}",
                }
            )
        # Get customer name
        customer_name = ""
        if self.customer:
            customer_name = (
                f"{self.customer.first_name} {self.customer.last_name}".strip()
            )
            if not customer_name:
                customer_name = self.customer.email

        # Get location description
        location_description = (
            "Studio Al Warqa Mall" if self.location == "A" else "Studio Al Mizhar Branch"
        )

        # Map payment method display names
        payment_method_display = {
            "CASH": "Cash",
            "CARD": "Card",
            "ONLINE_LINK": "Online Payment",
        }.get(self.payment_method, self.payment_method)

        # Prepare the context - matching the format expected by the template
        context = {
            # Invoice info
            "invoice_number": self.invoice_number,
            "payment_method": payment_method_display,
            "invoice_date": self.created_at.strftime("%d/%m/%Y"),
            "due_date": self.created_at.strftime("%d/%m/%Y"),
            "date_paid": self.created_at.strftime("%d/%m/%Y"),
            "is_paid": self.status == "COMPLETED",
            # Company and location
            "location": location_description,
            # Customer details
            "bill_to_name": customer_name,
            # Line items and calculations - use calculated values for subtotal
            "items": items,
            "sub_total": f"{float(subtotal):.2f}",
            "vat_amount": f"{float(vat_amount):.2f}",
            "grand_total": f"{float(self.total_amount):.2f}",
        }

        # Load logo as base64
        logo_path = os.path.join(settings.BASE_DIR, "static/images/logo.png")
        if os.path.exists(logo_path):
            with open(logo_path, "rb") as f:
                logo_data = base64.b64encode(f.read()).decode("utf-8")
                context["Logo"] = f"data:image/png;base64,{logo_data}"

        return context


class ProductSaleItem(models.Model):
    """
    Represents a single item within a product sale.
    """

    product_sale = models.ForeignKey(
        ProductSale, on_delete=models.CASCADE, related_name="sale_items"
    )
    product = models.ForeignKey(
        Product, on_delete=models.CASCADE, related_name="sale_items"
    )
    quantity = models.PositiveIntegerField(default=1)
    unit_price = models.DecimalField(max_digits=10, decimal_places=2)
    vat_amount = models.DecimalField(max_digits=10, decimal_places=2)
    total_price = models.DecimalField(max_digits=10, decimal_places=2)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return (
            f"{self.product.name} x{self.quantity} in {self.product_sale.invoice_number}"
        )


class CashRegister(models.Model):
    """
    Represents the daily cash register for each location.
    """

    LOCATION_CHOICES = [
        ("A", "Studio Al Warqa Mall"),
        ("B", "Studio Al mizhar branch"),
    ]

    date = models.DateField()
    current_balance = models.DecimalField(max_digits=10, decimal_places=2)
    location = models.CharField(max_length=1, choices=LOCATION_CHOICES)
    notes = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ["date", "location"]

    def __str__(self):
        return f"Cash Register - {self.get_location_display()} - {self.date}"


class CashWithdrawal(models.Model):
    """
    Represents a withdrawal from the cash register.
    """

    cash_register = models.ForeignKey(
        CashRegister, on_delete=models.CASCADE, related_name="withdrawals"
    )
    amount = models.DecimalField(
        max_digits=10, decimal_places=2, validators=[MinValueValidator(0)]
    )
    reason = models.TextField()
    staff = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="cash_withdrawals",
    )
    notes = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Withdrawal: {self.amount} AED from {self.cash_register}"


class CashDeposit(models.Model):
    """
    Represents a deposit to the cash register.
    """

    cash_register = models.ForeignKey(
        CashRegister, on_delete=models.CASCADE, related_name="deposits"
    )
    amount = models.DecimalField(
        max_digits=10, decimal_places=2, validators=[MinValueValidator(0)]
    )
    reason = models.TextField()
    staff = models.ForeignKey(
        settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name="cash_deposits"
    )
    notes = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Deposit: {self.amount} AED to {self.cash_register}"


class Expense(models.Model):
    """
    Represents an expense paid from the cash register.
    """

    CATEGORY_CHOICES = [
        ("SUPPLIES", "Supplies"),
        ("FOOD", "Food"),
        ("MAINTENANCE", "Maintenance"),
        ("UTILITIES", "Utilities"),
        ("TRANSPORTATION", "Transportation"),
        ("OTHER", "Other"),
    ]

    cash_register = models.ForeignKey(
        CashRegister, on_delete=models.CASCADE, related_name="expenses"
    )
    description = models.CharField(max_length=255)
    category = models.CharField(max_length=20, choices=CATEGORY_CHOICES)
    amount = models.DecimalField(
        max_digits=10, decimal_places=2, validators=[MinValueValidator(0)]
    )
    staff = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="recorded_expenses",
    )
    receipt_image = models.ImageField(
        upload_to="expense_receipts/", blank=True, null=True
    )
    notes = models.TextField(blank=True, null=True)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Expense: {self.amount} AED for {self.description}"


class DailySalesReport(models.Model):
    """Model for daily sales reports."""

    LOCATION_CHOICES = [
        ("A", "Studio Al Warqa Mall"),
        ("B", "Studio Al mizhar branch"),
    ]

    date = models.DateField()
    location = models.CharField(max_length=1, choices=LOCATION_CHOICES)

    # Sales amounts
    gross_sales_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    service_sales_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    product_sales_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    package_sales_amount = models.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )  # New field for package sales

    # Payment method breakdown
    cash_sales_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    card_sales_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    online_link_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)

    # Fees and taxes
    vat_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    card_charges_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    link_charges_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)

    # Cash register data
    starting_cash_balance = models.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )
    ending_cash_balance = models.DecimalField(max_digits=10, decimal_places=2, default=0)

    # Expenses and withdrawals
    expenses_total = models.DecimalField(max_digits=10, decimal_places=2, default=0)
    cash_withdrawals_total = models.DecimalField(
        max_digits=10, decimal_places=2, default=0
    )

    # Net result
    net_sales_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0)

    # Metadata
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name="daily_reports"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    email_sent = models.BooleanField(default=False)
    notes = models.TextField(blank=True)

    class Meta:
        unique_together = ("date", "location")

    def __str__(self):
        return f"Sales Report: {self.get_location_display()} - {self.date}"
