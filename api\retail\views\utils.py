from django.utils import timezone
import logging
from datetime import datetime
from decimal import Decimal, getcontext

# Set precision for Decimal calculations
getcontext().prec = 2

logger = logging.getLogger(__name__)


def format_date(date_string, input_format="%Y-%m-%d", output_format="%Y-%m-%d"):
    """Helper to handle date string formatting."""
    try:
        date_obj = datetime.strptime(date_string, input_format).date()
        return date_obj.strftime(output_format)
    except ValueError:
        logger.error(f"Invalid date format: {date_string}")
        return None


def calculate_vat_amount(amount, vat_percentage=5):
    """Calculate VAT amount for a given price."""
    return Decimal(str(amount)) * (Decimal(str(vat_percentage)) / Decimal("100"))


def calculate_payment_charges(amount, payment_method):
    """Calculate payment processor charges."""
    amount_decimal = Decimal(str(amount))

    if payment_method == "CARD":
        return amount_decimal * Decimal("0.021")  # 2.1%
    elif payment_method == "ONLINE_LINK":
        return amount_decimal * Decimal("0.03")  # 3%
    return Decimal("0")  # No charges for cash


def get_today_date():
    """Return today's date in YYYY-MM-DD format."""
    return timezone.now().date().isoformat()


def generate_invoice_number(location_prefix, count):
    """Generate a formatted invoice number."""
    today = timezone.now().strftime("%Y%m%d")
    return f"{location_prefix}-{today}-{count:04d}"
