# yourapp/management/commands/copy_invoices_by_date.py

import os
import shutil
from datetime import datetime
from django.core.management.base import BaseCommand
from django.utils.timezone import make_aware
from django.conf import settings
from api.appointments.models import Sale
from api.retail.models import ProductSale  # Added import for ProductSale

class Command(BaseCommand):
    help = 'Filter sales by date range, get invoice numbers, and copy invoices to a new folder'

    def add_arguments(self, parser):
        parser.add_argument('--start_date', type=str, required=True, 
                            help='Start date in YYYY-MM-DD format')
        parser.add_argument('--end_date', type=str, required=True, 
                            help='End date in YYYY-MM-DD format')
        parser.add_argument('--folder_name', type=str, required=True,
                            help='Name of the folder to store invoices (e.g., "february")')

    def handle(self, *args, **options):
        # Parse date strings to datetime objects
        try:
            start_date = datetime.strptime(options['start_date'], '%Y-%m-%d')
            end_date = datetime.strptime(options['end_date'], '%Y-%m-%d')
            
            # Make start_date start at 00:00:00
            start_date = make_aware(datetime(start_date.year, start_date.month, start_date.day, 0, 0, 0))
            
            # Make end_date end at 23:59:59
            end_date = make_aware(datetime(end_date.year, end_date.month, end_date.day, 23, 59, 59))
            
        except ValueError:
            self.stdout.write(self.style.ERROR('Invalid date format. Use YYYY-MM-DD'))
            return

        # Create target directory if it doesn't exist
        folder_name = options['folder_name']
        target_dir = os.path.join(settings.MEDIA_ROOT, folder_name, 'all invoices')
        os.makedirs(target_dir, exist_ok=True)
        
        # Track overall stats
        total_copied = 0
        total_skipped = 0
        all_invoice_numbers = []
        
        # Process Service Sales (from api.appointment.models.Sale)
        self.stdout.write(self.style.SUCCESS('\n=== Processing Service Sales ==='))
        service_copied, service_skipped, service_invoice_numbers = self._process_model(
            Sale,
            start_date,
            end_date,
            target_dir,
            'Service Sale'
        )
        total_copied += service_copied
        total_skipped += service_skipped
        all_invoice_numbers.extend(service_invoice_numbers)
        
        # Process Product Sales (from api.retail.models.ProductSale)
        self.stdout.write(self.style.SUCCESS('\n=== Processing Product Sales ==='))
        product_copied, product_skipped, product_invoice_numbers = self._process_model(
            ProductSale,
            start_date,
            end_date,
            target_dir,
            'Product Sale'
        )
        total_copied += product_copied
        total_skipped += product_skipped
        all_invoice_numbers.extend(product_invoice_numbers)
        
        # Overall summary
        self.stdout.write(self.style.SUCCESS('\n=== Overall Summary ==='))
        self.stdout.write(f"- Total service sales invoices: {len(service_invoice_numbers)}")
        self.stdout.write(f"- Total product sales invoices: {len(product_invoice_numbers)}")
        self.stdout.write(f"- Total invoices found: {len(all_invoice_numbers)}")
        self.stdout.write(f"- Successfully copied: {total_copied}")
        self.stdout.write(f"- Skipped (missing files): {total_skipped}")
        self.stdout.write(f"- Target directory: {target_dir}")
        
        # List all invoice numbers
        self.stdout.write(self.style.SUCCESS('\nAll Invoice numbers:'))
        for idx, invoice_num in enumerate(all_invoice_numbers, 1):
            self.stdout.write(f"{idx}. {invoice_num}")
    
    def _process_model(self, model_class, start_date, end_date, target_dir, model_name):
        """Helper method to process sales from a specific model"""
        # Filter sales by date range (inclusive)
        sales = model_class.objects.filter(
            created_at__gte=start_date,
            created_at__lte=end_date,
            invoice__isnull=False  # Only include sales with invoices
        ).order_by('created_at')

        # Count total sales with invoices
        total_count = sales.count()
        self.stdout.write(f'Found {total_count} {model_name}s with invoices in date range')

        # Copy invoice files
        copied_count = 0
        skipped_count = 0
        invoice_numbers = []
        
        for sale in sales:
            if sale.invoice and os.path.exists(sale.invoice.path):
                # Get invoice file name and number
                invoice_file_name = os.path.basename(sale.invoice.path)
                invoice_number = getattr(sale, 'invoice_number', None) or f"no-invoice-number-{sale.id}"
                invoice_numbers.append(f"{model_name}_{invoice_number}")
                
                # Create a unique destination filename to avoid overwriting
                # in case product sales and service sales have same filenames
                file_base, file_ext = os.path.splitext(invoice_file_name)
                dest_filename = f"{file_base}_{model_name.replace(' ', '_')}{file_ext}"
                dest_path = os.path.join(target_dir, dest_filename)
                
                try:
                    # Copy the file
                    shutil.copy2(sale.invoice.path, dest_path)
                    copied_count += 1
                    self.stdout.write(f"Copied {model_name} invoice {invoice_number} to {dest_path}")
                except Exception as e:
                    self.stdout.write(self.style.ERROR(f"Error copying {model_name} invoice {invoice_number}: {str(e)}"))
                    skipped_count += 1
            else:
                skipped_count += 1
        
        return copied_count, skipped_count, invoice_numbers