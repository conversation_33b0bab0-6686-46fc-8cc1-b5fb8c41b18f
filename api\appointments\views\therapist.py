from datetime import datetime, timed<PERSON>ta
import calendar
from django.db.models import Q, Sum
from django.utils.dateparse import parse_date
from rest_framework.exceptions import ValidationError
from rest_framework import status
from rest_framework import generics
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from rest_framework.pagination import PageNumberPagination
from rest_framework.views import APIView
from django.shortcuts import get_object_or_404
from django.contrib.auth import get_user_model
from rest_framework.filters import SearchFilter
from django.http import HttpResponse
from django_filters.rest_framework import DjangoFilterBackend
from api.appointments.models import (
    Appointment,
)
from api.services.models import Service, ServiceDuration, PackageOption
from api.appointments.serializers import (
    AppointmentSerializer,
    UserPackageSerializer,
    SharedPackageSerializer,
    UnlimitedPackageSerializer,
    AddMinutesToAppointmentSerializer,
    AppointmentAdditionalServiceSerializer,
    AppointmentAdditionalService,
    SharedPackageUserSerializer,
    PackageUsageSerializer,
    DiscountSerializer,
    AdminRewardSerializer,
    RewardBalanceSerializer,
)


from api.core.permissions import IsOwnerOrReceptionist
from api.staff.models import TherapistProfile, WorkingHour
from api.authentication.serializers import UserProfileSerializer
import traceback
from decimal import Decimal
from rest_framework.exceptions import NotFound, APIException, PermissionDenied
from api.staff.serializers import (
    TherapistProfileOptimizedSerializer,
)
from django.utils import timezone
import csv

User = get_user_model()

from django.template.loader import render_to_string
from django.utils.html import strip_tags
from django.core.mail import send_mail
from django.conf import settings

# Constants
MIN_ACTIVE_MINUTES = 0
BREAK_TIME = 10  # minutes break between appointments
NEW_APPOINTMENT_BREAK = 10  # minutes

from utils.logging import api_logger, log_request_data, log_response_data, log_error


class TherapistProfileView(APIView):
    """
    API view for retrieving a therapist's profile with all their data.
    Only owner and receptionist roles can access this endpoint.
    """

    permission_classes = [IsAuthenticated, IsOwnerOrReceptionist]

    def get(self, request, therapist_id):
        log_request_data(request, f"📥 Therapist Profile Request for ID:{therapist_id}")
        api_logger.info(f"🔍 Retrieving therapist profile for ID:{therapist_id}")

        try:
            # Get therapist details with optimized serializer
            therapist_profile = get_object_or_404(TherapistProfile, id=therapist_id)
            therapist_user = therapist_profile.user
            api_logger.debug(f"Found therapist profile for user:{therapist_user.id}")
            therapist_serializer = TherapistProfileOptimizedSerializer(therapist_profile)

            # Get recent appointments
            api_logger.debug(f"Fetching recent appointments for therapist:{therapist_id}")
            recent_appointments = Appointment.objects.filter(
                therapist=therapist_profile
            ).order_by("-date", "-time")[:10]

            # Get statistics
            api_logger.debug(f"Calculating statistics for therapist:{therapist_id}")
            total_appointments = Appointment.objects.filter(
                therapist=therapist_profile
            ).count()

            # Get last appointment
            last_appointment = (
                Appointment.objects.filter(therapist=therapist_profile)
                .order_by("-date", "-time")
                .first()
            )

            # Prepare response data
            response_data = {
                **therapist_serializer.data,
                "stats": {
                    "total_appointments": total_appointments,
                    "last_appointment": (
                        {
                            "date": (
                                last_appointment.date.isoformat()
                                if last_appointment
                                else None
                            ),
                            "time": (
                                last_appointment.time.isoformat()
                                if last_appointment
                                else None
                            ),
                            "id": last_appointment.id if last_appointment else None,
                        }
                        if last_appointment
                        else None
                    ),
                },
                "recent_appointments": AppointmentSerializer(
                    recent_appointments, many=True
                ).data,
            }

            api_logger.info(
                f"✅ Successfully retrieved therapist profile for ID:{therapist_id}"
            )
            response = Response(response_data, status=status.HTTP_200_OK)
            log_response_data(
                response, f"📤 Therapist Profile Response for ID:{therapist_id}"
            )
            return response

        except Exception as e:
            log_error(
                e,
                f"Retrieving therapist profile for ID:{therapist_id}",
                log_full_trace=True,
            )
            return Response(
                {"detail": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class TherapistAppointmentsView(APIView):
    """
    API view for retrieving a therapist's appointments with date range filtering
    and CSV export functionality.
    Only owner and receptionist roles can access this endpoint.
    """

    permission_classes = [IsAuthenticated, IsOwnerOrReceptionist]

    def get(self, request, therapist_id):
        """
        Get appointments within a date range with optional filtering.

        Query params:
        - start_date: Start date (YYYY-MM-DD)
        - end_date: End date (YYYY-MM-DD)
        - export: Set to 'csv' to export as CSV
        - status: Filter by appointment status
        """
        log_request_data(
            request, f"📥 Therapist Appointments Request for ID:{therapist_id}"
        )
        api_logger.info(f"🔍 Retrieving appointments for therapist ID:{therapist_id}")

        try:
            # Get query parameters
            start_date = request.query_params.get("start_date")
            end_date = request.query_params.get("end_date")
            export_format = request.query_params.get("export")
            status_filter = request.query_params.get("status")

            # Default to last 30 days if no dates provided
            if not start_date:
                start_date = (timezone.now() - timedelta(days=30)).date().isoformat()
            if not end_date:
                end_date = timezone.now().date().isoformat()

            api_logger.debug(
                f"Date range: {start_date} to {end_date}, Status filter: {status_filter or 'None'}"
            )

            # Get therapist
            therapist_profile = get_object_or_404(TherapistProfile, id=therapist_id)
            api_logger.debug(f"Found therapist profile for ID:{therapist_id}")

            # Build the query
            query = (
                Q(date__gte=start_date)
                & Q(date__lte=end_date)
                & Q(therapist=therapist_profile)
            )

            # Add status filter if provided
            if status_filter:
                query &= Q(status=status_filter)

            # Get appointments
            appointments = Appointment.objects.filter(query).order_by("-date", "-time")
            appointment_count = appointments.count()
            api_logger.info(
                f"Found {appointment_count} appointments for therapist ID:{therapist_id}"
            )

            # Export to CSV if requested
            if export_format == "csv":
                api_logger.info(
                    f"🔄 Exporting {appointment_count} appointments to CSV for therapist ID:{therapist_id}"
                )
                return self._export_to_csv(appointments, therapist_id)

            # Regular JSON response
            appointments_serializer = AppointmentSerializer(appointments, many=True)

            # Count total appointments and sum of total price
            total_count = appointment_count
            total_price = (
                appointments.aggregate(Sum("total_price"))["total_price__sum"] or 0
            )
            api_logger.debug(f"Total price sum: {total_price}")

            response_data = {
                "appointments": appointments_serializer.data,
                "meta": {
                    "total_count": total_count,
                    "total_price": float(total_price),
                    "start_date": start_date,
                    "end_date": end_date,
                },
            }

            api_logger.info(
                f"✅ Successfully retrieved appointments for therapist ID:{therapist_id}"
            )
            response = Response(response_data, status=status.HTTP_200_OK)
            log_response_data(
                response, f"📤 Therapist Appointments Response for ID:{therapist_id}"
            )
            return response

        except Exception as e:
            log_error(
                e,
                f"Retrieving appointments for therapist ID:{therapist_id}",
                log_full_trace=True,
            )
            return Response(
                {"detail": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def _export_to_csv(self, appointments, therapist_id):
        """
        Helper method to export appointments to CSV
        """
        api_logger.info(f"🔄 Creating CSV export for therapist ID:{therapist_id}")

        response = HttpResponse(content_type="text/csv")

        # Set filename with therapist name if possible
        filename = "therapist_appointments"
        try:
            therapist_user = User.objects.get(id=therapist_id)
            filename = (
                f"appointments_{therapist_user.first_name}_{therapist_user.last_name}"
            )
            api_logger.debug(
                f"Using therapist name for filename: {therapist_user.first_name} {therapist_user.last_name}"
            )
        except User.DoesNotExist:
            filename = f"appointments_therapist_{therapist_id}"
            api_logger.warning(
                f"Could not find user for therapist ID:{therapist_id}, using generic filename"
            )

        filename += f"_{timezone.now().strftime('%Y%m%d')}.csv"
        response["Content-Disposition"] = f'attachment; filename="{filename}"'
        api_logger.debug(f"Setting filename to: {filename}")

        # Create CSV writer
        writer = csv.writer(response)

        # Write headers
        writer.writerow(
            [
                "ID",
                "Date",
                "Time",
                "Customer",
                "Email",
                "Phone",
                "Service",
                "Duration",
                "Price",
                "Status",
                "Location",
                "Reward Points",
            ]
        )

        # Write data rows
        row_count = 0
        for appointment in appointments:
            # Prepare services list as comma-joined string
            services = ", ".join(
                [
                    f"{service.service.name} ({service.duration}min)"
                    for service in appointment.appointment_services.all()
                ]
            )

            writer.writerow(
                [
                    appointment.id,
                    appointment.date,
                    appointment.time,
                    f"{appointment.customer.first_name} {appointment.customer.last_name}",
                    appointment.customer.email,
                    appointment.customer.phone_number,
                    services or "Package Service",
                    appointment.total_duration,
                    appointment.total_price,
                    appointment.status,
                    appointment.location,
                    (
                        appointment.reward.points
                        if hasattr(appointment, "reward") and appointment.reward
                        else 0
                    ),
                ]
            )
            row_count += 1

        api_logger.info(
            f"✅ Successfully exported {row_count} appointments to CSV for therapist ID:{therapist_id}"
        )
        return response
