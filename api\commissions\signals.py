from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver
from django.db import transaction
from api.appointments.models import Sale
from .services import CommissionCalculator
from .models import CommissionEarning
import logging

logger = logging.getLogger('commission_calculator')

# Store original sale data before updates to detect changes
_sale_original_data = {}


@receiver(pre_save, sender=Sale)
def store_original_sale_data(sender, instance, **kwargs):
    """
    Store original sale data before save to detect changes in post_save.
    """
    if instance.pk:  # Only for existing instances (updates)
        try:
            original = Sale.objects.get(pk=instance.pk)
            _sale_original_data[instance.pk] = {
                'total_price': original.total_price,
                'discount_percentage': original.discount_percentage,
                'payment_method': getattr(original, 'payment_method', None),
                'sale_type': original.sale_type,
                'appointment_id': original.appointment_id,
                'user_package_id': original.user_package_id,
                'shared_package_id': getattr(original, 'shared_package_id', None),
                'unlimited_package_id': getattr(original, 'unlimited_package_id', None),
            }
        except Sale.DoesNotExist:
            # Sale doesn't exist yet, this is a creation
            pass


@receiver(post_save, sender=Sale)
def calculate_commission_on_sale(sender, instance, created, **kwargs):
    """
    Calculate commission when a sale is created or updated.
    For updates, only recalculate if commission-affecting fields changed.
    """
    if created:
        # Calculate commission for new sales
        logger.info(f"🆕 New sale created (ID: {instance.id}) - calculating commissions")
        CommissionCalculator.calculate_for_sale(instance)
    else:
        # Handle sale updates - check if commission-affecting fields changed
        logger.info(f"🔄 Sale updated (ID: {instance.id}) - checking if commission recalculation needed")

        try:
            needs_recalculation = _check_if_commission_recalculation_needed(instance)

            if needs_recalculation:
                logger.info(f"   ✅ Commission-affecting fields changed - recalculating")
                _handle_sale_update(instance)
            else:
                logger.info(f"   ℹ️ No commission-affecting fields changed - skipping recalculation")

        except Exception as e:
            logger.error(f"❌ Error handling sale update for Sale ID {instance.id}: {e}")
        finally:
            # Clean up stored data
            _sale_original_data.pop(instance.pk, None)


def _check_if_commission_recalculation_needed(sale_instance):
    """
    Check if commission recalculation is needed based on field changes.
    Returns True if commission-affecting fields have changed.
    """
    original_data = _sale_original_data.get(sale_instance.pk)

    if not original_data:
        # No original data stored, assume recalculation is needed for safety
        logger.info(f"   ⚠️ No original data found - assuming recalculation needed")
        return True

    # Fields that affect commission calculation
    commission_affecting_fields = [
        'total_price',
        'discount_percentage',
        'payment_method',
        'sale_type',
        'appointment_id',
        'user_package_id',
        'shared_package_id',
        'unlimited_package_id',
    ]

    changes_detected = []

    for field in commission_affecting_fields:
        original_value = original_data.get(field)
        current_value = getattr(sale_instance, field, None)

        if original_value != current_value:
            changes_detected.append(f"{field}: {original_value} → {current_value}")

    if changes_detected:
        logger.info(f"   📋 Commission-affecting changes detected:")
        for change in changes_detected:
            logger.info(f"      • {change}")
        return True

    return False


def _handle_sale_update(sale_instance):
    """
    Handle commission recalculation for sale updates.
    Deletes existing commissions and recalculates new ones.
    """
    logger.info(f"🔄 Recalculating commissions for updated Sale ID: {sale_instance.id}")

    with transaction.atomic():
        # Step 1: Delete existing commission records for this sale
        existing_commissions = CommissionEarning.objects.filter(sale=sale_instance)
        commission_count = existing_commissions.count()

        if commission_count > 0:
            logger.info(f"   🗑️ Deleting {commission_count} existing commission records")
            existing_commissions.delete()

        # Step 2: Recalculate and create new commissions
        logger.info(f"   🧮 Recalculating commissions for updated sale")
        CommissionCalculator.calculate_for_sale(sale_instance)

        logger.info(f"✅ Commission recalculation completed for Sale ID: {sale_instance.id}")
