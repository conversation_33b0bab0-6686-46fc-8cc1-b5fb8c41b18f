from django.core.management.base import BaseCommand
from django.db import transaction
from django.db.models import OuterRef, Exists
from django.utils import timezone
from decimal import Decimal

# Import your models here - adjust import paths as needed
from api.appointments.models import Appointment, Sale


class Command(BaseCommand):
    help = "Syncs checked-in appointments with sales objects, creating sales records for appointments that need them"

    def add_arguments(self, parser):
        parser.add_argument(
            "--dry-run",
            action="store_true",
            dest="dry_run",
            help="Perform a dry run without making actual changes",
        )
        parser.add_argument(
            "--since-days",
            type=int,
            dest="since_days",
            default=90,
            help="Process appointments from the last N days (default: 90)",
        )

    def handle(self, *args, **options):
        dry_run = options["dry_run"]
        since_days = options["since_days"]
        Sale.objects.filter().delete()
        self.stdout.write(
            self.style.NOTICE(
                f"{'DRY RUN: ' if dry_run else ''}Looking for checked-in appointments without sales from the last {since_days} days"
            )
        )

        # Calculate the date threshold
        date_threshold = timezone.now().date() - timezone.timedelta(days=since_days)

        # Find appointments that are checked in but have no associated sales
        appointments_without_sales = self._get_appointments_without_sales(date_threshold)

        count = appointments_without_sales.count()
        self.stdout.write(
            self.style.SUCCESS(f"Found {count} appointments requiring sales records")
        )

        if count == 0:
            self.stdout.write(
                self.style.SUCCESS("No appointments need processing. Exiting.")
            )
            return

        if dry_run:
            self._display_appointments(appointments_without_sales)
            self.stdout.write(
                self.style.SUCCESS("Dry run completed. No changes were made.")
            )
            return

        # Process each appointment and create sales
        created_count = self._create_sales_for_appointments(appointments_without_sales)

        self.stdout.write(
            self.style.SUCCESS(
                f"Successfully created {created_count} sales records for appointments"
            )
        )

    def _get_appointments_without_sales(self, date_threshold):
        """Find appointments that are checked in but have no associated sales."""
        return (
            Appointment.objects.filter(
                # Only checked-in appointments
                status="check_in",
                # Only appointments after the threshold date
                date__gte=date_threshold,
                # No existing sale references this appointment
            )
            .exclude(Exists(Sale.objects.filter(appointment=OuterRef("pk"))))
            .select_related("customer", "therapist", "package_option", "discount")
        )

    def _display_appointments(self, appointments):
        """Display the appointments that would be processed (for dry run)."""
        self.stdout.write(
            self.style.NOTICE("The following appointments would be processed:")
        )
        for idx, appointment in enumerate(appointments, 1):
            self.stdout.write(
                f"{idx}. {appointment} - {appointment.date} - "
                f"Customer: {appointment.customer.email} - "
                f"Status: {appointment.status} - "
                f"Price: {appointment.total_price}"
            )

    def _create_sales_for_appointments(self, appointments):
        """Create sales records for appointments in a transactional manner."""
        created_count = 0
        errors = []

        for appointment in appointments:
            try:
                with transaction.atomic():
                    # Check one more time inside transaction to prevent race conditions
                    if Sale.objects.filter(appointment=appointment).exists():
                        self.stdout.write(
                            self.style.WARNING(
                                f"Skipping appointment {appointment.id}: Sale already exists"
                            )
                        )
                        continue

                    # Create the sale record
                    sale = Sale(
                        user=appointment.customer,
                        sale_type="service",
                        appointment=appointment,
                        total_price=appointment.total_price,
                        discount=appointment.discount,
                        location=appointment.location,
                        discount_percentage=appointment.discount_percentage
                        or Decimal("0.00"),
                    )

                    # If appointment has package-related fields, copy them to the sale
                    if appointment.package_option:
                        sale.package_option = appointment.package_option

                    # Save the sale
                    sale.save()
                    created_count += 1

                    self.stdout.write(
                        self.style.SUCCESS(
                            f"Created sale #{sale.id} for appointment #{appointment.id}"
                        )
                    )

            except Exception as e:
                error_msg = (
                    f"Error creating sale for appointment #{appointment.id}: {str(e)}"
                )
                errors.append(error_msg)
                self.stdout.write(self.style.ERROR(error_msg))

        if errors:
            self.stdout.write(
                self.style.ERROR(
                    f"Encountered {len(errors)} errors during processing. "
                    f"Created {created_count} sales successfully."
                )
            )

        return created_count
