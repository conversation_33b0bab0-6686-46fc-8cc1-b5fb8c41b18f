# helpers/daily_sales.py
from decimal import Decimal
from django.db.models import Q, Sum
from django.utils import timezone

from api.retail.models import (
    DailySalesReport,
    CashWithdrawal,
    Expense,
    ProductSale,
)
from api.appointments.models import Sale


def calculate_sales_data(user, date, location, cash_register):
    """Calculate sales data and create or update a DailySalesReport for a specific date and location"""
    from decimal import Decimal
    from django.db.models import Q, Sum
    from django.utils import timezone

    # Get all product sales for today at this location
    product_sales = ProductSale.objects.filter(
        created_at__date=date, location=location, status="COMPLETED"
    )

    product_sales_amount = (
        product_sales.aggregate(total=Sum("total_amount"))["total"] or 0
    )

    # Get all Sales (service + package) for today at this location
    all_sales = Sale.objects.filter(created_at__date=date, location=location)

    # Service sales - filter from all_sales
    service_sales = all_sales.filter(sale_type="service")
    service_sales_amount = service_sales.aggregate(total=Sum("total_price"))["total"] or 0

    # Package sales - filter from all_sales
    package_sales = all_sales.filter(sale_type="package")
    package_sales_amount = package_sales.aggregate(total=Sum("total_price"))["total"] or 0

    # Total gross sales
    gross_sales = service_sales_amount + package_sales_amount + product_sales_amount

    # VAT amount (5%)
    vat_amount = gross_sales * Decimal("0.05")

    # Payment method breakdown for service and package sales (Sale model)
    cash_service_sales = (
        all_sales.filter(payment_method="cash").aggregate(total=Sum("total_price"))[
            "total"
        ]
        or 0
    )

    card_service_sales = (
        all_sales.filter(payment_method="card").aggregate(total=Sum("total_price"))[
            "total"
        ]
        or 0
    )

    link_service_sales = (
        all_sales.filter(payment_method="link").aggregate(total=Sum("total_price"))[
            "total"
        ]
        or 0
    )

    # Payment method breakdown for product sales
    cash_product_sales = (
        product_sales.filter(payment_method="CASH").aggregate(total=Sum("total_amount"))[
            "total"
        ]
        or 0
    )

    card_product_sales = (
        product_sales.filter(payment_method="CARD").aggregate(total=Sum("total_amount"))[
            "total"
        ]
        or 0
    )

    online_product_sales = (
        product_sales.filter(payment_method="ONLINE_LINK").aggregate(
            total=Sum("total_amount")
        )["total"]
        or 0
    )

    # Total sales by payment method (these match the HTML template variables)
    cash_sales_amount = cash_service_sales + cash_product_sales
    card_sales_amount = card_service_sales + card_product_sales
    online_link_amount = link_service_sales + online_product_sales

    # Calculate payment processing charges
    card_charges_amount = Decimal(str(card_sales_amount)) * Decimal("0.021")  # 2.1%
    link_charges_amount = Decimal(str(online_link_amount)) * Decimal("0.03")  # 3%

    # Get expense and withdrawal totals for the day
    expenses_total = (
        Expense.objects.filter(
            cash_register=cash_register, created_at__date=date  # Added date filter
        ).aggregate(total=Sum("amount"))["total"]
        or 0
    )

    withdrawals_total = (
        CashWithdrawal.objects.filter(
            cash_register=cash_register, created_at__date=date  # Added date filter
        ).aggregate(total=Sum("amount"))["total"]
        or 0
    )

    # Calculate net sales
    net_sales_amount = (
        Decimal(str(gross_sales)) - card_charges_amount - link_charges_amount - vat_amount
    )

    # Find or create the report
    report, created = DailySalesReport.objects.update_or_create(
        date=date,
        location=location,
        defaults={
            "gross_sales_amount": gross_sales,
            "service_sales_amount": service_sales_amount,
            "product_sales_amount": product_sales_amount,
            "package_sales_amount": package_sales_amount,
            "cash_sales_amount": cash_sales_amount,
            "card_sales_amount": card_sales_amount,
            "online_link_amount": online_link_amount,
            "vat_amount": vat_amount,
            "card_charges_amount": card_charges_amount,
            "link_charges_amount": link_charges_amount,
            "net_sales_amount": net_sales_amount,
            "expenses_total": expenses_total,
            "cash_withdrawals_total": withdrawals_total,
            "starting_cash_balance": cash_register.current_balance
            - cash_sales_amount
            + expenses_total
            + withdrawals_total,
            "ending_cash_balance": cash_register.current_balance,
            "created_by": user,
            "notes": f"Updated on {timezone.now().strftime('%Y-%m-%d %H:%M:%S')}",
        },
    )

    return report, created
    """Calculate sales data and create or update a DailySalesReport for a specific date and location"""
    from decimal import Decimal
    from django.db.models import Q, Sum
    from django.utils import timezone

    # Get all sales for today at this location
    # Product sales
    product_sales = ProductSale.objects.filter(
        created_at__date=date, location=location, status="COMPLETED"
    )

    product_sales_amount = (
        product_sales.aggregate(total=Sum("total_amount"))["total"] or 0
    )

    # Service sales - directly filter by date and location
    service_sales = Sale.objects.filter(
        created_at__date=date, sale_type="service", location=location
    )

    service_sales_amount = service_sales.aggregate(total=Sum("total_price"))["total"] or 0

    # Package sales - directly filter by date and location
    package_sales = Sale.objects.filter(
        created_at__date=date, sale_type="package", location=location
    )

    package_sales_amount = package_sales.aggregate(total=Sum("total_price"))["total"] or 0

    # Total gross sales
    gross_sales = service_sales_amount + package_sales_amount + product_sales_amount

    # VAT amount (5%)
    vat_amount = gross_sales * Decimal("0.05")

    # Payment method breakdown for service and package sales (combined from Sale model)
    # Cash payments
    cash_service_sales = (
        Sale.objects.filter(
            created_at__date=date, payment_method="cash", location=location
        ).aggregate(total=Sum("total_price"))["total"]
        or 0
    )

    # Card payments
    card_service_sales = (
        Sale.objects.filter(
            created_at__date=date, payment_method="card", location=location
        ).aggregate(total=Sum("total_price"))["total"]
        or 0
    )

    # Link payments
    link_service_sales = (
        Sale.objects.filter(
            created_at__date=date, payment_method="link", location=location
        ).aggregate(total=Sum("total_price"))["total"]
        or 0
    )

    # Payment method breakdown for product sales
    cash_product_sales = (
        product_sales.filter(payment_method="CASH").aggregate(total=Sum("total_amount"))[
            "total"
        ]
        or 0
    )

    card_product_sales = (
        product_sales.filter(payment_method="CARD").aggregate(total=Sum("total_amount"))[
            "total"
        ]
        or 0
    )

    online_product_sales = (
        product_sales.filter(payment_method="ONLINE_LINK").aggregate(
            total=Sum("total_amount")
        )["total"]
        or 0
    )

    # Total sales by payment method
    cash_sales = cash_service_sales + cash_product_sales
    card_sales = card_service_sales + card_product_sales
    online_sales = link_service_sales + online_product_sales

    # Calculate payment processing charges
    card_charges = Decimal(str(card_sales)) * Decimal("0.021")  # 2.1%
    link_charges = Decimal(str(online_sales)) * Decimal("0.03")  # 3%

    # Get expense and withdrawal totals for the day
    expenses_total = (
        Expense.objects.filter(
            cash_register=cash_register, created_at__date=date  # Added date filter
        ).aggregate(total=Sum("amount"))["total"]
        or 0
    )

    withdrawals_total = (
        CashWithdrawal.objects.filter(
            cash_register=cash_register, created_at__date=date  # Added date filter
        ).aggregate(total=Sum("amount"))["total"]
        or 0
    )

    # Calculate net sales
    net_sales = Decimal(str(gross_sales)) - card_charges - link_charges - vat_amount

    # Find or create the report
    report, created = DailySalesReport.objects.update_or_create(
        date=date,
        location=location,
        defaults={
            "gross_sales_amount": gross_sales,
            "service_sales_amount": service_sales_amount,
            "product_sales_amount": product_sales_amount,
            "package_sales_amount": package_sales_amount,
            "cash_sales_amount": cash_sales,
            "card_sales_amount": card_sales,
            "online_link_amount": online_sales,
            "vat_amount": vat_amount,
            "card_charges_amount": card_charges,
            "link_charges_amount": link_charges,
            "net_sales_amount": net_sales,
            "expenses_total": expenses_total,
            "cash_withdrawals_total": withdrawals_total,
            "starting_cash_balance": cash_register.current_balance
            - cash_sales
            + expenses_total
            + withdrawals_total,
            "ending_cash_balance": cash_register.current_balance,
            "created_by": user,
            "notes": f"Updated on {timezone.now().strftime('%Y-%m-%d %H:%M:%S')}",
        },
    )

    return report, created
