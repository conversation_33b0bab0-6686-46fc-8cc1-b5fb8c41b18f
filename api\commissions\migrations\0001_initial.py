# Generated by Django 4.2.19 on 2025-05-19 19:25

from decimal import Decimal
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('appointments', '0031_sale_invoice_sale_invoice_number'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('services', '0004_alter_service_name'),
        ('staff', '0004_therapistprofile_is_active'),
    ]

    operations = [
        migrations.CreateModel(
            name='CommissionProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('base_percentage', models.DecimalField(decimal_places=2, default=0, help_text='Base commission percentage for all sales', max_digits=5)),
                ('sessions_threshold', models.PositiveIntegerField(default=0, help_text='Minimum number of sessions before commission is applied')),
                ('is_active', models.BooleanField(default=True)),
                ('is_default', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('therapist', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='commission_profiles', to='staff.therapistprofile')),
            ],
            options={
                'ordering': ['-is_default', '-is_active', 'name'],
            },
        ),
        migrations.CreateModel(
            name='TherapistStats',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_sessions', models.PositiveIntegerField(default=0)),
                ('total_earnings', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10)),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('therapist', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='stats', to='staff.therapistprofile')),
            ],
            options={
                'verbose_name_plural': 'Therapist Stats',
            },
        ),
        migrations.CreateModel(
            name='ManualCommission',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(blank=True, decimal_places=2, help_text='Fixed commission amount', max_digits=10, null=True)),
                ('percentage', models.DecimalField(blank=True, decimal_places=2, help_text='Commission percentage of sale price', max_digits=5, null=True)),
                ('notes', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_manual_commissions', to=settings.AUTH_USER_MODEL)),
                ('sale', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='manual_commissions', to='appointments.sale')),
                ('therapist', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='manual_commissions', to='staff.therapistprofile')),
            ],
            options={
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='CommissionRule',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('rule_type', models.CharField(choices=[('global', 'Global Rule'), ('service', 'Service-specific Rule'), ('package', 'Package-specific Rule')], default='global', max_length=20)),
                ('percentage', models.DecimalField(blank=True, decimal_places=2, help_text='Commission percentage for this rule', max_digits=5, null=True)),
                ('fixed_amount', models.DecimalField(blank=True, decimal_places=2, help_text='Fixed commission amount for this rule', max_digits=10, null=True)),
                ('min_sessions', models.PositiveIntegerField(default=0, help_text='Minimum number of sessions before this rule applies')),
                ('priority', models.PositiveIntegerField(default=0, help_text='Higher priority rules are applied first')),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('package', models.ForeignKey(blank=True, help_text='Only for package-specific rules', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='commission_rules', to='services.servicepackage')),
                ('profile', models.ForeignKey(blank=True, help_text='If null, this is a global rule', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='commission_rules', to='commissions.commissionprofile')),
                ('service', models.ForeignKey(blank=True, help_text='Only for service-specific rules', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='commission_rules', to='services.service')),
            ],
            options={
                'ordering': ['-priority', '-is_active', 'name'],
            },
        ),
        migrations.CreateModel(
            name='CommissionEarning',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('percentage_used', models.DecimalField(blank=True, decimal_places=2, help_text='Percentage used to calculate this commission', max_digits=5, null=True)),
                ('date_earned', models.DateField(default=django.utils.timezone.now)),
                ('is_paid', models.BooleanField(default=False)),
                ('payment_date', models.DateField(blank=True, null=True)),
                ('notes', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('commission_rule', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='earnings', to='commissions.commissionrule')),
                ('manual_commission', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='earnings', to='commissions.manualcommission')),
                ('sale', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='commission_earnings', to='appointments.sale')),
                ('therapist', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='commission_earnings', to='staff.therapistprofile')),
            ],
            options={
                'ordering': ['-date_earned', '-created_at'],
            },
        ),
    ]
