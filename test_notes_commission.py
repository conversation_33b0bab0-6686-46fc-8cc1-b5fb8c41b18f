#!/usr/bin/env python
"""
Test script for notes-based commission calculation
"""
import os
import sys
import django

# Add the project directory to Python path
sys.path.append('/app')

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'stretchup.settings')
django.setup()

from api.commissions.services import CommissionCalculator
from api.appointments.models import Appointment, Sale
from decimal import Decimal

def test_notes_parsing():
    """Test the notes parsing functionality"""
    print("🧪 Testing Notes Parsing Functionality")
    print("=" * 50)
    
    # Mock appointment class for testing
    class MockAppointment:
        def __init__(self, notes, total_duration):
            self.notes = notes
            self.total_duration = total_duration
    
    test_cases = [
        # (notes, total_duration, expected_commission_duration, description)
        ("Stretching: 30 min", 60, 30, "Single commission service"),
        ("Massage: 45 min, Red-Light: 15 min", 60, 45, "Mixed services - exclude red light"),
        ("Compression: 20 min", 30, 0, "Only excluded service"),
        ("Red-Light: 10 min", 15, 0, "Only red light service"),
        ("Stretching: 25 min, Massage: 35 min", 60, 60, "Multiple commission services"),
        ("Stretching: 20 min, Compression: 10 min, Massage: 30 min", 60, 50, "Mixed with compression"),
        ("", 45, 45, "Empty notes - fallback to total"),
        (None, 30, 30, "No notes - fallback to total"),
    ]
    
    for i, (notes, total_duration, expected, description) in enumerate(test_cases, 1):
        print(f"\n{i}. {description}")
        print(f"   Notes: '{notes}'")
        print(f"   Total Duration: {total_duration} min")
        
        mock_apt = MockAppointment(notes, total_duration)
        result = CommissionCalculator._extract_commission_duration_from_notes(mock_apt)
        
        print(f"   Expected: {expected} min")
        print(f"   Got: {result} min")
        
        if result == expected:
            print(f"   ✅ PASS")
        else:
            print(f"   ❌ FAIL")

def test_real_appointments():
    """Test with real appointment data"""
    print("\n\n🔍 Testing Real Appointments")
    print("=" * 50)
    
    appointments = Appointment.objects.filter(notes__isnull=False).exclude(notes='')[:3]
    
    if not appointments.exists():
        print("No appointments with notes found")
        return
    
    for apt in appointments:
        print(f"\n📋 Appointment {apt.id}:")
        print(f"   Total Duration: {apt.total_duration} min")
        print(f"   Notes: {apt.notes}")
        
        commission_duration = CommissionCalculator._extract_commission_duration_from_notes(apt)
        print(f"   Commission Duration: {commission_duration} min")
        
        if commission_duration != apt.total_duration:
            print(f"   ✅ Duration adjusted (excluded non-commission services)")
        else:
            print(f"   ℹ️ Duration unchanged")

def test_session_based_calculation():
    """Test session-based package commission calculation"""
    print("\n\n💰 Testing Session-Based Package Commission")
    print("=" * 50)
    
    # Find a package sale with appointment
    package_sales = Sale.objects.filter(sale_type='package', appointment__isnull=False)
    
    if not package_sales.exists():
        print("No package sales with appointments found")
        return
    
    sale = package_sales.first()
    print(f"\n📦 Testing Sale ID: {sale.id}")
    print(f"   Sale Type: {sale.sale_type}")
    print(f"   Total Price: {sale.total_price} AED")
    
    if sale.appointment:
        print(f"   Appointment Duration: {sale.appointment.total_duration} min")
        print(f"   Appointment Notes: {sale.appointment.notes}")
        
        # Test commission duration extraction
        commission_duration = CommissionCalculator._extract_commission_duration_from_notes(sale.appointment)
        print(f"   Commission Duration: {commission_duration} min")
        
        # Test session-based price calculation
        session_price = CommissionCalculator._calculate_session_based_package_price(sale, None)
        print(f"   Session-Based Price: {session_price} AED")
        
        # Calculate expected price manually
        if sale.user_package and sale.user_package.package_option:
            package_price = sale.user_package.package_option.price
            package_duration = sale.user_package.package_option.time
            expected_price = (Decimal(str(package_price)) / Decimal(str(package_duration))) * Decimal(str(commission_duration))
            expected_price = expected_price.quantize(Decimal('0.01'))
            
            print(f"   Package: {package_price} AED for {package_duration} min")
            print(f"   Expected: ({package_price} ÷ {package_duration}) × {commission_duration} = {expected_price} AED")
            
            if session_price == expected_price:
                print(f"   ✅ Calculation correct")
            else:
                print(f"   ❌ Calculation mismatch")

if __name__ == "__main__":
    try:
        test_notes_parsing()
        test_real_appointments()
        test_session_based_calculation()
        print("\n🎉 Testing completed!")
    except Exception as e:
        print(f"\n❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
