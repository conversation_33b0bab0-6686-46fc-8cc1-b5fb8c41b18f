import time
import logging

from django.contrib.auth import get_user_model
from django.shortcuts import render
from django.core.cache import cache
from django.http import JsonResponse
from django.conf import settings

from rest_framework import generics, status
from rest_framework.views import APIView
from rest_framework_simplejwt.views import TokenObtainPairView
from rest_framework.throttling import ScopedRateThrottle
from rest_framework_simplejwt.exceptions import AuthenticationFailed
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.permissions import AllowAny
from rest_framework_simplejwt.tokens import RefreshToken

from api.utils.push_notifications import send_push_notification

from .models import CacheTest
from .serializers import UserProfileSerializer, CustomTokenObtainPairSerializer
from utils.email import send_email
from utils.logging import api_logger, log_request_data, log_response_data, log_error

from api.core.permissions import IsSelf, IsStaffUser
from api.core.views import NonStaffRetrieveUpdateDestroyApiView, BaseApiView

User = get_user_model()

from utils.logging import (
    api_logger,
    logger,
    log_request_data,
    log_response_data,
    log_error,
)


class RegisterUserView(APIView):
    """
    View to register a new user with email and phone number.
    """

    permission_classes = [AllowAny]

    def post(self, request):
        # Log incoming request
        log_request_data(request, "📥 User Registration Request")

        email = request.data.get("email")
        phone_number = request.data.get("phone_number")
        first_name = request.data.get("first_name")
        last_name = request.data.get("last_name")
        role = request.data.get("role", "customer")  # Default role is "customer"
        gender = request.data.get("gender", "male")
        medical_issues = request.data.get("medicalIssues", "")
        goal = request.data.get("goal", "")

        api_logger.info(f"Processing registration for email: {email}, role: {role}")

        if not email or not phone_number:
            api_logger.warning(f"❌ Registration failed: Missing email or phone number")
            response = Response(
                {"error": "Email and phone number are required"},
                status=status.HTTP_400_BAD_REQUEST,
            )
            log_response_data(response, "📤 Registration Response")
            return response

        # Check if the user already exists
        if User.objects.filter(email=email).exists():
            api_logger.warning(f"❌ Registration failed: Email already exists: {email}")
            response = Response(
                {"error": "User with this email already exists"},
                status=status.HTTP_400_BAD_REQUEST,
            )
            log_response_data(response, "📤 Registration Response")
            return response

        if User.objects.filter(phone_number=phone_number).exists():
            api_logger.warning(
                f"❌ Registration failed: Phone number already exists: {phone_number}"
            )
            response = Response(
                {"error": "User with this phone number already exists"},
                status=status.HTTP_400_BAD_REQUEST,
            )
            log_response_data(response, "📤 Registration Response")
            return response

        try:
            # Create the user
            api_logger.info(f"🔄 Creating new user with email: {email}")
            user = User.objects.create_user(
                email=email,
                phone_number=phone_number,
                role=role,
                first_name=first_name,
                last_name=last_name,
                gender=gender,
                medical_issues=medical_issues,
                goal=goal,
            )

            # Generate OTP immediately after user creation
            api_logger.info(f"🔄 Generating OTP for user: {email}")
            otp = user.generate_otp()

            # Send email with OTP
            recipient_email = email
            api_logger.info(f"🔄 Sending OTP email to: {recipient_email}")
            send_email(
                subject="Your OTP Code",
                message=f"Your OTP is {otp}. It will expire in 10 minutes.",
                recipient_list=[recipient_email],
                html_template="email/otp.html",
                context={"otp": otp},
            )

            api_logger.info(f"✅ User registered successfully: {email}")
            response = Response(
                {
                    "message": "User registered successfully and OTP sent",
                    "otp_sent": True,
                },
                status=status.HTTP_201_CREATED,
            )
            log_response_data(response, "📤 Registration Response")
            return response

        except Exception as e:
            log_error(e, f"User registration for email: {email}", log_full_trace=True)
            response = Response(
                {"error": "An error occurred during registration"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
            log_response_data(response, "📤 Registration Error Response")
            return response


class GenerateOTPView(APIView):
    """
    Generate an OTP and send it to the user's email.
    """

    permission_classes = [AllowAny]

    def post(self, request):
        # Log incoming request
        log_request_data(request, "📥 OTP Generation Request")

        email = request.data.get("email")
        if not email:
            api_logger.warning("❌ OTP generation failed: Email not provided")
            response = Response(
                {"error": "Email is required"}, status=status.HTTP_400_BAD_REQUEST
            )
            log_response_data(response, "📤 OTP Generation Response")
            return response

        try:
            api_logger.info(f"🔍 Looking up user with email: {email}")
            user = User.objects.get(email__iexact=email)

            # Check if user is active
            if not user.is_active:
                api_logger.warning(
                    f"❌ OTP generation failed: User {email} is not active"
                )
                response = Response(
                    {"error": "Account is deleted."}, status=status.HTTP_403_FORBIDDEN
                )
                log_response_data(response, "📤 OTP Generation Response")
                return response

            api_logger.info(f"🔄 Generating OTP for user: {email}")
            otp = user.generate_otp()

            # Determine the recipient based on environment
            recipient_email = email
            # if settings.DEBUG or getattr(settings, "ENVIRONMENT", "production") in [
            #     "local",
            #     "staging",
            # ]:
            #     recipient_email = settings.DEFAULT_FROM_EMAIL

            # Send the email
            api_logger.info(f"🔄 Sending OTP email to: {recipient_email}")
            send_email(
                subject="Your OTP Code",
                message=f"Your OTP is {otp}. It will expire in 10 minutes.",
                recipient_list=[recipient_email],
                html_template="email/otp.html",
                context={"otp": otp},
            )

            api_logger.info(f"✅ OTP sent successfully to user: {email}")
            response = Response(
                {"message": "OTP sent successfully"}, status=status.HTTP_200_OK
            )
            log_response_data(response, "📤 OTP Generation Response")
            return response

        except User.DoesNotExist:
            api_logger.warning(
                f"❌ OTP generation failed: User not found with email: {email}"
            )
            response = Response(
                {"error": "User not found"}, status=status.HTTP_404_NOT_FOUND
            )
            log_response_data(response, "📤 OTP Generation Response")
            return response

        except Exception as e:
            log_error(e, f"OTP generation for email: {email}", log_full_trace=True)
            response = Response(
                {"error": "An error occurred while generating OTP"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
            log_response_data(response, "📤 OTP Generation Error Response")
            return response


class ObtainTokenPairView(TokenObtainPairView):
    """
    Overrides the default ObtainTokenPairView to use OTP-based authentication.
    """

    def post(self, request, *args, **kwargs):
        # Log incoming request
        log_request_data(request, "📥 Token Authentication Request")

        email = request.data.get("email")
        otp = request.data.get("otp")

        api_logger.info(f"🔄 Processing authentication for email: {email}")

        if not email or not otp:
            api_logger.warning(f"❌ Authentication failed: Missing email or OTP")
            response = Response(
                {"error": "Email and OTP are required"},
                status=status.HTTP_400_BAD_REQUEST,
            )
            log_response_data(response, "📤 Authentication Response")
            return response

        try:
            api_logger.info(f"🔍 Looking up user with email: {email}")
            user = User.objects.get(email__iexact=email)

            # Special case: Bypass OTP verification for test emails
            bypass_emails = [
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
            ]
            if email.lower() in bypass_emails:
                api_logger.info(f"🔑 Bypassing OTP verification for test email: {email}")

                # Special test case: require specific OTP only for your email
                if email.lower() == "<EMAIL>" and otp != "130199":
                    api_logger.warning(
                        f"❌ Authentication failed: Invalid test OTP for: {email}"
                    )
                    return Response(
                        {"error": "Invalid test OTP"}, status=status.HTTP_400_BAD_REQUEST
                    )

                # All other bypass emails or correct test OTP
                refresh = RefreshToken.for_user(user)
                response = Response(
                    {
                        "refresh": str(refresh),
                        "access": str(refresh.access_token),
                    },
                    status=status.HTTP_200_OK,
                )
                log_response_data(response, "📤 Authentication Response (OTP Bypass)")
                return response

            # Verify the OTP for all other users
            api_logger.info(f"🔄 Verifying OTP for user: {email}")
            if user.verify_otp(otp):
                api_logger.info(f"✅ Authentication successful for user: {email}")
                refresh = RefreshToken.for_user(user)
                response = Response(
                    {
                        "refresh": str(refresh),
                        "access": str(refresh.access_token),
                    },
                    status=status.HTTP_200_OK,
                )
                log_response_data(response, "📤 Authentication Response")
                return response

            api_logger.warning(f"❌ Authentication failed: Invalid OTP for user: {email}")
            response = Response(
                {"error": "Invalid or expired OTP"}, status=status.HTTP_400_BAD_REQUEST
            )
            log_response_data(response, "📤 Authentication Response")
            return response

        except User.DoesNotExist:
            api_logger.warning(
                f"❌ Authentication failed: User not found with email: {email}"
            )
            response = Response(
                {"error": "User not found"}, status=status.HTTP_404_NOT_FOUND
            )
            log_response_data(response, "📤 Authentication Response")
            return response

        except Exception as e:
            log_error(e, f"Token authentication for email: {email}", log_full_trace=True)
            response = Response(
                {"error": "An error occurred during authentication"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
            log_response_data(response, "📤 Authentication Error Response")
            return response


class UserProfileView(APIView):
    permission_classes = [IsAuthenticated]

    def get(self, request):
        # Log incoming request
        log_request_data(request, "📥 User Profile Request")

        api_logger.info(f"🔄 Fetching profile for user: {request.user.email}")
        try:
            serializer = UserProfileSerializer(request.user, context={"request": request})
            api_logger.info(
                f"✅ Profile retrieved successfully for user: {request.user.email}"
            )
            response = Response(serializer.data)
            log_response_data(response, "📤 User Profile Response")
            return response
        except Exception as e:
            log_error(
                e,
                f"Profile retrieval for user: {request.user.email}",
                log_full_trace=True,
            )
            response = Response(
                {"error": "An error occurred while retrieving profile"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
            log_response_data(response, "📤 User Profile Error Response")
            return response


class EmailCheckView(APIView):
    """
    POST endpoint to check if an email is already registered in the system.
    Returns true if the email exists, false otherwise.
    """

    def post(self, request):
        log_request_data(request, "📥 Email Check Request")
        api_logger.info("🔄 Processing email check")

        email = request.data.get("email")

        if not email:
            api_logger.warning("❌ Missing email parameter")
            response = Response(
                {"error": "Email parameter is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )
            log_response_data(response, "📤 Email Check Error Response")
            return response

        email_exists = User.objects.filter(email=email).exists()
        api_logger.info(
            f"📧 Email check result for {email}: {'exists' if email_exists else 'does not exist'}"
        )

        response = Response({"exists": email_exists}, status=status.HTTP_200_OK)
        log_response_data(response, "📤 Email Check Response")
        return response


class DeactivateAccountView(APIView):
    """
    View to deactivate (soft delete) a user account.
    Only authenticated users can deactivate their own account.
    """

    permission_classes = [IsAuthenticated, IsSelf]

    def post(self, request):
        # Log incoming request
        log_request_data(request, "📥 Account Deactivation Request")

        user = request.user
        api_logger.info(f"🔄 Processing account deactivation for user: {user.email}")

        try:
            # Deactivate the account
            user.is_active = False
            user.save(update_fields=["is_active"])

            api_logger.info(f"✅ Account successfully deactivated for user: {user.email}")
            response = Response(
                {"message": "Account successfully deactivated"}, status=status.HTTP_200_OK
            )
            log_response_data(response, "📤 Account Deactivation Response")
            return response

        except Exception as e:
            log_error(
                e, f"Account deactivation for user: {user.email}", log_full_trace=True
            )
            response = Response(
                {"error": "An error occurred while deactivating account"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
            log_response_data(response, "📤 Account Deactivation Error Response")
            return response


"""
class ObtainTokenPairView(TokenObtainPairView):
    serializer_class = CustomTokenObtainPairSerializer
    throttle_classes = [ScopedRateThrottle]
    throttle_scope = "login"

    def post(self, request, *args, **kwargs):
        # throttle = ScopedRateThrottle()
        # throttle.scope = self.throttle_scope

        # if not throttle.allow_request(request, self):
        #     logger.warning(
        #         f"Token response throttled for email: {request.data.get('email', 'Unknown')}",
        #         extra={
        #             "user": "",
        #             "action": "response",
        #             "status": status.HTTP_429_TOO_MANY_REQUESTS,
        #         },
        #     )
        #     return Response(
        #         {"detail": "Request was throttled. Expected available in X seconds."},
        #         status=status.HTTP_429_TOO_MANY_REQUESTS,
        #     )

        # Extract the email from the request data safely
        email = request.data.get("email", "Unknown")

        # Log the email attempt before processing
        api_logger.info(
            f"Token request attempt for email: {email}",
            extra={"user": "", "action": "request", "status": ""},
        )

        try:
            # Call the original TokenObtainPairView's post method
            response = super().post(request, *args, **kwargs)
            api_logger.info(
                f"Token response for email: {email}",
                extra={"user": "", "action": "response", "status": response.status_code},
            )

            # send_email(
            #     subject="Welcome to Our Service",
            #     message="Welcome to our service. We are glad to have you.",
            #     recipient_list=["<EMAIL>"],
            # )

            return response

        except Exception as e:
            logger.warning(
                f"Token response failed for email: {email}",
                extra={
                    "user": "",
                    "action": "response",
                    "status": status.HTTP_401_UNAUTHORIZED,
                },
            )
            exception_logger.error(
                f"Token response failed for email: {email}: {str(e)}')"
            )
            return Response(
                {"detail": "Invalid credentials provided"},
                status=status.HTTP_401_UNAUTHORIZED,
            )
"""


def cache_test_list(request):
    cache_key = "cache_tests"
    start_time = time.time()

    cache_tests = cache.get(cache_key)
    if not cache_tests:
        # api_logger.info(
        #     "Cache miss: fetching data from database",
        #     extra={"user": "", "action": "response", "status": ""},
        # )
        cache_tests = list(CacheTest.objects.all())
        cache.set(cache_key, cache_tests, timeout=60 * 15)  # Cache timeout of 15 minutes
        # api_logger.info(
        #     f"Database fetch time: {time.time() - start_time} seconds",
        #     extra={"user": "", "action": "response", "status": ""},
        # )
    else:
        print("test cache")
        # api_logger.info(
        #     "Cache hit: fetching data from cache",
        #     extra={"user": "", "action": "response", "status": ""},
        # )
        # api_logger.info(
        #     f"Cache fetch time: {time.time() - start_time} seconds",
        #     extra={"user": "", "action": "response", "status": ""},
        # )

    return render(request, "cache_test_list.html", {"cache_tests": cache_tests})


class TestXSSView(APIView):
    permission_classes = [AllowAny]

    def get(self, request):
        data = {"message": "GET request successful. This is a test endpoint."}
        return Response(data, status=status.HTTP_200_OK)

    def post(self, request):
        return Response(request.data, status=status.HTTP_200_OK)


class SaveExpoPushTokenView(APIView):
    """
    API endpoint to save the user's Expo push token for notifications.
    """

    permission_classes = [IsAuthenticated]

    def post(self, request):
        user = request.user
        expo_push_token = request.data.get("expo_push_token")

        if not expo_push_token:
            return Response(
                {"error": "Expo push token is required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Validate token format (Expo tokens start with "ExponentPushToken[")
        if not expo_push_token.startswith("ExponentPushToken["):
            return Response(
                {"error": "Invalid Expo push token format"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Save the token
        user.expo_push_token = expo_push_token
        user.save(update_fields=["expo_push_token"])

        api_logger.info(f"Saved Expo push token for user {user.email}")

        # Return updated user data using your existing serializer
        from .serializers import UserProfileSerializer

        serializer = UserProfileSerializer(user, context={"request": request})

        return Response(
            {"message": "Expo push token saved successfully", "user": serializer.data},
            status=status.HTTP_200_OK,
        )


class NotifyUserView(APIView):
    """
    API endpoint to send a custom push notification to the authenticated user.
    Useful for testing notifications.
    """

    permission_classes = [IsAuthenticated]

    def post(self, request):
        user = request.user
        title = request.data.get("title")
        body = request.data.get("body")
        data = request.data.get("data", {})

        if not title or not body:
            return Response(
                {"error": "Title and body are required"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        if not user.expo_push_token:
            return Response(
                {"error": "User does not have a valid Expo push token"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            result = send_push_notification(
                expo_push_token=user.expo_push_token, title=title, body=body, data=data
            )

            return Response(
                {"message": "Notification sent successfully", "result": result},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            api_logger.error(f"Error sending notification to {user.email}: {str(e)}")
            return Response(
                {"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class RemoveExpoPushTokenView(APIView):
    """
    API endpoint to remove the user's Expo push token (e.g., on logout).
    """

    permission_classes = [IsAuthenticated]

    def post(self, request):
        user = request.user
        user.expo_push_token = None
        user.save(update_fields=["expo_push_token"])

        api_logger.info(f"Removed Expo push token for user {user.email}")

        return Response(
            {"message": "Expo push token removed successfully"}, status=status.HTTP_200_OK
        )


class TestNotificationView(APIView):
    """
    API endpoint for testing push notifications.
    Only available in DEBUG mode for security.
    """

    permission_classes = [IsAuthenticated]

    def post(self, request):
        from django.conf import settings

        if not settings.DEBUG:
            return Response(
                {"error": "This endpoint is only available in DEBUG mode"},
                status=status.HTTP_403_FORBIDDEN,
            )

        user = request.user

        if not user.expo_push_token:
            return Response(
                {"error": "User does not have a valid Expo push token"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            result = send_push_notification(
                expo_push_token=user.expo_push_token,
                title="Test Notification",
                body="This is a test notification from your Django backend!",
                data={"test": True, "timestamp": str(timezone.now())},
            )

            return Response(
                {"message": "Test notification sent successfully", "result": result},
                status=status.HTTP_200_OK,
            )

        except Exception as e:
            api_logger.error(f"Error sending test notification to {user.email}: {str(e)}")
            return Response(
                {"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
