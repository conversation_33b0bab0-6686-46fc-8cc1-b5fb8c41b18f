# Generated by Django 4.0 on 2024-12-18 11:28

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('authentication', '0003_remove_historicaluser_otp_secret_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='historicaluser',
            name='address',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='historicaluser',
            name='date_of_birth',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='historicaluser',
            name='first_name',
            field=models.CharField(default='Testname', max_length=80),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='historicaluser',
            name='last_name',
            field=models.CharField(default='TestLastName', max_length=80),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='historicaluser',
            name='profile_picture',
            field=models.TextField(blank=True, max_length=100, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='address',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='date_of_birth',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='first_name',
            field=models.CharField(default='TestName', max_length=80),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='user',
            name='last_name',
            field=models.CharField(default='TestLastName', max_length=80),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='user',
            name='profile_picture',
            field=models.ImageField(blank=True, null=True, upload_to='profile_pictures/'),
        ),
        migrations.AlterField(
            model_name='historicaluser',
            name='role',
            field=models.CharField(choices=[('customer', 'Customer'), ('therapist', 'Therapist'), ('owner', 'Business Owner'), ('receptionist', 'Receptionist')], default='customer', max_length=20),
        ),
        migrations.AlterField(
            model_name='user',
            name='role',
            field=models.CharField(choices=[('customer', 'Customer'), ('therapist', 'Therapist'), ('owner', 'Business Owner'), ('receptionist', 'Receptionist')], default='customer', max_length=20),
        ),
    ]
