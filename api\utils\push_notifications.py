# api/utils/push_notifications.py
import requests
import logging
from typing import Optional, Dict, Any
from django.conf import settings
from datetime import datetime, date, time

logger = logging.getLogger(__name__)


def send_push_notification(
    expo_push_token: str,
    title: str,
    body: str,
    data: Optional[Dict[str, Any]] = None,
    sound: str = "default",
) -> Dict[str, Any]:
    """
    Sends a push notification to a user via Expo.

    Args:
        expo_push_token: The Expo push token for the user's device
        title: The notification title
        body: The notification body text
        data: Optional additional data to send with the notification
        sound: Sound to play (default: "default")

    Returns:
        Response from Expo push service

    Raises:
        ValueError: If expo_push_token is missing
        Exception: If the push notification fails to send
    """
    if not expo_push_token:
        raise ValueError("Expo push token is required")

    if not expo_push_token.startswith("ExponentPushToken["):
        logger.warning(f"Invalid Expo push token format: {expo_push_token}")

    url = "https://exp.host/--/api/v2/push/send"
    headers = {
        "Accept": "application/json",
        "Accept-Encoding": "gzip, deflate",
        "Content-Type": "application/json",
    }

    payload = {
        "to": expo_push_token,
        "sound": sound,
        "title": title,
        "body": body,
        "data": data or {},
    }

    try:
        logger.info(f"Sending push notification to token: {expo_push_token[:20]}...")
        response = requests.post(url, json=payload, headers=headers, timeout=10)

        if response.status_code == 200:
            result = response.json()

            # Debug: Log the full response to understand its structure
            logger.debug(f"Expo API response: {result}")

            # Check if there were any errors in the response
            if "data" in result and result["data"]:
                # Ensure result['data'] is a list
                data_items = (
                    result["data"]
                    if isinstance(result["data"], list)
                    else [result["data"]]
                )

                for item in data_items:
                    # Check if item is a dictionary before accessing its keys
                    if isinstance(item, dict):
                        if "status" in item and item["status"] == "error":
                            error_msg = item.get("message", "Unknown error")
                            logger.error(f"Expo push notification error: {error_msg}")
                            raise Exception(f"Push notification failed: {error_msg}")
                    elif isinstance(item, str):
                        # If item is a string, it might be an error message
                        logger.warning(f"Unexpected string response from Expo: {item}")
                        if "error" in item.lower():
                            raise Exception(f"Push notification failed: {item}")
                    else:
                        logger.warning(
                            f"Unexpected item type in Expo response: {type(item)} - {item}"
                        )

            logger.info("Push notification sent successfully")
            return result
        else:
            error_msg = f"HTTP {response.status_code}: {response.text}"
            logger.error(f"Failed to send push notification: {error_msg}")
            raise Exception(f"Failed to send push notification: {error_msg}")

    except requests.exceptions.RequestException as e:
        logger.error(f"Network error sending push notification: {str(e)}")
        raise Exception(f"Network error sending push notification: {str(e)}")


def _format_date(date_obj):
    """
    Helper function to format date whether it's a date object or string.
    """
    if isinstance(date_obj, str):
        try:
            # Try to parse common date string formats
            if "T" in date_obj:  # ISO format like "2025-05-22T00:00:00"
                parsed_date = datetime.fromisoformat(
                    date_obj.replace("Z", "+00:00")
                ).date()
            else:  # Date string like "2025-05-22"
                parsed_date = datetime.strptime(date_obj, "%Y-%m-%d").date()
            return parsed_date.strftime("%B %d, %Y")
        except ValueError:
            # If parsing fails, return the string as-is
            return date_obj
    elif isinstance(date_obj, (date, datetime)):
        return date_obj.strftime("%B %d, %Y")
    else:
        return str(date_obj)


def _format_time(time_obj):
    """
    Helper function to format time whether it's a time object or string.
    """
    if isinstance(time_obj, str):
        try:
            # Try to parse common time string formats
            if "T" in time_obj:  # ISO format like "2025-05-22T14:30:00"
                parsed_time = datetime.fromisoformat(
                    time_obj.replace("Z", "+00:00")
                ).time()
            elif ":" in time_obj:  # Time string like "14:30:00" or "14:30"
                time_parts = time_obj.split(":")
                if len(time_parts) >= 2:
                    hour = int(time_parts[0])
                    minute = int(time_parts[1])
                    parsed_time = time(hour, minute)
                else:
                    return time_obj
            else:
                return time_obj
            return parsed_time.strftime("%I:%M %p")
        except (ValueError, IndexError):
            # If parsing fails, return the string as-is
            return time_obj
    elif isinstance(time_obj, (time, datetime)):
        return time_obj.strftime("%I:%M %p")
    else:
        return str(time_obj)


def _get_user_full_name(user_obj):
    """
    Helper function to get user's full name from various object types.
    """
    if hasattr(user_obj, "first_name") and hasattr(user_obj, "last_name"):
        # Django User model instance
        return f"{user_obj.first_name} {user_obj.last_name}".strip() or user_obj.email
    elif isinstance(user_obj, dict):
        # Dictionary representation
        first_name = user_obj.get("first_name", "")
        last_name = user_obj.get("last_name", "")
        return f"{first_name} {last_name}".strip() or user_obj.get("email", "User")
    else:
        return str(user_obj)


def send_appointment_confirmation_notification(user, appointment):
    """
    Send a push notification when an appointment is created/confirmed.
    This now sends notifications to both customer and therapist.
    """
    # Original customer notification logic preserved with modifications
    if not user.expo_push_token:
        logger.info(f"No expo push token for user {user.email}, skipping notification")
        # Don't return early - still need to notify therapist
    else:
        try:
            title = "Appointment Confirmed!"

            # Debug logging to understand the appointment object structure
            logger.debug(f"Appointment object type: {type(appointment)}")
            logger.debug(f"Appointment ID: {getattr(appointment, 'id', 'N/A')}")

            # Handle both Django model instances and dictionary data
            if hasattr(appointment, "_meta"):  # This is a Django model instance
                appointment_date = appointment.date
                appointment_time = appointment.time
                appointment_id = appointment.id

                # Access therapist safely through the proper relationship
                therapist_name = "your therapist"  # Default fallback
                therapist_user = None
                try:
                    if appointment.therapist and appointment.therapist.user:
                        therapist_user = appointment.therapist.user
                        therapist_name = _get_user_full_name(therapist_user)
                except Exception as e:
                    logger.warning(f"Could not get therapist name from model: {str(e)}")

            elif isinstance(appointment, dict):  # This is dictionary data
                appointment_date = appointment.get("date")
                appointment_time = appointment.get("time")
                appointment_id = appointment.get("id")

                # Handle therapist from dictionary
                therapist_name = "your therapist"  # Default fallback
                therapist_user = None
                try:
                    therapist_data = appointment.get("therapist")
                    if therapist_data and isinstance(therapist_data, dict):
                        user_data = therapist_data.get("user")
                        if user_data and isinstance(user_data, dict):
                            therapist_user = user_data
                            therapist_name = _get_user_full_name(user_data)
                except Exception as e:
                    logger.warning(f"Could not get therapist name from dict: {str(e)}")
            else:
                logger.error(f"Unexpected appointment object type: {type(appointment)}")
                return

            if not appointment_date or not appointment_time:
                logger.error(
                    f"Missing appointment date/time: date={appointment_date}, time={appointment_time}"
                )
                return

            # Format date and time safely, handling both objects and strings
            formatted_date = _format_date(appointment_date)
            formatted_time = _format_time(appointment_time)

            body = f"Your appointment with {therapist_name} on {formatted_date} at {formatted_time} has been confirmed."

            # Ensure data values are strings for JSON serialization
            date_str = (
                appointment_date.isoformat()
                if hasattr(appointment_date, "isoformat")
                else str(appointment_date)
            )
            time_str = (
                appointment_time.isoformat()
                if hasattr(appointment_time, "isoformat")
                else str(appointment_time)
            )

            data = {
                "type": "appointment_confirmation",
                "appointment_id": str(appointment_id) if appointment_id else None,
                "date": date_str,
                "time": time_str,
            }

            send_push_notification(
                expo_push_token=user.expo_push_token, title=title, body=body, data=data
            )

            logger.info(
                f"Sent appointment confirmation notification to customer {user.email}"
            )

        except Exception as e:
            logger.error(
                f"Failed to send appointment confirmation notification to customer {user.email}: {str(e)}"
            )
            logger.exception("Full traceback:")  # This will log the full stack trace

    # NEW: Send notification to therapist
    try:
        therapist_expo_token = None
        customer_name = _get_user_full_name(user)

        # Get therapist's expo token based on appointment type
        if hasattr(appointment, "_meta"):  # Django model instance
            if (
                appointment.therapist
                and appointment.therapist.user
                and hasattr(appointment.therapist.user, "expo_push_token")
            ):
                therapist_expo_token = appointment.therapist.user.expo_push_token
        elif isinstance(appointment, dict):  # Dictionary data
            therapist_data = appointment.get("therapist", {})
            if isinstance(therapist_data, dict):
                user_data = therapist_data.get("user", {})
                if isinstance(user_data, dict):
                    therapist_expo_token = user_data.get("expo_push_token")

        if therapist_expo_token:
            title = "New Appointment Confirmed!"
            body = f"Your appointment with {customer_name} on {formatted_date} at {formatted_time} has been confirmed."

            send_push_notification(
                expo_push_token=therapist_expo_token, title=title, body=body, data=data
            )

            logger.info(f"Sent appointment confirmation notification to therapist")
        else:
            logger.info(
                "No expo push token for therapist, skipping therapist notification"
            )

    except Exception as e:
        logger.error(
            f"Failed to send appointment confirmation notification to therapist: {str(e)}"
        )
        logger.exception("Full traceback:")


def send_appointment_reminder_notification(user, appointment):
    """
    Send a push notification as a reminder 24 hours before an appointment.
    This now sends reminders to both customer and therapist.
    """
    # Original customer reminder logic preserved with modifications
    if not user.expo_push_token:
        logger.info(f"No expo push token for user {user.email}, skipping reminder")
        # Don't return early - still need to notify therapist
    else:
        try:
            title = "Appointment Reminder"

            # Debug logging to understand the appointment object structure
            logger.debug(f"Appointment object type: {type(appointment)}")
            logger.debug(f"Appointment ID: {getattr(appointment, 'id', 'N/A')}")

            # Handle both Django model instances and dictionary data
            if hasattr(appointment, "_meta"):  # This is a Django model instance
                appointment_date = appointment.date
                appointment_time = appointment.time
                appointment_id = appointment.id

                # Access therapist safely through the proper Django relationship
                therapist_name = "your therapist"  # Default fallback
                therapist_user = None
                try:
                    if appointment.therapist and appointment.therapist.user:
                        therapist_user = appointment.therapist.user
                        therapist_name = _get_user_full_name(therapist_user)
                except Exception as e:
                    logger.warning(f"Could not get therapist name from model: {str(e)}")

            elif isinstance(appointment, dict):  # This is dictionary data
                appointment_date = appointment.get("date")
                appointment_time = appointment.get("time")
                appointment_id = appointment.get("id")

                # Handle therapist from dictionary structure
                therapist_name = "your therapist"  # Default fallback
                therapist_user = None
                try:
                    therapist_data = appointment.get("therapist")
                    if therapist_data and isinstance(therapist_data, dict):
                        user_data = therapist_data.get("user")
                        if user_data and isinstance(user_data, dict):
                            therapist_user = user_data
                            therapist_name = _get_user_full_name(user_data)
                except Exception as e:
                    logger.warning(f"Could not get therapist name from dict: {str(e)}")
            else:
                logger.error(f"Unexpected appointment object type: {type(appointment)}")
                return

            if not appointment_date or not appointment_time:
                logger.error(
                    f"Missing appointment date/time: date={appointment_date}, time={appointment_time}"
                )
                return

            # Format time safely, handling both objects and strings
            formatted_time = _format_time(appointment_time)

            body = f"You have an appointment tomorrow at {formatted_time} with {therapist_name}."

            # Ensure data values are strings for JSON serialization
            date_str = (
                appointment_date.isoformat()
                if hasattr(appointment_date, "isoformat")
                else str(appointment_date)
            )
            time_str = (
                appointment_time.isoformat()
                if hasattr(appointment_time, "isoformat")
                else str(appointment_time)
            )

            data = {
                "type": "appointment_reminder",
                "appointment_id": str(appointment_id) if appointment_id else None,
                "date": date_str,
                "time": time_str,
            }

            send_push_notification(
                expo_push_token=user.expo_push_token, title=title, body=body, data=data
            )

            logger.info(
                f"Sent appointment reminder notification to customer {user.email}"
            )

        except Exception as e:
            logger.error(
                f"Failed to send appointment reminder notification to customer {user.email}: {str(e)}"
            )
            logger.exception("Full traceback:")  # This will log the full stack trace

    # NEW: Send reminder to therapist
    try:
        therapist_expo_token = None
        customer_name = _get_user_full_name(user)

        # Get therapist's expo token based on appointment type
        if hasattr(appointment, "_meta"):  # Django model instance
            if (
                appointment.therapist
                and appointment.therapist.user
                and hasattr(appointment.therapist.user, "expo_push_token")
            ):
                therapist_expo_token = appointment.therapist.user.expo_push_token
        elif isinstance(appointment, dict):  # Dictionary data
            therapist_data = appointment.get("therapist", {})
            if isinstance(therapist_data, dict):
                user_data = therapist_data.get("user", {})
                if isinstance(user_data, dict):
                    therapist_expo_token = user_data.get("expo_push_token")

        if therapist_expo_token:
            title = "Appointment Reminder"
            body = f"You have an appointment tomorrow at {formatted_time} with {customer_name}."

            send_push_notification(
                expo_push_token=therapist_expo_token, title=title, body=body, data=data
            )

            logger.info(f"Sent appointment reminder notification to therapist")
        else:
            logger.info("No expo push token for therapist, skipping therapist reminder")

    except Exception as e:
        logger.error(
            f"Failed to send appointment reminder notification to therapist: {str(e)}"
        )
        logger.exception("Full traceback:")

    # Mark reminder as sent to avoid duplicates - only if appointment is a Django model instance
    # Keep this at the end after both notifications are sent
    try:
        if (
            hasattr(appointment, "_meta")
            and hasattr(appointment, "save")
            and hasattr(appointment, "reminder_sent")
        ):
            appointment.reminder_sent = True
            appointment.save(update_fields=["reminder_sent"])
            logger.debug(f"Marked reminder as sent for appointment {appointment_id}")
    except Exception as e:
        logger.warning(f"Could not mark reminder as sent: {str(e)}")


def send_bulk_push_notifications(notifications_list):
    """
    Send multiple push notifications in bulk.

    Args:
        notifications_list: List of dictionaries with notification data
                          [{"expo_push_token": "...", "title": "...", "body": "...", "data": {...}}]
    """
    if not notifications_list:
        return

    url = "https://exp.host/--/api/v2/push/send"
    headers = {
        "Accept": "application/json",
        "Accept-Encoding": "gzip, deflate",
        "Content-Type": "application/json",
    }

    # Prepare bulk payload
    payloads = []
    for notification in notifications_list:
        if not notification.get("expo_push_token"):
            continue

        payload = {
            "to": notification["expo_push_token"],
            "sound": notification.get("sound", "default"),
            "title": notification["title"],
            "body": notification["body"],
            "data": notification.get("data", {}),
        }
        payloads.append(payload)

    if not payloads:
        logger.warning("No valid notifications to send in bulk")
        return

    try:
        logger.info(f"Sending {len(payloads)} push notifications in bulk")
        response = requests.post(url, json=payloads, headers=headers, timeout=30)

        if response.status_code == 200:
            result = response.json()
            logger.info(
                f"Bulk push notifications sent successfully: {len(payloads)} notifications"
            )
            return result
        else:
            error_msg = f"HTTP {response.status_code}: {response.text}"
            logger.error(f"Failed to send bulk push notifications: {error_msg}")
            raise Exception(f"Failed to send bulk push notifications: {error_msg}")

    except requests.exceptions.RequestException as e:
        logger.error(f"Network error sending bulk push notifications: {str(e)}")
        raise Exception(f"Network error sending bulk push notifications: {str(e)}")
