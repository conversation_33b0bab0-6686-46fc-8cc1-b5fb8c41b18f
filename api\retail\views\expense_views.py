from rest_framework import viewsets, status, filters
from rest_framework.response import Response
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Sum
from django.utils import timezone
from django.http import HttpResponse
import csv
import logging
from datetime import datetime

from api.retail.models import CashRegister, Expense
from api.retail.serializers import ExpenseSerializer
from api.core.permissions import IsOwnerOrReceptionist

logger = logging.getLogger(__name__)


class ExpenseViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing expenses.
    """

    queryset = Expense.objects.all().order_by("-created_at")
    serializer_class = ExpenseSerializer
    permission_classes = [IsAuthenticated, IsOwnerOrReceptionist]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ["cash_register__location", "category"]
    search_fields = ["description"]
    ordering_fields = ["created_at", "amount"]
    ordering = ["-created_at"]

    def perform_create(self, serializer):
        """Set the staff to the current user."""
        serializer.save(staff=self.request.user)

    @action(detail=False, methods=["get"])
    def by_date(self, request):
        """Get expenses for a specific date and location."""
        try:
            date_str = request.query_params.get("date")
            location = request.query_params.get("location")

            if not date_str or not location:
                return Response(
                    {"detail": "Both date and location parameters are required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            try:
                date = datetime.strptime(date_str, "%Y-%m-%d").date()
            except ValueError:
                return Response(
                    {"detail": "Invalid date format. Use YYYY-MM-DD"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Get the cash register for this date and location
            try:
                register = CashRegister.objects.get(date=date, location=location)
            except CashRegister.DoesNotExist:
                return Response(
                    {"detail": f"No cash register found for {location} on {date_str}"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            # Get expenses for this register
            expenses = Expense.objects.filter(cash_register=register).order_by(
                "-created_at"
            )

            # Group by category
            category_totals = (
                expenses.values("category")
                .annotate(total=Sum("amount"))
                .order_by("category")
            )

            return Response(
                {
                    "date": date_str,
                    "location": location,
                    "location_display": dict(CashRegister.LOCATION_CHOICES).get(
                        location, location
                    ),
                    "total": expenses.aggregate(total=Sum("amount"))["total"] or 0,
                    "category_breakdown": category_totals,
                    "expenses": ExpenseSerializer(expenses, many=True).data,
                }
            )
        except Exception as e:
            logger.error(f"Error retrieving expenses by date: {str(e)}", exc_info=True)
            return Response(
                {"detail": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=["get"])
    def export_csv(self, request):
        """Export expenses to a CSV file with filtering options."""
        try:
            # Get filtered queryset
            queryset = self.filter_queryset(self.get_queryset())

            response = HttpResponse(content_type="text/csv")
            filename = f"expenses_export_{timezone.now().strftime('%Y%m%d_%H%M%S')}.csv"
            response["Content-Disposition"] = f'attachment; filename="{filename}"'

            writer = csv.writer(response)
            writer.writerow(
                [
                    "Date",
                    "Location",
                    "Category",
                    "Description",
                    "Amount",
                    "Staff",
                    "Notes",
                ]
            )

            for expense in queryset:
                writer.writerow(
                    [
                        expense.created_at.strftime("%Y-%m-%d"),
                        expense.cash_register.get_location_display(),
                        expense.get_category_display(),
                        expense.description,
                        expense.amount,
                        f"{expense.staff.first_name} {expense.staff.last_name}",
                        expense.notes or "",
                    ]
                )

            return response
        except Exception as e:
            logger.error(f"Error exporting expenses to CSV: {str(e)}", exc_info=True)
            return Response(
                {"detail": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
