# Appointment views
from .appointment import (
    AppointmentList<PERSON>reateView,
    SplitAppointmentView,
    AppointmentDayView,
    AppointmentManageView,
    AddMinutesToAppointmentView,
    DeleteAdditionalServiceView,
    adjust_appointment_minutes,
)

# Package views
from .package import (
    ActivePackage,
    UserPackageDetailView,
    user_package_usage,
    PackageUpdateExpiryView,
    PackageDeleteView,
    PackageUpdateRemainingTimeView,
)

# Shared package views
from .shared_package import (
    ActiveSharedPackage,
    AppointmentSharedPackageView,
    CustomerActiveSharedPackageView,
    SharedPackageDetailView,
    shared_package_usage,
    SharedPackageUsersView,
    SharedPackageUserRemoveView,
)

# Unlimited package views
from .unlimited_package import (
    ActiveUnlimitedPackage,
    AppointmentUnlimitedPackageView,
    CustomerActiveUnlimitedPackageView,
    UnlimitedPackageDetailView,
    unlimited_package_usage,
)

# Customer views
from .customer import (
    CustomerListView,
    CustomerView,
    CustomerProfileView,
    CustomerAppointmentsView,
    CustomerActivePackageView,
    CustomerAllPackagesView,
    CustomerAllSharedPackagesView,
    CustomerAllUnlimitedPackagesView,
)

# Therapist views
from .therapist import (
    TherapistProfileView,
    TherapistAppointmentsView,
)

# Discount views
from .discount import (
    ApplyDiscountView,
    AvailableDiscountCodesView,
)

# Availability views
from .availability import (
    AvailableTimesForPackagesView,
    AvailableTimesForServicesView,
)

from .sales import (
    DirectPackageSaleView,
    SaleListView,
    SaleDetailView,
    SaleViewSet,
)

from .mobile_views import (
    AppointmentMobilePastView,
    AppointmentMobileTodayView,
    AppointmentMobileNextView,
)
