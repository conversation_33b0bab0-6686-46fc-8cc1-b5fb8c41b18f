# Generated by Django 4.0 on 2025-01-14 22:39

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('services', '0001_initial'),
        ('staff', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='therapistprofile',
            name='eligible_packages',
            field=models.ManyToManyField(blank=True, to='services.ServicePackage'),
        ),
        migrations.AddField(
            model_name='therapistprofile',
            name='services',
            field=models.ManyToManyField(to='services.Service'),
        ),
    ]
