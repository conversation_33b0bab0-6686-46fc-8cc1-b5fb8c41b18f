# Commission Rules Test Suite

This directory contains comprehensive test coverage for Commission Rules functionality (TC-R-001 through TC-R-013).

## Test Files

### 1. `test_commission_rules.py`
**Primary test file for basic CRUD operations and access control**

- **TC-R-001**: Commission Rule Creation Access Control (5 tests)
- **TC-R-002**: Commission Rule Update Access Control (6 tests)
- **TC-R-003**: Commission Rule Delete Access Control (5 tests)
- **TC-R-004**: Commission Rule List Access Control (7 tests)

**Total: 23 test methods**

### 2. `test_commission_rules_advanced.py`
**Advanced test file for rule configuration and activation**

- **TC-R-005**: Rule Creation with Percentage-based Commission (4 tests)
- **TC-R-006**: Rule Creation with Fixed Amount Commission (3 tests)
- **TC-R-007**: Minimum Session Threshold Enforcement (3 tests)
- **TC-R-011**: Rule Priority Setting (3 tests)
- **TC-R-009**: Rule Activation/Deactivation (4 tests)

**Total: 17 test methods**

### 3. `test_commission_rules_business_logic.py`
**Business logic test file for complex rule behavior**

- **TC-R-008**: Rules Applied After Minimum Session Threshold (4 tests)
- **TC-R-010**: Rule Deactivation Restrictions (3 tests)
- **TC-R-012**: Highest Priority Rule Application (3 tests)
- **TC-R-013**: Same Priority Rule Selection (3 tests)

**Total: 13 test methods**



## Test Coverage Summary

### 2.1 Access Control (TC-R-001 to TC-R-004) ✅
- **TC-R-001**: Only owner can create commission rules (5 tests)
- **TC-R-002**: Only owner can update commission rules (6 tests)
- **TC-R-003**: Only owner can delete commission rules (5 tests)
- **TC-R-004**: Proper access control for listing commission rules (7 tests)

### 2.2 Rule Creation and Configuration (TC-R-005 to TC-R-008) ✅
- **TC-R-005**: Rule creation with percentage-based commission (4 tests)
- **TC-R-006**: Rule creation with fixed amount commission (3 tests)
- **TC-R-007**: Minimum session threshold enforcement (3 tests)
- **TC-R-008**: Rules applied after minimum session threshold (4 tests)

### 2.3 Rule Status and Priority (TC-R-009 to TC-R-013) ✅
- **TC-R-009**: Rules can be activated/deactivated (4 tests)
- **TC-R-010**: Rule deactivation restrictions (3 tests)
- **TC-R-011**: Rule priority setting (3 tests)
- **TC-R-012**: Highest priority rule application (3 tests)
- **TC-R-013**: Same priority rule selection (3 tests)

## Running Tests

### Individual Test Cases
```bash
# Run specific test case
python test_formatter.py tc-r-001  # Rule creation access control
python test_formatter.py tc-r-002  # Rule update access control
python test_formatter.py tc-r-003  # Rule delete access control
python test_formatter.py tc-r-004  # Rule list access control
python test_formatter.py tc-r-005  # Percentage-based rules
python test_formatter.py tc-r-006  # Fixed amount rules
python test_formatter.py tc-r-007  # Minimum session threshold
python test_formatter.py tc-r-008  # Rule threshold application
python test_formatter.py tc-r-009  # Rule activation/deactivation
python test_formatter.py tc-r-010  # Rule deactivation restrictions
python test_formatter.py tc-r-011  # Rule priority setting
python test_formatter.py tc-r-012  # Priority rule application
python test_formatter.py tc-r-013  # Same priority rule selection
```

### All Commission Tests
```bash
# Run all commission tests (profiles + rules)
python test_formatter.py commission
```

### Direct pytest Commands
```bash
# Run specific test file
docker-compose exec web pytest api/commissions/tests/test_commission_rules.py -v
docker-compose exec web pytest api/commissions/tests/test_commission_rules_advanced.py -v
docker-compose exec web pytest api/commissions/tests/test_commission_rules_business_logic.py -v

# Run specific test class
docker-compose exec web pytest api/commissions/tests/test_commission_rules.py::TestCommissionRuleCreation -v

# Run specific test method
docker-compose exec web pytest api/commissions/tests/test_commission_rules.py::TestCommissionRuleCreation::test_owner_can_create_commission_rule -v
```

## Test Statistics

- **Total Test Files**: 4
- **Total Test Classes**: 13
- **Total Test Methods**: 63+
- **Coverage**: TC-R-001 through TC-R-013 (100% of specified requirements)

## Key Features Tested

### Access Control
- Owner-only permissions for create, update, delete operations
- Proper access levels for different user roles (owner, receptionist, therapist, customer)
- Authentication requirements

### Rule Configuration
- Percentage-based commission rules
- Fixed amount commission rules
- Service-specific and package-specific rules
- Global rules
- Minimum session thresholds
- Priority settings

### Business Logic
- Rule application based on session thresholds
- Priority-based rule selection
- Timestamp-based tie-breaking for same priority rules
- Rule activation/deactivation
- Deactivation restrictions

### Validation
- Input validation for all fields
- Edge case handling
- Decimal precision handling
- Extreme value testing

## Dependencies

The tests require:
- Django test framework
- pytest
- pytest-django
- Django REST Framework test client
- Docker and docker-compose for running tests

## Notes

- All tests use fixtures for consistent test data setup
- Tests are isolated and can run independently
- Mock data is created for each test to avoid dependencies
- Tests cover both positive and negative scenarios
- Error messages and status codes are validated
- Database state is verified after operations
