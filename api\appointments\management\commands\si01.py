from django.core.management.base import BaseCommand
from api.appointments.models import Sale


class Command(BaseCommand):
    help = "Generate invoice PDFs for existing sales"

    def add_arguments(self, parser):
        parser.add_argument(
            "--dry-run",
            action="store_true",
            help="Perform a dry run without saving changes",
        )

    def handle(self, *args, **options):
        dry_run = options.get("dry_run", False)

        # Get all sales without invoices
        sales_without_invoices = Sale.objects.filter().order_by("created_at")

        total_count = sales_without_invoices.count()
        success_count = 0
        error_count = 0

        self.stdout.write(f"Found {total_count} sales without invoices")

        for i, sale in enumerate(sales_without_invoices, 1):
            try:
                # Generate invoice number if needed
                if not sale.invoice_number:
                    sale.generate_invoice_number()
                    if not dry_run:
                        sale.save(update_fields=["invoice_number"])

                # Generate PDF
                if not dry_run:
                    success = sale.generate_invoice()
                    if success:
                        sale.save(update_fields=["invoice"])
                        success_count += 1
                    else:
                        error_count += 1
                else:
                    self.stdout.write(f"  Would generate invoice: {sale.invoice_number}")
                    success_count += 1
            except Exception as e:
                self.stdout.write(self.style.ERROR(f"  Error: {str(e)}"))
                error_count += 1

        action = "Would process" if dry_run else "Processed"
        self.stdout.write(
            self.style.SUCCESS(
                f"{action} {total_count} sales: {success_count} successful, {error_count} failed"
            )
        )

        if dry_run:
            self.stdout.write(
                "This was a dry run. Use without --dry-run to apply changes."
            )
