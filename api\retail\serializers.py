from rest_framework import serializers
from .models import (
    Product,
    ProductSale,
    ProductSaleItem,
    CashRegister,
    CashWithdrawal,
    CashDeposit,
    Expense,
    DailySalesReport,
)
from django.db import transaction
from django.utils import timezone

from django.contrib.auth import get_user_model

User = get_user_model()


class ProductSerializer(serializers.ModelSerializer):
    location_display = serializers.CharField(
        source="get_location_display", read_only=True
    )
    category_display = serializers.CharField(
        source="get_category_display", read_only=True
    )

    class Meta:
        model = Product
        fields = [
            "id",
            "name",
            "category",
            "category_display",
            "description",
            "price",
            "vat_percentage",
            "quantity_in_stock",
            "location",
            "location_display",
            "is_active",
            "created_at",
            "updated_at",
        ]


class ProductSaleItemSerializer(serializers.ModelSerializer):
    product_name = serializers.CharField(source="product.name", read_only=True)

    class Meta:
        model = ProductSaleItem
        fields = [
            "id",
            "product",
            "product_name",
            "quantity",
            "unit_price",
            "vat_amount",
            "total_price",
        ]
        read_only_fields = ["id", "product_name"]


class ProductSaleSerializer(serializers.ModelSerializer):
    sale_items = ProductSaleItemSerializer(many=True, required=True)
    customer_name = serializers.SerializerMethodField()
    staff_name = serializers.SerializerMethodField()
    payment_method_display = serializers.SerializerMethodField()
    status_display = serializers.SerializerMethodField()
    location_display = serializers.SerializerMethodField()

    class Meta:
        model = ProductSale
        fields = [
            "id",
            "invoice_number",
            "invoice",
            "customer",
            "customer_name",
            "staff",
            "staff_name",
            "subtotal_amount",
            "vat_amount",
            "total_amount",
            "payment_method",
            "payment_method_display",
            "status",
            "status_display",
            "location",
            "location_display",
            "notes",
            "created_at",
            "sale_items",
        ]
        read_only_fields = [
            "id",
            "invoice_number",
            "created_at",
            "customer_name",
            "staff_name",
            "location_display",
            "status_display",
            "payment_method_display",
            "staff",
        ]

    def get_customer_name(self, obj):
        """Get the full name of the customer"""

        if obj.customer:
            return f"{obj.customer.first_name} {obj.customer.last_name}"

        return "Unknown Customer"

    def get_staff_name(self, obj):
        """Get the full name of the customer"""

        if obj.staff:
            return f"{obj.staff.first_name} {obj.staff.last_name}"

        return "Unknown Staff"

    def get_payment_method_display(self, obj):
        """Get the display value for payment method"""
        payment_methods = {"CASH": "Cash", "CARD": "Card", "ONLINE_LINK": "Online Link"}
        return payment_methods.get(obj.payment_method, obj.payment_method)

    def get_status_display(self, obj):
        """Get the display value for status"""
        statuses = {
            "COMPLETED": "Completed",
            "REFUNDED": "Refunded",
            "CANCELLED": "Cancelled",
        }
        return statuses.get(obj.status, obj.status)

    def get_location_display(self, obj):
        """Get the display value for location"""
        locations = {"A": "Studio Al Warqa Mall", "B": "Studio Al Mizhar Branch"}
        return locations.get(obj.location, obj.location)

    def to_representation(self, instance):
        """Add extra fields for detailed representation"""
        rep = super().to_representation(instance)

        # Add detailed sale items with product names
        sale_items = []
        for item in instance.sale_items.all():
            item_data = {
                "id": item.id,
                "product": item.product.id,
                "product_name": item.product.name,
                "quantity": item.quantity,
                "unit_price": item.unit_price,
                "vat_amount": item.vat_amount,
                "total_price": item.total_price,
            }
            sale_items.append(item_data)

        rep["sale_items"] = sale_items

        return rep

    @transaction.atomic
    def create(self, validated_data):
        # Print debugging data
        print("Creating sale with validated data:", validated_data)

        sale_items_data = validated_data.pop("sale_items")

        # Create the sale
        sale = ProductSale.objects.create(**validated_data)

        # Create sale items and update inventory
        for item_data in sale_items_data:
            product = item_data["product"]
            quantity = item_data["quantity"]

            # Check inventory
            if product.quantity_in_stock < quantity:
                raise serializers.ValidationError(
                    f"Not enough stock for {product.name}. Available: {product.quantity_in_stock}"
                )

            # Create sale item
            ProductSaleItem.objects.create(product_sale=sale, **item_data)

            # Update inventory
            product.quantity_in_stock -= quantity
            product.save()

        return sale

    def validate(self, data):
        """
        Add additional logging to help debug validation issues.
        """
        print("Validating sale data:", data)

        if "sale_items" not in data or not data["sale_items"]:
            raise serializers.ValidationError("Sale must include at least one item")

        # Check if the customer exists if provided
        if "customer" in data and data["customer"]:
            try:
                customer = User.objects.get(id=data["customer"].id)
                print(f"Found customer: {customer.email}")
            except User.DoesNotExist:
                print(f"Customer with ID {data['customer']} not found")
                raise serializers.ValidationError(
                    f"Customer with ID {data['customer']} not found"
                )

        # Calculate subtotal, VAT, and total to verify against provided values
        try:
            calc_subtotal = sum(
                item["unit_price"] * item["quantity"] for item in data["sale_items"]
            )
            calc_vat = sum(item["vat_amount"] for item in data["sale_items"])
            calc_total = calc_subtotal

            print(
                f"Calculated values - Subtotal: {calc_subtotal}, VAT: {calc_vat}, Total: {calc_total}"
            )
            print(
                f"Provided values - Subtotal: {data['subtotal_amount']}, VAT: {data['vat_amount']}, Total: {data['total_amount']}"
            )

            # Check with small tolerance for floating point errors
            subtotal_diff = abs(calc_subtotal - data["subtotal_amount"])
            vat_diff = abs(calc_vat - data["vat_amount"])
            total_diff = abs(calc_total - data["total_amount"])

            if subtotal_diff > 0.01:
                raise serializers.ValidationError(
                    f"Calculated subtotal ({calc_subtotal}) does not match provided subtotal ({data['subtotal_amount']})"
                )

            if vat_diff > 0.01:
                raise serializers.ValidationError(
                    f"Calculated VAT ({calc_vat}) does not match provided VAT ({data['vat_amount']})"
                )

            if total_diff > 0.01:
                raise serializers.ValidationError(
                    f"Calculated total ({calc_total}) does not match provided total ({data['total_amount']})"
                )
        except Exception as e:
            raise serializers.ValidationError(f"Error validating totals: {str(e)}")

        # Validate products exist
        for item in data["sale_items"]:
            try:
                product = Product.objects.get(id=item["product"].id)
            except Product.DoesNotExist:
                raise serializers.ValidationError(
                    f"Product with ID {item['product'].id} not found"
                )
            except Exception as e:
                raise serializers.ValidationError(f"Error checking product: {str(e)}")

        return data


class CashRegisterSerializer(serializers.ModelSerializer):
    location_display = serializers.CharField(
        source="get_location_display", read_only=True
    )

    class Meta:
        model = CashRegister
        fields = [
            "id",
            "date",
            "current_balance",
            "location",
            "location_display",
            "notes",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "created_at", "updated_at", "location_display"]

    def validate(self, data):
        """
        Validate that there isn't already a register for this date and location.
        """
        if self.instance is None:  # Only for creation, not updates
            date = data.get("date")
            location = data.get("location")

            if CashRegister.objects.filter(date=date, location=location).exists():
                raise serializers.ValidationError(
                    f"A cash register for {location} on {date} already exists"
                )

        return data


class CashWithdrawalSerializer(serializers.ModelSerializer):
    staff_name = serializers.SerializerMethodField(read_only=True)
    register_location = serializers.CharField(
        source="cash_register.get_location_display", read_only=True
    )
    register_date = serializers.DateField(source="cash_register.date", read_only=True)

    class Meta:
        model = CashWithdrawal
        fields = [
            "id",
            "cash_register",
            "amount",
            "reason",
            "staff",
            "staff_name",
            "notes",
            "created_at",
            "register_location",
            "register_date",
        ]
        read_only_fields = [
            "id",
            "created_at",
            "staff_name",
            "register_location",
            "register_date",
        ]

    def get_staff_name(self, obj):
        return f"{obj.staff.first_name} {obj.staff.last_name}"

    @transaction.atomic
    def create(self, validated_data):
        cash_register = validated_data["cash_register"]
        amount = validated_data["amount"]

        # Check if there's enough cash in the register
        if cash_register.current_balance < amount:
            raise serializers.ValidationError(
                f"Not enough cash in register. Current balance: {cash_register.current_balance}"
            )

        # Update the cash register balance
        cash_register.current_balance -= amount
        cash_register.save()

        # Create the withdrawal record
        withdrawal = CashWithdrawal.objects.create(**validated_data)

        return withdrawal


class CashDepositSerializer(serializers.ModelSerializer):
    staff_name = serializers.SerializerMethodField(read_only=True)
    register_location = serializers.CharField(
        source="cash_register.get_location_display", read_only=True
    )
    register_date = serializers.DateField(source="cash_register.date", read_only=True)

    class Meta:
        model = CashDeposit
        fields = [
            "id",
            "cash_register",
            "amount",
            "reason",
            "staff",
            "staff_name",
            "notes",
            "created_at",
            "register_location",
            "register_date",
        ]
        read_only_fields = [
            "id",
            "created_at",
            "staff_name",
            "register_location",
            "register_date",
        ]

    def get_staff_name(self, obj):
        return f"{obj.staff.first_name} {obj.staff.last_name}"

    @transaction.atomic
    def create(self, validated_data):
        cash_register = validated_data["cash_register"]
        amount = validated_data["amount"]

        # Update the cash register balance
        cash_register.current_balance += amount
        cash_register.save()

        # Create the deposit record
        deposit = CashDeposit.objects.create(**validated_data)

        return deposit


class OptionalImageField(serializers.ImageField):
    def to_internal_value(self, data):
        if data in (None, ""):
            return None
        return super().to_internal_value(data)


class ExpenseSerializer(serializers.ModelSerializer):
    staff_name = serializers.SerializerMethodField(read_only=True)
    register_location = serializers.CharField(
        source="cash_register.get_location_display", read_only=True
    )
    register_date = serializers.DateField(source="cash_register.date", read_only=True)
    category_display = serializers.CharField(
        source="get_category_display", read_only=True
    )
    receipt_image = OptionalImageField(required=False, allow_null=True)

    class Meta:
        model = Expense
        fields = [
            "id",
            "cash_register",
            "description",
            "category",
            "category_display",
            "amount",
            "staff",
            "staff_name",
            "receipt_image",
            "notes",
            "created_at",
            "register_location",
            "register_date",
        ]
        read_only_fields = [
            "id",
            "created_at",
            "staff",
            "staff_name",
            "register_location",
            "register_date",
            "category_display",
        ]

    def get_staff_name(self, obj):
        return f"{obj.staff.first_name} {obj.staff.last_name}"

    @transaction.atomic
    def create(self, validated_data):
        cash_register = validated_data["cash_register"]
        amount = validated_data["amount"]

        # Check if there's enough cash in the register
        if cash_register.current_balance < amount:
            raise serializers.ValidationError(
                f"Not enough cash in register. Current balance: {cash_register.current_balance}"
            )

        # Update the cash register balance
        cash_register.current_balance -= amount
        cash_register.save()

        # Create the expense record
        expense = Expense.objects.create(**validated_data)

        return expense


class DailySalesReportSerializer(serializers.ModelSerializer):
    location_display = serializers.CharField(
        source="get_location_display", read_only=True
    )
    created_by_name = serializers.SerializerMethodField(read_only=True)

    class Meta:
        model = DailySalesReport
        fields = [
            "id",
            "date",
            "location",
            "location_display",
            "gross_sales_amount",
            "service_sales_amount",
            "package_sales_amount",
            "product_sales_amount",
            "cash_sales_amount",
            "card_sales_amount",
            "online_link_amount",
            "vat_amount",
            "card_charges_amount",
            "link_charges_amount",
            "net_sales_amount",
            "expenses_total",
            "cash_withdrawals_total",
            "starting_cash_balance",
            "ending_cash_balance",
            "created_by",
            "created_by_name",
            "notes",
            "email_sent",
            "created_at",
            "updated_at",
        ]
        read_only_fields = [
            "id",
            "created_at",
            "updated_at",
            "location_display",
            "created_by_name",
        ]

    def get_created_by_name(self, obj):
        return f"{obj.created_by.first_name} {obj.created_by.last_name}"

    def validate(self, data):
        """
        Validate that there isn't already a report for this date and location.
        """
        if self.instance is None:  # Only for creation, not updates
            date = data.get("date")
            location = data.get("location")

            if DailySalesReport.objects.filter(date=date, location=location).exists():
                raise serializers.ValidationError(
                    f"A sales report for {location} on {date} already exists"
                )

        return data
