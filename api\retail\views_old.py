from rest_framework import viewsets, status, generics, filters
from django.shortcuts import render
from rest_framework.response import Response
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q, Sum, F, ExpressionWrapper, DecimalField
from django.db import transaction
from django.utils import timezone
from django.shortcuts import get_object_or_404
from django.http import HttpResponse
import csv
import logging
from datetime import timedelta, datetime
from api.core.permissions import IsOwnerOrReceptionist


from .models import (
    Product,
    ProductSale,
    ProductSaleItem,
    CashRegister,
    CashWithdrawal,
    CashDeposit,
    Expense,
    DailySalesReport,
)
from .serializers import (
    ProductSerializer,
    ProductSaleSerializer,
    ProductSaleItemSerializer,
    CashRegisterSerializer,
    CashWithdrawalSerializer,
    CashDepositSerializer,
    ExpenseSerializer,
    DailySalesReportSerializer,
)

# Add custom permissions
from api.core.permissions import IsOwnerOrReceptionist

from decimal import Decimal, getcontext

# Set the precision for Decimal calculations
getcontext().prec = 2

logger = logging.getLogger(__name__)


class ProductViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing products.
    Staff users can create, update, and delete products.
    """

    queryset = Product.objects.all()
    serializer_class = ProductSerializer
    permission_classes = [IsAuthenticated, IsOwnerOrReceptionist]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ["category", "location", "is_active"]
    search_fields = ["name", "description"]
    ordering_fields = ["name", "price", "quantity_in_stock", "created_at"]
    ordering = ["name"]

    def get_queryset(self):
        """Filter products by location if specified."""
        queryset = super().get_queryset()
        location = self.request.query_params.get("location")

        if location:
            queryset = queryset.filter(Q(location=location) | Q(location="BOTH"))

        # Filter for low stock products
        low_stock = self.request.query_params.get("low_stock")
        if low_stock:
            try:
                threshold = int(low_stock)
                queryset = queryset.filter(quantity_in_stock__lte=threshold)
            except ValueError:
                # If not a valid integer, just filter for any low stock
                queryset = queryset.filter(quantity_in_stock__lt=10)

        return queryset

    @action(detail=False, methods=["post"])
    def update_stock(self, request):
        """
        Update stock quantity for multiple products at once.

        Expected payload:
        {
            "product_updates": [
                {"id": 1, "quantity_change": 10},
                {"id": 2, "quantity_change": -5}
            ]
        }
        """
        try:
            product_updates = request.data.get("product_updates", [])

            if not product_updates:
                return Response(
                    {"detail": "No product updates provided"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            updated_products = []
            with transaction.atomic():
                for update in product_updates:
                    product_id = update.get("id")
                    quantity_change = update.get("quantity_change", 0)

                    try:
                        product = Product.objects.get(id=product_id)

                        # Prevent negative stock
                        if product.quantity_in_stock + quantity_change < 0:
                            return Response(
                                {
                                    "detail": f"Cannot reduce {product.name} stock below zero. Current stock: {product.quantity_in_stock}"
                                },
                                status=status.HTTP_400_BAD_REQUEST,
                            )

                        product.quantity_in_stock += quantity_change
                        product.save()
                        updated_products.append(
                            {
                                "id": product.id,
                                "name": product.name,
                                "new_quantity": product.quantity_in_stock,
                            }
                        )
                    except Product.DoesNotExist:
                        return Response(
                            {"detail": f"Product with id {product_id} not found"},
                            status=status.HTTP_404_NOT_FOUND,
                        )

            return Response(
                {
                    "detail": f"Updated stock for {len(updated_products)} products",
                    "products": updated_products,
                }
            )
        except Exception as e:
            logger.error(f"Error updating product stock: {str(e)}", exc_info=True)
            return Response(
                {"detail": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=["get"])
    def export_csv(self, request):
        """Export all products to a CSV file."""
        try:
            # Get filtered queryset
            queryset = self.filter_queryset(self.get_queryset())

            response = HttpResponse(content_type="text/csv")
            filename = f"products_export_{timezone.now().strftime('%Y%m%d_%H%M%S')}.csv"
            response["Content-Disposition"] = f'attachment; filename="{filename}"'

            writer = csv.writer(response)
            writer.writerow(
                [
                    "ID",
                    "Name",
                    "Category",
                    "Description",
                    "Price",
                    "VAT %",
                    "Stock Quantity",
                    "Location",
                    "Active",
                    "Created At",
                ]
            )

            for product in queryset:
                writer.writerow(
                    [
                        product.id,
                        product.name,
                        product.get_category_display(),
                        product.description or "",
                        product.price,
                        product.vat_percentage,
                        product.quantity_in_stock,
                        product.get_location_display(),
                        "Yes" if product.is_active else "No",
                        product.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                    ]
                )

            return response
        except Exception as e:
            logger.error(f"Error exporting products to CSV: {str(e)}", exc_info=True)
            return Response(
                {"detail": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ProductSaleViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing product sales.
    Staff users can create and manage sales.
    """

    queryset = ProductSale.objects.all().order_by("-created_at")
    serializer_class = ProductSaleSerializer
    permission_classes = [IsAuthenticated, IsOwnerOrReceptionist]

    def create(self, request, *args, **kwargs):
        """Custom create method to properly handle sale items"""
        try:
            # Log the incoming data for debugging
            print("Received sale data:", request.data)

            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)
            self.perform_create(serializer)
            headers = self.get_success_headers(serializer.data)
            return Response(
                serializer.data, status=status.HTTP_201_CREATED, headers=headers
            )
        except Exception as e:
            # Log the detailed error
            print(f"Error creating sale: {str(e)}")
            import traceback

            traceback.print_exc()
            return Response({"detail": str(e)}, status=status.HTTP_400_BAD_REQUEST)

    def perform_create(self, serializer):
        """
        Set the staff to the current user and update cash register for cash sales.
        """
        # Create the sale
        sale = serializer.save(staff=self.request.user)

        # If this is a cash sale, update the cash register
        if sale.payment_method == "CASH" and sale.status == "COMPLETED":
            # Find today's cash register for this location
            today = timezone.now().date()
            try:
                cash_register = CashRegister.objects.get(
                    date=today, location=sale.location
                )

                # Add the cash sale amount to the register
                cash_register.current_balance += sale.total_amount
                cash_register.save()
                print(
                    f"Cash register {cash_register.id} updated: +{sale.total_amount} from sale {sale.invoice_number}"
                )
            except CashRegister.DoesNotExist:
                # Log warning if no cash register is found
                logger.warning(
                    f"Cash register not found for location {sale.location} on {today} "
                    f"while processing cash sale {sale.invoice_number}"
                )

    @action(detail=True, methods=["post"])
    def refund(self, request, pk=None):
        """Process a refund for a sale."""
        try:
            sale = self.get_object()

            if sale.status != "COMPLETED":
                return Response(
                    {"detail": f"Cannot refund a sale with status {sale.status}"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            with transaction.atomic():
                # Update product inventory
                for item in sale.sale_items.all():
                    product = item.product
                    product.quantity_in_stock += item.quantity
                    product.save()

                # Update sale status
                sale.status = "REFUNDED"
                sale.save()

                # If it was a cash sale, we should handle the cash register
                if sale.payment_method == "CASH":
                    # Find today's cash register for this location
                    today = timezone.now().date()
                    try:
                        cash_register = CashRegister.objects.get(
                            date=today, location=sale.location
                        )

                        # If refunding cash, subtract from register
                        cash_register.current_balance -= sale.total_amount
                        cash_register.save()
                    except CashRegister.DoesNotExist:
                        # Just log this - we won't block the refund if register not found
                        logger.warning(
                            f"Cash register not found for location {sale.location} on {today} "
                            f"while processing refund for sale {sale.invoice_number}"
                        )

            return Response(
                {
                    "detail": f"Sale {sale.invoice_number} has been refunded",
                    "sale": ProductSaleSerializer(sale).data,
                }
            )
        except Exception as e:
            logger.error(f"Error processing refund: {str(e)}", exc_info=True)
            return Response(
                {"detail": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=["get"])
    def export_csv(self, request):
        """Export sales to a CSV file with filtering options."""
        try:
            # Get filtered queryset
            queryset = self.filter_queryset(self.get_queryset())

            response = HttpResponse(content_type="text/csv")
            filename = f"sales_export_{timezone.now().strftime('%Y%m%d_%H%M%S')}.csv"
            response["Content-Disposition"] = f'attachment; filename="{filename}"'

            writer = csv.writer(response)
            writer.writerow(
                [
                    "Invoice Number",
                    "Date",
                    "Time",
                    "Customer",
                    "Payment Method",
                    "Status",
                    "Location",
                    "Subtotal",
                    "VAT",
                    "Total",
                    "Items",
                ]
            )

            for sale in queryset:
                # Format the items as a string
                items_str = "; ".join(
                    [
                        f"{item.product.name} x{item.quantity}"
                        for item in sale.sale_items.all()
                    ]
                )

                # Format customer name
                customer_name = ""
                if sale.customer:
                    customer_name = (
                        f"{sale.customer.first_name} {sale.customer.last_name}"
                    )

                writer.writerow(
                    [
                        sale.invoice_number,
                        sale.created_at.strftime("%Y-%m-%d"),
                        sale.created_at.strftime("%H:%M:%S"),
                        customer_name,
                        sale.get_payment_method_display(),
                        sale.get_status_display(),
                        sale.get_location_display(),
                        sale.subtotal_amount,
                        sale.vat_amount,
                        sale.total_amount,
                        items_str,
                    ]
                )

            return response
        except Exception as e:
            logger.error(f"Error exporting sales to CSV: {str(e)}", exc_info=True)
            return Response(
                {"detail": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=["get"])
    def daily_summary(self, request):
        """Get a summary of today's sales."""
        try:
            location = request.query_params.get("location")
            if not location:
                return Response(
                    {"detail": "Location parameter is required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            today = timezone.now().date()

            # Filter sales for today and the specified location
            sales = ProductSale.objects.filter(
                created_at__date=today, location=location, status="COMPLETED"
            )

            # Calculate summary stats
            total_sales = sales.count()
            total_revenue = sales.aggregate(total=Sum("total_amount"))["total"] or 0

            # Breakdown by payment method
            payment_breakdown = (
                sales.values("payment_method")
                .annotate(count=Sum("id"), total=Sum("total_amount"))
                .order_by("payment_method")
            )

            # Format payment methods for display
            payment_methods = {
                "CASH": "Cash",
                "CARD": "Card",
                "ONLINE_LINK": "Online Link",
            }
            for item in payment_breakdown:
                method = item["payment_method"]
                item["payment_method_display"] = payment_methods.get(method, method)

            # Top selling products
            top_products = (
                ProductSaleItem.objects.filter(product_sale__in=sales)
                .values("product__name")
                .annotate(quantity=Sum("quantity"), revenue=Sum("total_price"))
                .order_by("-quantity")[:5]
            )

            return Response(
                {
                    "date": today.isoformat(),
                    "location": location,
                    "location_display": dict(ProductSale.LOCATION_CHOICES).get(
                        location, location
                    ),
                    "total_sales": total_sales,
                    "total_revenue": total_revenue,
                    "payment_breakdown": payment_breakdown,
                    "top_products": top_products,
                }
            )
        except Exception as e:
            logger.error(f"Error generating daily sales summary: {str(e)}", exc_info=True)
            return Response(
                {"detail": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class CashRegisterViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing cash registers.
    Staff users can create and manage cash registers.
    """

    queryset = CashRegister.objects.all().order_by("-date")
    serializer_class = CashRegisterSerializer
    permission_classes = [IsAuthenticated, IsOwnerOrReceptionist]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ["location", "date"]  # Ensure date is included in filter fields
    ordering_fields = ["date"]
    ordering = ["-date"]

    def list(self, request, *args, **kwargs):
        """
        Override list to add transactions to response when date and location are specified.
        """
        # Get query parameters
        date_str = request.query_params.get("date")
        location = request.query_params.get("location")

        # If both date and location are provided, get detailed info for that specific register
        if date_str and location:
            try:
                date = datetime.strptime(date_str, "%Y-%m-%d").date()

                try:
                    register = CashRegister.objects.get(date=date, location=location)
                    from api.appointments.models import Appointment

                    # Get sales for this date and location
                    sales = ProductSale.objects.filter(
                        created_at__date=date,
                        location=location,
                        status="COMPLETED",
                        payment_method="CASH",
                    )
                    cash_sales_total = sales.aggregate(total=Sum("total_amount"))[
                        "total"
                    ] or 0 + (
                        Appointment.objects.filter(
                            date=date,
                            location=location,
                            status="check_in",
                            payment_method="cash",
                        ).aggregate(total=Sum("total_price"))["total"]
                        or 0
                    )

                    # Get withdrawals, deposits, and expenses
                    withdrawals = CashWithdrawal.objects.filter(cash_register=register)
                    withdrawals_total = (
                        withdrawals.aggregate(total=Sum("amount"))["total"] or 0
                    )

                    deposits = CashDeposit.objects.filter(cash_register=register)
                    deposits_total = deposits.aggregate(total=Sum("amount"))["total"] or 0

                    expenses = Expense.objects.filter(cash_register=register)
                    expenses_total = expenses.aggregate(total=Sum("amount"))["total"] or 0

                    # Include additional info in the response
                    data = CashRegisterSerializer(register).data
                    data.update(
                        {
                            "cash_sales_total": cash_sales_total,
                            "withdrawals_total": withdrawals_total,
                            "deposits_total": deposits_total,
                            "expenses_total": expenses_total,
                            "transactions": {
                                "withdrawals": CashWithdrawalSerializer(
                                    withdrawals, many=True
                                ).data,
                                "deposits": CashDepositSerializer(
                                    deposits, many=True
                                ).data,
                                "expenses": ExpenseSerializer(expenses, many=True).data,
                            },
                        }
                    )

                    return Response(data)

                except CashRegister.DoesNotExist:
                    return Response(
                        {
                            "detail": f"No cash register found for {location} on {date_str}"
                        },
                        status=status.HTTP_404_NOT_FOUND,
                    )

            except ValueError:
                return Response(
                    {"detail": "Invalid date format. Use YYYY-MM-DD"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

        # If not filtering by both date and location, use the standard list method
        return super().list(request, *args, **kwargs)


class CashWithdrawalViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing cash withdrawals.
    """

    queryset = CashWithdrawal.objects.all().order_by("-created_at")
    serializer_class = CashWithdrawalSerializer
    permission_classes = [IsAuthenticated, IsOwnerOrReceptionist]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ["cash_register__location"]
    ordering_fields = ["created_at", "amount"]
    ordering = ["-created_at"]

    def perform_create(self, serializer):
        """Set the staff to the current user."""
        serializer.save(staff=self.request.user)

    @action(detail=False, methods=["get"])
    def by_date(self, request):
        """Get withdrawals for a specific date and location."""
        try:
            date_str = request.query_params.get("date")
            location = request.query_params.get("location")

            if not date_str or not location:
                return Response(
                    {"detail": "Both date and location parameters are required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            try:
                date = datetime.strptime(date_str, "%Y-%m-%d").date()
            except ValueError:
                return Response(
                    {"detail": "Invalid date format. Use YYYY-MM-DD"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Get the cash register for this date and location
            try:
                register = CashRegister.objects.get(date=date, location=location)
            except CashRegister.DoesNotExist:
                return Response(
                    {"detail": f"No cash register found for {location} on {date_str}"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            # Get withdrawals for this register
            withdrawals = CashWithdrawal.objects.filter(cash_register=register).order_by(
                "-created_at"
            )

            return Response(
                {
                    "date": date_str,
                    "location": location,
                    "location_display": dict(CashRegister.LOCATION_CHOICES).get(
                        location, location
                    ),
                    "total": withdrawals.aggregate(total=Sum("amount"))["total"] or 0,
                    "withdrawals": CashWithdrawalSerializer(withdrawals, many=True).data,
                }
            )
        except Exception as e:
            logger.error(f"Error retrieving withdrawals by date: {str(e)}", exc_info=True)
            return Response(
                {"detail": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class CashDepositViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing cash deposits.
    """

    queryset = CashDeposit.objects.all().order_by("-created_at")
    serializer_class = CashDepositSerializer
    permission_classes = [IsAuthenticated, IsOwnerOrReceptionist]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ["cash_register__location"]
    ordering_fields = ["created_at", "amount"]
    ordering = ["-created_at"]

    def perform_create(self, serializer):
        """Set the staff to the current user."""
        serializer.save(staff=self.request.user)

    @action(detail=False, methods=["get"])
    def by_date(self, request):
        """Get deposits for a specific date and location."""
        try:
            date_str = request.query_params.get("date")
            location = request.query_params.get("location")

            if not date_str or not location:
                return Response(
                    {"detail": "Both date and location parameters are required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            try:
                date = datetime.strptime(date_str, "%Y-%m-%d").date()
            except ValueError:
                return Response(
                    {"detail": "Invalid date format. Use YYYY-MM-DD"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Get the cash register for this date and location
            try:
                register = CashRegister.objects.get(date=date, location=location)
            except CashRegister.DoesNotExist:
                return Response(
                    {"detail": f"No cash register found for {location} on {date_str}"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            # Get deposits for this register
            deposits = CashDeposit.objects.filter(cash_register=register).order_by(
                "-created_at"
            )

            return Response(
                {
                    "date": date_str,
                    "location": location,
                    "location_display": dict(CashRegister.LOCATION_CHOICES).get(
                        location, location
                    ),
                    "total": deposits.aggregate(total=Sum("amount"))["total"] or 0,
                    "deposits": CashDepositSerializer(deposits, many=True).data,
                }
            )
        except Exception as e:
            logger.error(f"Error retrieving deposits by date: {str(e)}", exc_info=True)
            return Response(
                {"detail": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ExpenseViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing expenses.
    """

    queryset = Expense.objects.all().order_by("-created_at")
    serializer_class = ExpenseSerializer
    permission_classes = [IsAuthenticated, IsOwnerOrReceptionist]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ["cash_register__location", "category"]
    search_fields = ["description"]
    ordering_fields = ["created_at", "amount"]
    ordering = ["-created_at"]

    def perform_create(self, serializer):
        """Set the staff to the current user."""
        serializer.save(staff=self.request.user)

    @action(detail=False, methods=["get"])
    def by_date(self, request):
        """Get expenses for a specific date and location."""
        try:
            date_str = request.query_params.get("date")
            location = request.query_params.get("location")

            if not date_str or not location:
                return Response(
                    {"detail": "Both date and location parameters are required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            try:
                date = datetime.strptime(date_str, "%Y-%m-%d").date()
            except ValueError:
                return Response(
                    {"detail": "Invalid date format. Use YYYY-MM-DD"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Get the cash register for this date and location
            try:
                register = CashRegister.objects.get(date=date, location=location)
            except CashRegister.DoesNotExist:
                return Response(
                    {"detail": f"No cash register found for {location} on {date_str}"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            # Get expenses for this register
            expenses = Expense.objects.filter(cash_register=register).order_by(
                "-created_at"
            )

            # Group by category
            category_totals = (
                expenses.values("category")
                .annotate(total=Sum("amount"))
                .order_by("category")
            )

            return Response(
                {
                    "date": date_str,
                    "location": location,
                    "location_display": dict(CashRegister.LOCATION_CHOICES).get(
                        location, location
                    ),
                    "total": expenses.aggregate(total=Sum("amount"))["total"] or 0,
                    "category_breakdown": category_totals,
                    "expenses": ExpenseSerializer(expenses, many=True).data,
                }
            )
        except Exception as e:
            logger.error(f"Error retrieving expenses by date: {str(e)}", exc_info=True)
            return Response(
                {"detail": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=["get"])
    def export_csv(self, request):
        """Export expenses to a CSV file with filtering options."""
        try:
            # Get filtered queryset
            queryset = self.filter_queryset(self.get_queryset())

            response = HttpResponse(content_type="text/csv")
            filename = f"expenses_export_{timezone.now().strftime('%Y%m%d_%H%M%S')}.csv"
            response["Content-Disposition"] = f'attachment; filename="{filename}"'

            writer = csv.writer(response)
            writer.writerow(
                [
                    "Date",
                    "Location",
                    "Category",
                    "Description",
                    "Amount",
                    "Staff",
                    "Notes",
                ]
            )

            for expense in queryset:
                writer.writerow(
                    [
                        expense.created_at.strftime("%Y-%m-%d"),
                        expense.cash_register.get_location_display(),
                        expense.get_category_display(),
                        expense.description,
                        expense.amount,
                        f"{expense.staff.first_name} {expense.staff.last_name}",
                        expense.notes or "",
                    ]
                )

            return response
        except Exception as e:
            logger.error(f"Error exporting expenses to CSV: {str(e)}", exc_info=True)
            return Response(
                {"detail": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class DailySalesReportViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing daily sales reports.
    """

    queryset = DailySalesReport.objects.all().order_by("-date")
    serializer_class = DailySalesReportSerializer
    permission_classes = [IsAuthenticated, IsOwnerOrReceptionist]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ["location"]
    ordering_fields = ["date"]
    ordering = ["-date"]

    def perform_create(self, serializer):
        """Set the created_by to the current user."""
        serializer.save(created_by=self.request.user)

    @action(detail=False, methods=["post"])
    def generate(self, request):
        """
        Generate or update a daily sales report for a specific date and location.
        """
        try:
            date_str = request.data.get("date")
            location = request.data.get("location")

            if not location:
                return Response(
                    {"detail": "Location parameter is required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            if date_str:
                try:
                    date = datetime.strptime(date_str, "%Y-%m-%d").date()
                except ValueError:
                    return Response(
                        {"detail": "Invalid date format. Use YYYY-MM-DD"},
                        status=status.HTTP_400_BAD_REQUEST,
                    )
            else:
                date = timezone.now().date()

            # Get the cash register for this date
            try:
                cash_register = CashRegister.objects.get(date=date, location=location)
            except CashRegister.DoesNotExist:
                return Response(
                    {"detail": f"No cash register found for {location} on {date}"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            # Get sales data
            sales_filter = Q(created_at__date=date, location=location, status="COMPLETED")

            # Total sales
            from api.appointments.models import Appointment

            all_product_sales = ProductSale.objects.filter(sales_filter)

            service_sales_filter = Q(
                date=date,
                location=location,
                status="check_in",
            )
            # Service sales from appointments
            service_sales = (
                Appointment.objects.filter(service_sales_filter).aggregate(
                    total=Sum("total_price")
                )["total"]
                or 0
            )
            print("XDDD", service_sales)
            all_product_sales_amount = (
                all_product_sales.aggregate(total=Sum("total_amount"))["total"] or 0
            )
            gross_sales = service_sales + all_product_sales_amount

            # VAT amount
            vat_amount = gross_sales * Decimal("0.05")

            # Payment method breakdown
            # Payment method breakdown including appointments
            cash_sales = (
                all_product_sales.filter(payment_method="CASH").aggregate(
                    total=Sum("total_amount")
                )["total"]
                or 0
            ) + (
                Appointment.objects.filter(
                    date=date, location=location, status="check_in", payment_method="cash"
                ).aggregate(total=Sum("total_price"))["total"]
                or 0
            )

            print("CASH SALES", cash_sales)
            card_sales = (
                all_product_sales.filter(payment_method="CARD").aggregate(
                    total=Sum("total_amount")
                )["total"]
                or 0
            ) + (
                Appointment.objects.filter(
                    date=date, location=location, status="check_in", payment_method="card"
                ).aggregate(total=Sum("total_price"))["total"]
                or 0
            )

            online_sales = (
                all_product_sales.filter(payment_method="ONLINE_LINK").aggregate(
                    total=Sum("total_amount")
                )["total"]
                or 0
            ) + (
                Appointment.objects.filter(
                    date=date,
                    location=location,
                    status="check_in",
                    payment_method="link",
                ).aggregate(total=Sum("total_price"))["total"]
                or 0
            )
            print("ONLINE SALES", online_sales)
            # Calculate charges
            card_charges = Decimal(str(card_sales)) * Decimal("0.021")  # 2.1%
            link_charges = Decimal(str(online_sales)) * Decimal("0.03")  # 3%

            # Get expense and withdrawal totals
            expenses_total = (
                Expense.objects.filter(cash_register=cash_register).aggregate(
                    total=Sum("amount")
                )["total"]
                or 0
            )

            withdrawals_total = (
                CashWithdrawal.objects.filter(cash_register=cash_register).aggregate(
                    total=Sum("amount")
                )["total"]
                or 0
            )

            # Calculate net sales
            net_sales = (
                Decimal(str(gross_sales)) - card_charges - link_charges - vat_amount
            )

            # Find or create the report
            report, created = DailySalesReport.objects.update_or_create(
                date=date,
                location=location,
                defaults={
                    "gross_sales_amount": gross_sales,
                    "service_sales_amount": service_sales,
                    "product_sales_amount": all_product_sales_amount,
                    "cash_sales_amount": cash_sales,
                    "card_sales_amount": card_sales,
                    "online_link_amount": online_sales,
                    "vat_amount": vat_amount,
                    "card_charges_amount": card_charges,
                    "link_charges_amount": link_charges,
                    "net_sales_amount": net_sales,
                    "expenses_total": expenses_total,
                    "cash_withdrawals_total": withdrawals_total,
                    "starting_cash_balance": cash_register.current_balance
                    - cash_sales
                    + expenses_total
                    + withdrawals_total,
                    "ending_cash_balance": cash_register.current_balance,
                    "created_by": request.user,
                    "notes": f"Updated on {timezone.now().strftime('%Y-%m-%d %H:%M:%S')}",
                },
            )

            return Response(
                DailySalesReportSerializer(report).data,
                status=status.HTTP_201_CREATED if created else status.HTTP_200_OK,
            )

        except Exception as e:
            logger.error(f"Error generating daily report: {str(e)}", exc_info=True)
            return Response(
                {"detail": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=["post"])
    def send_email(self, request, pk=None):
        """Send the report as an email."""
        try:
            report = self.get_object()

            # Mark as sent
            report.email_sent = True
            report.save()

            # Here you would implement the actual email sending logic
            # For now, we'll just return a success message

            return Response(
                {
                    "detail": f"Report for {report.get_location_display()} on {report.date} has been sent",
                    "email_sent": True,
                }
            )
        except Exception as e:
            logger.error(f"Error sending report email: {str(e)}", exc_info=True)
            return Response(
                {"detail": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=["get"])
    def export_html(self, request, pk=None):
        """Render the report as a Material Design HTML page."""
        try:
            report = self.get_object()
            return render(request, "reports/daily_sales_report.html", {"report": report})
        except Exception as e:
            logger.error(f"Error rendering report as HTML: {str(e)}", exc_info=True)
            return Response(
                {"detail": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
