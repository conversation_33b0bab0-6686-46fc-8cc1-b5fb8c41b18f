# Generated by Django 4.2.19 on 2025-02-08 06:56

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('appointments', '0012_activity'),
    ]

    operations = [
        migrations.AddField(
            model_name='userpackage',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, default=django.utils.timezone.now),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='userpackage',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AlterField(
            model_name='activity',
            name='activity_type',
            field=models.CharField(choices=[('booking', 'Booking'), ('confirmation', 'Confirmation'), ('cancellation', 'Cancellation')], max_length=20),
        ),
        migrations.AlterField(
            model_name='activity',
            name='customer',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='customer', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='activity',
            name='staff',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='staff', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='appointment',
            name='status',
            field=models.CharField(choices=[('booked', 'Booked'), ('check_in', 'check_in'), ('no_show', 'No-Show'), ('in_edit', 'In Edit')], default='booked', max_length=10),
        ),
    ]
