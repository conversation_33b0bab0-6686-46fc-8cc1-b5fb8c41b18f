from django.core.management.base import BaseCommand
from django.db import transaction
from api.appointments.models import Sale
from datetime import datetime
import re
import os
from django.conf import settings


class Command(BaseCommand):
    help = "Regenerates invoice numbers and PDFs for all existing sales"

    def add_arguments(self, parser):
        parser.add_argument(
            "--dry-run",
            action="store_true",
            help="Run in dry-run mode without making changes",
        )
        parser.add_argument(
            "--batch-size",
            type=int,
            default=100,
            help="Number of sales to process in each batch",
        )
        parser.add_argument(
            "--location",
            type=str,
            choices=["A", "B", "all"],
            default="all",
            help="Process only sales from specific location (A, B, or all)",
        )

    def handle(self, *args, **options):
        dry_run = options.get("dry-run", False)
        batch_size = options.get("batch-size", 100)
        location_filter = options.get("location", "all")

        if dry_run:
            self.stdout.write(
                self.style.WARNING("Running in DRY RUN mode - no changes will be made")
            )

        # Create location filter query
        location_query = {}
        if location_filter != "all":
            location_query["location"] = location_filter

        # Get total count of sales
        total_sales = Sale.objects.filter(**location_query).count()
        self.stdout.write(self.style.SUCCESS(f"Found {total_sales} sales to process"))

        # Track counters
        processed_count = 0
        updated_count = 0
        error_count = 0

        # Process in batches to avoid memory issues
        for offset in range(0, total_sales, batch_size):
            self.stdout.write(f"Processing batch {offset//batch_size + 1}...")

            # Get batch of sales
            sales_batch = Sale.objects.filter(**location_query).order_by("created_at")[
                offset : offset + batch_size
            ]

            # Dictionary to track highest invoice number per location and year
            highest_numbers = {}

            # First pass: reset all invoice numbers to ensure no conflicts during regeneration
            if not dry_run:
                with transaction.atomic():
                    for sale in sales_batch:
                        # Store the original invoice number for logging
                        sale.original_invoice_number = sale.invoice_number

                        # Reset the invoice number to null
                        # This is needed to prevent unique constraint violations during regeneration
                        if sale.invoice_number:
                            Sale.objects.filter(pk=sale.pk).update(invoice_number=None)

            # Second pass: generate new invoice numbers
            for sale in sales_batch:
                processed_count += 1
                try:
                    if not dry_run:
                        with transaction.atomic():
                            # Determine location code
                            location_code = "AW" if sale.location == "A" else "AM"

                            # Get year from sale creation date (not current year)
                            year = sale.created_at.strftime("%y")

                            # Get highest number for this location and year
                            key = f"{location_code}-{year}"
                            if key not in highest_numbers:
                                highest_numbers[key] = 0

                            # Increment counter
                            highest_numbers[key] += 1
                            seq_number = f"{highest_numbers[key]:04d}"

                            # Create the new invoice number
                            new_invoice_number = f"INV-{location_code}-{year}{seq_number}"

                            # Check for existing invoices with this number
                            while Sale.objects.filter(
                                invoice_number=new_invoice_number
                            ).exists():
                                highest_numbers[key] += 1
                                seq_number = f"{highest_numbers[key]:04d}"
                                new_invoice_number = (
                                    f"INV-{location_code}-{year}{seq_number}"
                                )

                            # Update sale with new invoice number
                            original_num = (
                                getattr(sale, "original_invoice_number", None) or "None"
                            )
                            self.stdout.write(
                                f"Updating Sale:{sale.id} invoice: {original_num} → {new_invoice_number}"
                            )

                            # Update the invoice number
                            Sale.objects.filter(pk=sale.pk).update(
                                invoice_number=new_invoice_number
                            )
                            sale.refresh_from_db()

                            # Remove old invoice file if it exists
                            if sale.invoice:
                                try:
                                    if os.path.exists(sale.invoice.path):
                                        os.remove(sale.invoice.path)
                                except (ValueError, FileNotFoundError):
                                    # File may not exist or path might be invalid
                                    pass

                            # Regenerate invoice PDF
                            sale.generate_invoice()
                            updated_count += 1
                    else:
                        # In dry run mode, just print what would be done
                        location_code = "AW" if sale.location == "A" else "AM"
                        year = sale.created_at.strftime("%y")
                        self.stdout.write(
                            f"Would update Sale:{sale.id} invoice (current: {sale.invoice_number or 'None'})"
                        )

                except Exception as e:
                    error_count += 1
                    self.stdout.write(
                        self.style.ERROR(f"❌ Error updating Sale:{sale.id} - {str(e)}")
                    )

                # Progress report
                if processed_count % 10 == 0 or processed_count == total_sales:
                    self.stdout.write(
                        f"Processed {processed_count}/{total_sales} sales..."
                    )

        # Summary
        self.stdout.write("=" * 50)
        self.stdout.write(self.style.SUCCESS(f"Processed {processed_count} sales"))
        if not dry_run:
            self.stdout.write(
                self.style.SUCCESS(f"Successfully updated {updated_count} sales")
            )
        if error_count:
            self.stdout.write(self.style.ERROR(f"Encountered {error_count} errors"))

        # Reminder to update the model method
        self.stdout.write(
            self.style.WARNING(
                "Remember to update the Sale.generate_invoice_number method in your models.py to use a more robust approach!"
            )
        )
