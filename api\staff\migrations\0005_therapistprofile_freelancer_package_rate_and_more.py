# Generated by Django 4.2.19 on 2025-05-24 05:39

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('staff', '0004_therapistprofile_is_active'),
    ]

    operations = [
        migrations.AddField(
            model_name='therapistprofile',
            name='freelancer_package_rate',
            field=models.DecimalField(decimal_places=2, default=40.0, help_text='Percentage commission for freelancer packages (default 40%)', max_digits=5),
        ),
        migrations.AddField(
            model_name='therapistprofile',
            name='freelancer_service_rate',
            field=models.DecimalField(decimal_places=2, default=50.0, help_text='Percentage commission for freelancer services (default 50%)', max_digits=5),
        ),
        migrations.AddField(
            model_name='therapistprofile',
            name='is_freelancer',
            field=models.BooleanField(default=False),
        ),
    ]
