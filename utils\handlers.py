import logging
import traceback
from rest_framework.views import exception_handler
from rest_framework.exceptions import APIException

# Initialize the logger
# exception_logger = logging.getLogger("exception_logger")


class GenericAPIException(APIException):
    status_code = 500
    default_detail = "A server error occurred."
    default_code = "error"


def custom_exception_handler(exc, context):
    # Log the exception details
    request = context.get("request")
    user = getattr(request, "user", None)
    user_email = (
        getattr(user, "email", "Anonymous")
        if user and not user.is_anonymous
        else "Anonymous"
    )

    # Capture the traceback
    tb = traceback.format_exc()

    # exception_logger.error(
    #     f"Exception: {request.method} {request.path} by {user_email} - {str(exc)}\n{tb}"
    # )

    raise GenericAPIException()
