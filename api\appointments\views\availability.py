from datetime import datetime, timed<PERSON>ta
import calendar
from django.db.models import Q, Sum
from django.utils.dateparse import parse_date
from rest_framework.exceptions import ValidationError
from rest_framework import status
from rest_framework import generics
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from rest_framework.pagination import PageNumberPagination
from rest_framework.views import APIView
from django.shortcuts import get_object_or_404
from django.contrib.auth import get_user_model
from rest_framework.filters import SearchFilter
from django.http import HttpResponse
from django_filters.rest_framework import DjangoFilterBackend
from api.appointments.models import (
    Activity,
    Appointment,
    AppointmentService,
    UserPackage,
    SharedPackage,
    SharedPackageUser,
    UnlimitedPackage,
    Discount,
    RewardBalance,
)
from api.services.models import Service, ServiceDuration, PackageOption
from api.appointments.serializers import (
    AppointmentSerializer,
    UserPackageSerializer,
    SharedPackageSerializer,
    UnlimitedPackageSerializer,
    AddMinutesToAppointmentSerializer,
    AppointmentAdditionalServiceSerializer,
    AppointmentAdditionalService,
    SharedPackageUserSerializer,
    PackageUsageSerializer,
    DiscountSerializer,
    AdminRewardSerializer,
    RewardBalanceSerializer,
)


from api.core.permissions import IsOwnerOrReceptionist
from api.staff.models import TherapistProfile, WorkingHour
from api.authentication.serializers import UserProfileSerializer
import traceback
from decimal import Decimal
from rest_framework.exceptions import NotFound, APIException, PermissionDenied
from api.staff.serializers import (
    TherapistProfileOptimizedSerializer,
)
from django.utils import timezone
import csv

User = get_user_model()

from django.template.loader import render_to_string
from django.utils.html import strip_tags
from django.core.mail import send_mail
from django.conf import settings

# Constants
BREAK_TIME = 10  # minutes break between appointments
NEW_APPOINTMENT_BREAK = 10  # minutes
RECEPTIONIST_BOOKING_WINDOW_START = "10:00"  # Default start time for non-working days
RECEPTIONIST_BOOKING_WINDOW_END = "22:00"  # Default end time for non-working days
from utils.logging import api_logger, log_request_data, log_response_data, log_error


class AvailableTimesForPackagesView(generics.GenericAPIView):
    permission_classes = [AllowAny]  # Keep AllowAny for public access

    def get(self, request, *args, **kwargs):
        log_request_data(request, "📥 Available Times Request")

        try:
            therapist_id = request.query_params.get("therapist_id")
            date_str = request.query_params.get("date")
            requested_duration_str = request.query_params.get("total_duration")

            # Check if user is authenticated and is staff
            is_staff = False
            if request.user and request.user.is_authenticated:
                is_staff = hasattr(request.user, "role") and request.user.role in [
                    "receptionist",
                    "owner",
                    "admin",
                ]

            api_logger.info(
                f"🔍 Checking available times for Therapist:{therapist_id}, Date:{date_str}, Staff:{is_staff}"
            )

            if requested_duration_str is None:
                requested_duration = 60
                api_logger.info(f"Using default duration of 60 minutes")
            else:
                requested_duration = int(requested_duration_str)
                api_logger.info(f"Requested duration: {requested_duration} minutes")

            if not therapist_id or not date_str:
                api_logger.warning(
                    f"❌ Missing required parameters: therapist_id={therapist_id}, date={date_str}"
                )
                response = Response(
                    {"error": "Therapist ID and date are required"}, status=400
                )
                log_response_data(response, "📤 Available Times Error Response")
                return response

            parsed_date = parse_date(date_str)
            if not parsed_date:
                api_logger.warning(f"❌ Invalid date format: {date_str}")
                response = Response(
                    {"error": "Invalid date format. Use YYYY-MM-DD"}, status=400
                )
                log_response_data(response, "📤 Available Times Error Response")
                return response

            try:
                api_logger.info(f"🔍 Looking up therapist with ID:{therapist_id}")
                therapist = TherapistProfile.objects.get(id=therapist_id)
            except TherapistProfile.DoesNotExist:
                api_logger.warning(f"❌ Therapist not found: {therapist_id}")
                response = Response({"error": "Therapist not found"}, status=404)
                log_response_data(response, "📤 Available Times Error Response")
                return response

            day_of_week = parsed_date.weekday()

            # Try to get working hours for the day
            working_hour = None
            try:
                api_logger.info(
                    f"🔍 Checking working hours for Therapist:{therapist_id} on day {day_of_week}"
                )
                working_hour = therapist.working_hours.get(day=day_of_week)
            except WorkingHour.DoesNotExist:
                api_logger.warning(
                    f"❌ Therapist:{therapist_id} not available on day {day_of_week}"
                )
                # Check if the request is from a staff member
                if not is_staff:
                    response = Response(
                        {"error": "Therapist is not available on the selected day"},
                        status=404,
                    )
                    log_response_data(response, "📤 Available Times Error Response")
                    return response
                else:
                    api_logger.info(
                        f"🔧 Staff override for non-working day {day_of_week}"
                    )
                    # Use default working hours for staff members
                    start_time_str = RECEPTIONIST_BOOKING_WINDOW_START
                    end_time_str = RECEPTIONIST_BOOKING_WINDOW_END

                    # Convert string times to datetime.time objects
                    start_time = datetime.strptime(start_time_str, "%H:%M").time()
                    end_time = datetime.strptime(end_time_str, "%H:%M").time()

                    # Create synthetic working hours
                    start_datetime = datetime.combine(parsed_date, start_time)
                    end_datetime = datetime.combine(parsed_date, end_time)

            # If working hours were found, use them
            if working_hour:
                start_datetime = datetime.combine(parsed_date, working_hour.start_time)
                end_datetime = datetime.combine(parsed_date, working_hour.end_time)

            api_logger.info(
                f"🔄 Working hours: {start_datetime.strftime('%H:%M')} - {end_datetime.strftime('%H:%M')}"
            )

            # Get all appointments for the day, ordered by start time.
            api_logger.info(
                f"🔄 Retrieving existing appointments for Therapist:{therapist_id} on {date_str}"
            )
            appointments = list(
                Appointment.objects.filter(
                    therapist=therapist,
                    date=parsed_date,
                    status__in=["booked", "check_in"],
                ).order_by("time")
            )
            api_logger.info(f"Found {len(appointments)} existing appointments")

            # ----------------------------------------------
            # STEP 1. Precompute "chain counts" for the day.
            # ----------------------------------------------
            api_logger.debug(f"🔄 Computing appointment chain counts")
            chain_counts = {}
            for i, appointment in enumerate(appointments):
                appt_start = datetime.combine(parsed_date, appointment.time)
                appt_end = appt_start + timedelta(minutes=appointment.total_duration)
                if i == 0:
                    chain_counts[appointment.id] = 1
                else:
                    prev_app = appointments[i - 1]
                    prev_start = datetime.combine(parsed_date, prev_app.time)
                    prev_end = prev_start + timedelta(minutes=prev_app.total_duration)
                    if appt_start == prev_end + timedelta(minutes=NEW_APPOINTMENT_BREAK):
                        chain_counts[appointment.id] = chain_counts[prev_app.id] + 1
                    else:
                        chain_counts[appointment.id] = 1

            # ----------------------------------------------
            # STEP 2. Build blocked intervals from existing appointments.
            # ----------------------------------------------
            api_logger.debug(f"🔄 Building blocked time intervals")
            blocked_intervals = []
            for appointment in appointments:
                appt_start = datetime.combine(parsed_date, appointment.time)
                appt_end = appt_start + timedelta(minutes=appointment.total_duration)
                break_time = NEW_APPOINTMENT_BREAK
                if chain_counts.get(appointment.id, 1) == 3:
                    break_time = 20
                blocked_intervals.append(
                    (appt_start, appt_end + timedelta(minutes=break_time))
                )

            # ----------------------------------------------
            # STEP 3. Iterate over candidate start times and calculate available minutes.
            # ----------------------------------------------
            api_logger.debug(f"🔄 Calculating available time slots")
            available_slots = {}
            current_time = start_datetime
            increment = timedelta(minutes=10)
            while current_time + timedelta(minutes=requested_duration) <= end_datetime:
                candidate_end = current_time + timedelta(minutes=requested_duration)
                if any(
                    self.overlaps(current_time, candidate_end, interval[0], interval[1])
                    for interval in blocked_intervals
                ):
                    current_time += increment
                    continue

                next_unavailable = end_datetime
                for interval in blocked_intervals:
                    if interval[0] > current_time and interval[0] < next_unavailable:
                        next_unavailable = interval[0]
                gap = int((next_unavailable - current_time).total_seconds() // 60)

                required_break = NEW_APPOINTMENT_BREAK  # default
                for appointment in appointments:
                    appt_start = datetime.combine(parsed_date, appointment.time)
                    appt_end = appt_start + timedelta(minutes=appointment.total_duration)
                    if current_time == appt_end + timedelta(
                        minutes=NEW_APPOINTMENT_BREAK
                    ):
                        if chain_counts.get(appointment.id, 1) == 2:
                            required_break = 20
                        break

                available_minutes = gap - required_break
                if available_minutes < 0:
                    available_minutes = 0

                # Only add the time slot if there is a positive number of minutes available.
                if available_minutes > 0:
                    available_slots[current_time.strftime("%H:%M")] = available_minutes
                current_time += increment

            api_logger.info(
                f"✅ Found {len(available_slots)} available time slots for Therapist:{therapist_id}"
            )
            response = Response(available_slots, status=200)
            log_response_data(response, "📤 Available Times Response")
            return response

        except Exception as e:
            log_error(e, "Processing available times request", log_full_trace=True)
            response = Response({"error": "Internal server error"}, status=500)
            log_response_data(response, "📤 Available Times Error Response")
            return response

    def overlaps(self, start1, end1, start2, end2):
        return start1 < end2 and start2 < end1


class AvailableTimesForServicesView(generics.GenericAPIView):
    """
    Returns available start times for booking individual services,
    ensuring that the new appointment's duration plus a break do not
    overlap with any already booked appointments.

    Additional rules:
      - A new appointment of duration D requires D+10 minutes blocked.
      - Booked appointments block from their start time until their end plus
        a dynamic break (normally 10 min; if there are 3 consecutive sessions,
        the break for the 3rd session is 20 minutes, then resets after the 4th).
      - All available start times are offered in 10-minute steps.
    """

    permission_classes = [AllowAny]  # Keep AllowAny for public access

    def get(self, request, *args, **kwargs):
        log_request_data(request, "📥 Available Service Times Request")

        therapist_id = request.query_params.get("therapist_id")
        date_str = request.query_params.get("date")

        # Check if user is authenticated and is staff
        is_staff = False
        if request.user and request.user.is_authenticated:
            is_staff = hasattr(request.user, "role") and request.user.role in [
                "receptionist",
                "owner",
                "admin",
            ]

        api_logger.info(
            f"🔍 Checking available service times for Therapist:{therapist_id}, Date:{date_str}, Staff:{is_staff}"
        )

        try:
            total_duration = int(request.query_params.get("total_duration"))
            api_logger.info(f"Service duration: {total_duration} minutes")
        except (ValueError, TypeError):
            api_logger.warning(
                f"❌ Invalid duration parameter: {request.query_params.get('total_duration')}"
            )
            response = Response(
                {"error": "Invalid total_duration. Must be a positive integer"},
                status=400,
            )
            log_response_data(response, "📤 Error Response")
            return response

        if not therapist_id or not date_str:
            api_logger.warning(
                f"❌ Missing required parameters: therapist_id={therapist_id}, date={date_str}"
            )
            response = Response(
                {"error": "Therapist ID, date, and total_duration are required"},
                status=400,
            )
            log_response_data(response, "📤 Error Response")
            return response

        try:
            api_logger.info(f"🔍 Looking up therapist with ID:{therapist_id}")
            therapist = TherapistProfile.objects.get(id=therapist_id)
        except TherapistProfile.DoesNotExist:
            api_logger.warning(f"❌ Therapist not found: {therapist_id}")
            response = Response({"error": "Therapist not found"}, status=404)
            log_response_data(response, "📤 Error Response")
            return response

        parsed_date = parse_date(date_str)
        if not parsed_date:
            api_logger.warning(f"❌ Invalid date format: {date_str}")
            response = Response(
                {"error": "Invalid date format. Use YYYY-MM-DD"}, status=400
            )
            log_response_data(response, "📤 Error Response")
            return response

        day_of_week = parsed_date.weekday()

        # Try to get working hours for the day
        working_hour = None
        try:
            api_logger.info(
                f"🔍 Checking working hours for Therapist:{therapist_id} on day {day_of_week}"
            )
            working_hour = therapist.working_hours.get(day=day_of_week)
        except WorkingHour.DoesNotExist:
            api_logger.warning(
                f"❌ Therapist:{therapist_id} not available on day {day_of_week}"
            )
            # Check if the request is from a staff member
            if not is_staff:
                response = Response(
                    {"error": "Therapist is not available on the selected day"},
                    status=404,
                )
                log_response_data(response, "📤 Error Response")
                return response
            else:
                api_logger.info(f"🔧 Staff override for non-working day {day_of_week}")
                # Use default working hours for staff members
                start_time_str = RECEPTIONIST_BOOKING_WINDOW_START
                end_time_str = RECEPTIONIST_BOOKING_WINDOW_END

                # Convert string times to datetime.time objects
                start_time = datetime.strptime(start_time_str, "%H:%M").time()
                end_time = datetime.strptime(end_time_str, "%H:%M").time()

                # Create synthetic working hours
                start_datetime = datetime.combine(parsed_date, start_time)
                end_datetime = datetime.combine(parsed_date, end_time)

        # If working hours were found, use them
        if working_hour:
            start_datetime = datetime.combine(parsed_date, working_hour.start_time)
            end_datetime = datetime.combine(parsed_date, working_hour.end_time)

        api_logger.info(
            f"🔄 Working hours: {start_datetime.strftime('%H:%M')} - {end_datetime.strftime('%H:%M')}"
        )

        # Get all appointments for the day and compute their "blocked intervals"
        api_logger.info(
            f"🔄 Retrieving existing appointments for Therapist:{therapist_id} on {date_str}"
        )
        appointments = Appointment.objects.filter(
            therapist=therapist, date=parsed_date, status__in=["booked", "check_in"]
        ).order_by("time")
        api_logger.info(f"Found {appointments.count()} existing appointments")

        api_logger.debug(f"🔄 Computing blocked time intervals")
        blocked_intervals = self.get_blocked_intervals(appointments, parsed_date)
        api_logger.debug(f"Created {len(blocked_intervals)} blocked intervals")

        api_logger.debug(f"🔄 Calculating available time slots")
        available_times = []
        current_time = start_datetime
        increment = timedelta(minutes=10)
        # For a new appointment, define its "blocked interval" as [start, start + total_duration + NEW_APPOINTMENT_BREAK)
        while (
            current_time + timedelta(minutes=total_duration + NEW_APPOINTMENT_BREAK)
            <= end_datetime
        ):
            new_blocked_end = current_time + timedelta(
                minutes=total_duration + NEW_APPOINTMENT_BREAK
            )
            # If the new appointment's block does not overlap any blocked interval, then current_time is available.
            if not any(
                self.overlaps(current_time, new_blocked_end, interval[0], interval[1])
                for interval in blocked_intervals
            ):
                available_times.append(current_time.strftime("%H:%M"))
            current_time += increment

        api_logger.info(
            f"✅ Found {len(available_times)} available time slots for Therapist:{therapist_id}"
        )
        response = Response(available_times, status=200)
        log_response_data(response, "📤 Available Service Times Response")
        return response

    def overlaps(self, start1, end1, start2, end2):
        """
        Checks whether the half-open intervals [start1, end1) and [start2, end2) overlap.
        """
        return start1 < end2 and start2 < end1

    def get_blocked_intervals(self, appointments, date):
        """
        Compute the list of blocked intervals for the day based on booked appointments.
        Each booked appointment is assumed to block from its start time until its end time
        plus a break. The break is normally 10 minutes but if appointments are back-to-back,
        then if this is the 3rd consecutive session, the break is 20 minutes. After the 4th,
        the consecutive counter resets.
        """
        api_logger.debug(
            f"🔄 Calculating blocked intervals for {len(appointments)} appointments"
        )
        appointments_sorted = sorted(appointments, key=lambda a: a.time)
        blocked_intervals = []
        prev_end = None
        consecutive_count = 0
        for appointment in appointments_sorted:
            appt_start = datetime.combine(date, appointment.time)
            appt_duration = appointment.total_duration
            # Determine if this appointment is exactly consecutive with the previous one.
            if prev_end and appt_start == prev_end:
                consecutive_count += 1
                api_logger.debug(
                    f"Appointment is consecutive, count: {consecutive_count}"
                )
            else:
                consecutive_count = 1
            # Apply dynamic break: if 3 consecutive sessions, break = 20 minutes; if 4th, reset to 10.
            if consecutive_count == 3:
                break_minutes = 20
                api_logger.debug(
                    f"Using extended break (20 min) for 3rd consecutive appointment"
                )
            elif consecutive_count >= 4:
                consecutive_count = 1
                break_minutes = 10
                api_logger.debug(f"Resetting consecutive count after 4+ appointments")
            else:
                break_minutes = 10
            blocked_end = appt_start + timedelta(minutes=appt_duration + break_minutes)
            blocked_intervals.append((appt_start, blocked_end))
            api_logger.debug(
                f"Added blocked interval: {appt_start.strftime('%H:%M')} - {blocked_end.strftime('%H:%M')}"
            )
            prev_end = blocked_end
        return blocked_intervals
