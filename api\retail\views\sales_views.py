from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q, Sum
from django.db import transaction
from django.utils import timezone
from django.http import HttpResponse
import csv
import logging
from datetime import datetime

from api.retail.models import Product, ProductSale, ProductSaleItem, CashRegister
from api.retail.serializers import ProductSaleSerializer
from api.core.permissions import IsOwnerOrReceptionist

# Import the logging utilities
from utils.logging import api_logger, log_request_data, log_response_data, log_error

logger = logging.getLogger(__name__)


class ProductSaleViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing product sales.
    Staff users can create and manage sales.
    """

    queryset = ProductSale.objects.all().order_by("-created_at")
    serializer_class = ProductSaleSerializer
    permission_classes = [IsAuthenticated, IsOwnerOrReceptionist]
    # Add filter backend
    filter_backends = [DjangoFilterBackend]
    # Define basic filterable fields
    filterset_fields = ["location", "status", "payment_method"]

    def get_queryset(self):
        """
        Override get_queryset to apply custom filtering, especially for date ranges.
        """
        queryset = super().get_queryset()

        # Get query parameters
        start_date = self.request.query_params.get("start_date")
        end_date = self.request.query_params.get("end_date")
        location = self.request.query_params.get("location")
        status = self.request.query_params.get("status")
        payment_method = self.request.query_params.get("payment_method")
        user_id = self.request.query_params.get("user_id")

        # Log the filtering parameters
        if hasattr(self, "request"):
            api_logger.info(
                f"Filtering sales with params: start_date={start_date}, end_date={end_date}, "
                f"location={location}, status={status}, payment_method={payment_method}, user_id={user_id}"
            )

        # Apply date filters if provided
        if start_date:
            api_logger.info(f"Filtering by start date: {start_date}")
            queryset = queryset.filter(created_at__date__gte=start_date)
        if end_date:
            api_logger.info(f"Filtering by end date: {end_date}")
            queryset = queryset.filter(created_at__date__lte=end_date)

        # These filters could be handled by DjangoFilterBackend, but we're adding them explicitly
        # for better logging and to ensure they work correctly
        if location:
            api_logger.info(f"Filtering by location: {location}")
            queryset = queryset.filter(location=location)
        if status:
            api_logger.info(f"Filtering by status: {status}")
            queryset = queryset.filter(status=status)
        if payment_method:
            api_logger.info(f"Filtering by payment method: {payment_method}")
            queryset = queryset.filter(payment_method=payment_method)
        if user_id:
            api_logger.info(f"Filtering by user ID: {user_id}")
            queryset = queryset.filter(customer_id=user_id)

        if hasattr(self, "request"):
            api_logger.info(f"Filtered queryset count: {queryset.count()}")

        return queryset

    def list(self, request, *args, **kwargs):
        """Override list method to log query parameters and results"""
        api_logger.info(f"📥 GET request for product sales list")
        log_request_data(request, "Product Sales List Request")

        try:
            # Log query parameters
            api_logger.info(f"Query params: {request.query_params}")

            # Log the queryset before filtering
            api_logger.info(f"Base queryset count: {self.get_queryset().count()}")

            # Get the filtered queryset
            queryset = self.filter_queryset(self.get_queryset())
            api_logger.info(f"Filtered queryset count: {queryset.count()}")

            # Log permission checks
            api_logger.info(f"Checking permissions for user: {request.user.email}")

            # Continue with normal processing
            page = self.paginate_queryset(queryset)
            if page is not None:
                serializer = self.get_serializer(page, many=True)
                api_logger.info(
                    f"✅ Returning paginated response with {len(serializer.data)} items"
                )
                response = self.get_paginated_response(serializer.data)
                log_response_data(response, "Product Sales List Response")
                return response

            serializer = self.get_serializer(queryset, many=True)
            api_logger.info(
                f"✅ Returning full response with {len(serializer.data)} items"
            )
            response = Response(serializer.data)
            log_response_data(response, "Product Sales List Response")
            return response

        except Exception as e:
            log_error(e, "Error retrieving product sales list", log_full_trace=True)
            return Response(
                {"detail": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def retrieve(self, request, *args, **kwargs):
        """Override retrieve method to add logging"""
        api_logger.info(f"📥 GET request for product sale detail: {kwargs.get('pk')}")
        log_request_data(request, "Product Sale Detail Request")

        try:
            instance = self.get_object()
            api_logger.info(
                f"Retrieved sale with ID: {instance.id}, invoice: {instance.invoice_number}"
            )

            serializer = self.get_serializer(instance)
            api_logger.info(
                f"✅ Successfully retrieved sale with invoice: {instance.invoice_number}"
            )

            response = Response(serializer.data)
            log_response_data(response, "Product Sale Detail Response")
            return response

        except Exception as e:
            log_error(
                e,
                f"Error retrieving product sale {kwargs.get('pk')}",
                log_full_trace=True,
            )
            return Response(
                {"detail": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def create(self, request, *args, **kwargs):
        """Custom create method to properly handle sale items"""
        api_logger.info("📥 POST request to create product sale")
        log_request_data(request, "Create Sale Request")

        try:
            # Log the incoming data for debugging
            api_logger.info(f"Received sale data: {request.data}")

            with transaction.atomic():
                # Create the sale first
                serializer = self.get_serializer(data=request.data)
                serializer.is_valid(raise_exception=True)
                api_logger.info("Sale data validated successfully")

                sale = self.perform_create(serializer)
                api_logger.info(f"Sale created with ID: {sale.id}")

                # Now that all items are created, generate the invoice
                sale.generate_invoice()
                api_logger.info(f"Invoice generated: {sale.invoice_number}")

                # Refresh serializer data to include the newly generated invoice
                serializer = self.get_serializer(sale)

            api_logger.info(
                f"✅ Sale created successfully with invoice: {sale.invoice_number}"
            )
            headers = self.get_success_headers(serializer.data)
            response = Response(
                serializer.data, status=status.HTTP_201_CREATED, headers=headers
            )
            log_response_data(response, "Create Sale Response")
            return response

        except Exception as e:
            log_error(e, "Error creating sale", log_full_trace=True)
            return Response({"detail": str(e)}, status=status.HTTP_400_BAD_REQUEST)

    def perform_create(self, serializer):
        """
        Set the staff to the current user and update cash register for cash sales.
        """
        # Create the sale
        sale = serializer.save(staff=self.request.user)
        api_logger.info(f"Sale saved with staff: {self.request.user.email}")

        # If this is a cash sale, update the cash register
        if sale.payment_method == "CASH" and sale.status == "COMPLETED":
            # Find today's cash register for this location
            today = timezone.now().date()
            try:
                cash_register = CashRegister.objects.get(
                    date=today, location=sale.location
                )

                # Add the cash sale amount to the register
                cash_register.current_balance += sale.total_amount
                cash_register.save()
                api_logger.info(
                    f"💰 Cash register {cash_register.id} updated: +{sale.total_amount} from sale {sale.invoice_number}"
                )
            except CashRegister.DoesNotExist:
                # Log warning if no cash register is found
                api_logger.warning(
                    f"❌ Cash register not found for location {sale.location} on {today} "
                    f"while processing cash sale {sale.invoice_number}"
                )

        # Return the sale object
        return sale

    @action(detail=True, methods=["post"])
    def refund(self, request, pk=None):
        """Process a refund for a sale."""
        api_logger.info(f"📥 POST request to refund sale with ID: {pk}")
        log_request_data(request, "Refund Sale Request")

        try:
            sale = self.get_object()
            api_logger.info(
                f"Retrieved sale with invoice: {sale.invoice_number}, status: {sale.status}"
            )

            if sale.status != "COMPLETED":
                api_logger.warning(
                    f"❌ Cannot refund sale {sale.invoice_number} with status {sale.status}"
                )
                return Response(
                    {"detail": f"Cannot refund a sale with status {sale.status}"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            with transaction.atomic():
                # Update product inventory
                for item in sale.sale_items.all():
                    product = item.product
                    product.quantity_in_stock += item.quantity
                    product.save()
                    api_logger.info(
                        f"Updated inventory for product {product.id}: +{item.quantity}"
                    )

                # Update sale status
                sale.status = "REFUNDED"
                sale.save()
                api_logger.info(f"Updated sale status to REFUNDED")

                # If it was a cash sale, we should handle the cash register
                if sale.payment_method == "CASH":
                    # Find today's cash register for this location
                    today = timezone.now().date()
                    try:
                        cash_register = CashRegister.objects.get(
                            date=today, location=sale.location
                        )

                        # If refunding cash, subtract from register
                        cash_register.current_balance -= sale.total_amount
                        cash_register.save()
                        api_logger.info(
                            f"💰 Cash register {cash_register.id} updated: -{sale.total_amount} for refund of {sale.invoice_number}"
                        )
                    except CashRegister.DoesNotExist:
                        # Just log this - we won't block the refund if register not found
                        api_logger.warning(
                            f"❌ Cash register not found for location {sale.location} on {today} "
                            f"while processing refund for sale {sale.invoice_number}"
                        )

            api_logger.info(
                f"✅ Sale {sale.invoice_number} has been refunded successfully"
            )
            response = Response(
                {
                    "detail": f"Sale {sale.invoice_number} has been refunded",
                    "sale": ProductSaleSerializer(sale).data,
                }
            )
            log_response_data(response, "Refund Sale Response")
            return response
        except Exception as e:
            log_error(e, f"Error processing refund for sale {pk}", log_full_trace=True)
            return Response(
                {"detail": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=["get"])
    def export_csv(self, request):
        """Export sales to a CSV file with filtering options."""
        api_logger.info("📥 GET request for CSV export of sales")
        log_request_data(request, "Export Sales CSV Request")

        try:
            # Log query parameters for debugging filter issues
            api_logger.info(f"CSV export query params: {request.query_params}")

            # Log the base queryset count
            base_queryset = self.get_queryset()
            api_logger.info(
                f"Base queryset count before filtering: {base_queryset.count()}"
            )

            # Get filtered queryset
            queryset = self.filter_queryset(base_queryset)
            api_logger.info(f"Filtered queryset count for CSV: {queryset.count()}")

            response = HttpResponse(content_type="text/csv")
            filename = f"sales_export_{timezone.now().strftime('%Y%m%d_%H%M%S')}.csv"
            response["Content-Disposition"] = f'attachment; filename="{filename}"'

            writer = csv.writer(response)
            writer.writerow(
                [
                    "Invoice Number",
                    "Date",
                    "Time",
                    "Customer",
                    "Payment Method",
                    "Status",
                    "Location",
                    "Subtotal",
                    "VAT",
                    "Total",
                    "Items",
                ]
            )

            item_count = 0
            for sale in queryset:
                # Format the items as a string
                items_str = "; ".join(
                    [
                        f"{item.product.name} x{item.quantity}"
                        for item in sale.sale_items.all()
                    ]
                )

                # Format customer name
                customer_name = ""
                if sale.customer:
                    customer_name = (
                        f"{sale.customer.first_name} {sale.customer.last_name}"
                    )

                writer.writerow(
                    [
                        sale.invoice_number,
                        sale.created_at.strftime("%Y-%m-%d"),
                        sale.created_at.strftime("%H:%M:%S"),
                        customer_name,
                        sale.get_payment_method_display(),
                        sale.get_status_display(),
                        sale.get_location_display(),
                        sale.subtotal_amount,
                        sale.vat_amount,
                        sale.total_amount,
                        items_str,
                    ]
                )
                item_count += 1

            api_logger.info(f"✅ CSV export completed with {item_count} sales records")
            return response
        except Exception as e:
            log_error(e, "Error exporting sales to CSV", log_full_trace=True)
            return Response(
                {"detail": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=["get"])
    def daily_summary(self, request):
        """Get a summary of today's sales."""
        api_logger.info("📥 GET request for daily sales summary")
        log_request_data(request, "Daily Sales Summary Request")

        try:
            location = request.query_params.get("location")
            api_logger.info(f"Daily summary requested for location: {location}")

            if not location:
                api_logger.warning("❌ Location parameter missing for daily summary")
                return Response(
                    {"detail": "Location parameter is required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            today = timezone.now().date()

            # Filter sales for today and the specified location
            sales = ProductSale.objects.filter(
                created_at__date=today, location=location, status="COMPLETED"
            )
            api_logger.info(
                f"Found {sales.count()} completed sales for today at location {location}"
            )

            # Calculate summary stats
            total_sales = sales.count()
            total_revenue = sales.aggregate(total=Sum("total_amount"))["total"] or 0
            api_logger.info(f"Total sales: {total_sales}, Total revenue: {total_revenue}")

            # Breakdown by payment method
            payment_breakdown = (
                sales.values("payment_method")
                .annotate(count=Sum("id"), total=Sum("total_amount"))
                .order_by("payment_method")
            )
            api_logger.info(f"Payment breakdown: {list(payment_breakdown)}")

            # Format payment methods for display
            payment_methods = {
                "CASH": "Cash",
                "CARD": "Card",
                "ONLINE_LINK": "Online Link",
            }
            for item in payment_breakdown:
                method = item["payment_method"]
                item["payment_method_display"] = payment_methods.get(method, method)

            # Top selling products
            top_products = (
                ProductSaleItem.objects.filter(product_sale__in=sales)
                .values("product__name")
                .annotate(quantity=Sum("quantity"), revenue=Sum("total_price"))
                .order_by("-quantity")[:5]
            )
            api_logger.info(f"Top products: {list(top_products)}")

            response_data = {
                "date": today.isoformat(),
                "location": location,
                "location_display": dict(ProductSale.LOCATION_CHOICES).get(
                    location, location
                ),
                "total_sales": total_sales,
                "total_revenue": total_revenue,
                "payment_breakdown": payment_breakdown,
                "top_products": top_products,
            }

            api_logger.info(f"✅ Daily summary generated successfully for {location}")
            response = Response(response_data)
            log_response_data(response, "Daily Sales Summary Response")
            return response
        except Exception as e:
            log_error(e, "Error generating daily sales summary", log_full_trace=True)
            return Response(
                {"detail": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
