# helpers/monthly_overview.py
from decimal import Decimal
from datetime import datetime, timedelta
from django.db.models import Sum
from django.utils import timezone

from api.retail.models import DailySalesReport


def get_monthly_overview(month, year, location):
    """Get an overview of sales for a specific month."""

    # Create date range for the month
    if month == 12:
        next_month_year = year + 1
        next_month = 1
    else:
        next_month_year = year
        next_month = month + 1

    start_date = datetime(year, month, 1).date()
    end_date = datetime(next_month_year, next_month, 1).date() - timedelta(days=1)
    today = timezone.now().date()

    # Get all reports for this month and location
    reports = DailySalesReport.objects.filter(
        date__range=[start_date, end_date], location=location
    ).order_by("date")

    # Aggregate data from reports
    totals = reports.aggregate(
        gross_sales=Sum("gross_sales_amount"),
        service_sales=Sum("service_sales_amount"),
        product_sales=Sum("product_sales_amount"),
        package_sales=Sum("package_sales_amount"),
        cash_sales=Sum("cash_sales_amount"),
        card_sales=Sum("card_sales_amount"),
        online_sales=Sum("online_link_amount"),
        vat=Sum("vat_amount"),
        card_charges=Sum("card_charges_amount"),
        link_charges=Sum("link_charges_amount"),
        net_sales=Sum("net_sales_amount"),
        expenses=Sum("expenses_total"),
        withdrawals=Sum("cash_withdrawals_total"),
    )

    # Initialize with zeros for any None values
    for key in totals:
        if totals[key] is None:
            totals[key] = Decimal("0.00")

    # Get day-by-day data for charts
    daily_data = (
        reports.values("date")
        .annotate(
            gross_sales=Sum("gross_sales_amount"),
            service_sales=Sum("service_sales_amount"),
            product_sales=Sum("product_sales_amount"),
            package_sales=Sum("package_sales_amount"),
            net_sales=Sum("net_sales_amount"),
        )
        .order_by("date")
    )

    # Format daily data with complete date information including zeros for missing days
    complete_daily_data = []
    current_date = start_date
    while current_date <= min(end_date, today):
        day_data = next(
            (item for item in daily_data if item["date"] == current_date), None
        )

        if day_data:
            complete_daily_data.append(day_data)
        else:
            complete_daily_data.append(
                {
                    "date": current_date,
                    "gross_sales": Decimal("0.00"),
                    "service_sales": Decimal("0.00"),
                    "product_sales": Decimal("0.00"),
                    "package_sales": Decimal("0.00"),
                    "net_sales": Decimal("0.00"),
                }
            )

        current_date += timedelta(days=1)

    # Count of days with reports
    days_with_data = reports.count()

    # Calculate averages
    averages = {}
    if days_with_data > 0:
        for key, value in totals.items():
            averages[key] = value / days_with_data

    # Calculate payment method distribution
    payment_distribution = []
    total_payments = totals["cash_sales"] + totals["card_sales"] + totals["online_sales"]

    if total_payments > 0:
        payment_distribution = [
            {
                "method": "Cash",
                "amount": totals["cash_sales"],
                "percentage": (totals["cash_sales"] / total_payments * 100),
            },
            {
                "method": "Card",
                "amount": totals["card_sales"],
                "percentage": (totals["card_sales"] / total_payments * 100),
            },
            {
                "method": "Online",
                "amount": totals["online_sales"],
                "percentage": (totals["online_sales"] / total_payments * 100),
            },
        ]

    # Calculate sales type distribution
    sales_distribution = []
    total_sales = (
        totals["service_sales"] + totals["product_sales"] + totals["package_sales"]
    )

    if total_sales > 0:
        sales_distribution = [
            {
                "type": "Services",
                "amount": totals["service_sales"],
                "percentage": (totals["service_sales"] / total_sales * 100),
            },
            {
                "type": "Products",
                "amount": totals["product_sales"],
                "percentage": (totals["product_sales"] / total_sales * 100),
            },
            {
                "type": "Packages",
                "amount": totals["package_sales"],
                "percentage": (totals["package_sales"] / total_sales * 100),
            },
        ]

    # Calculate growth compared to previous month
    if month == 1:
        prev_month = 12
        prev_year = year - 1
    else:
        prev_month = month - 1
        prev_year = year

    prev_month_start = datetime(prev_year, prev_month, 1).date()
    if prev_month == 12:
        prev_month_end = datetime(year, 1, 1).date() - timedelta(days=1)
    else:
        prev_month_end = datetime(prev_year, prev_month + 1, 1).date() - timedelta(days=1)

    # Get previous month's totals
    prev_month_reports = DailySalesReport.objects.filter(
        date__range=[prev_month_start, prev_month_end], location=location
    )

    prev_month_totals = prev_month_reports.aggregate(
        gross_sales=Sum("gross_sales_amount"),
        service_sales=Sum("service_sales_amount"),
        product_sales=Sum("product_sales_amount"),
        package_sales=Sum("package_sales_amount"),
        net_sales=Sum("net_sales_amount"),
    )

    # Initialize with zeros for any None values
    for key in prev_month_totals:
        if prev_month_totals[key] is None:
            prev_month_totals[key] = Decimal("0.00")

    # Calculate growth percentages
    growth = {}
    for key in [
        "gross_sales",
        "service_sales",
        "product_sales",
        "package_sales",
        "net_sales",
    ]:
        if prev_month_totals[key] > 0:
            growth[key] = (
                (totals[key] - prev_month_totals[key]) / prev_month_totals[key] * 100
            )
        else:
            growth[key] = 100 if totals[key] > 0 else 0

    return {
        "month": month,
        "year": year,
        "month_name": start_date.strftime("%B"),
        "date_range": {
            "start": start_date.isoformat(),
            "end": min(end_date, today).isoformat(),
        },
        "location": location,
        "location_display": dict(DailySalesReport.LOCATION_CHOICES).get(
            location, location
        ),
        "days_with_data": days_with_data,
        "days_in_month": (end_date - start_date).days + 1,
        "days_to_date": min(
            (today - start_date).days + 1, (end_date - start_date).days + 1
        ),
        "totals": totals,
        "averages": averages,
        "daily_data": complete_daily_data,
        "payment_distribution": payment_distribution,
        "sales_distribution": sales_distribution,
        "growth": growth,
    }
