from django.db.models.signals import post_save, post_delete, pre_delete
from django.dispatch import receiver
from .models import Appointment, Reward, RewardBalance, Sale
from django.db import transaction

from api.retail.models import (
    ProductSale,
    CashRegister,
    CashWithdrawal,
    CashDeposit,
    Expense,
    DailySalesReport,
)

# Updated imports for better logging
from utils.logging import logger, api_logger, log_error
from .models import UserPackage, SharedPackage, UnlimitedPackage, Sale

from api.utils.push_notifications import send_appointment_confirmation_notification


@receiver(pre_delete, sender=UserPackage)
def delete_sale_on_user_package_delete(sender, instance, **kwargs):
    """
    Delete associated Sale records when a UserPackage is deleted.
    """
    Sale.objects.filter(user_package=instance).delete()


@receiver(pre_delete, sender=SharedPackage)
def delete_sale_on_shared_package_delete(sender, instance, **kwargs):
    """
    Delete associated Sale records when a SharedPackage is deleted.
    """
    Sale.objects.filter(shared_package=instance).delete()


@receiver(pre_delete, sender=UnlimitedPackage)
def delete_sale_on_unlimited_package_delete(sender, instance, **kwargs):
    """
    Delete associated Sale records when an UnlimitedPackage is deleted.
    """
    Sale.objects.filter(unlimited_package=instance).delete()


@receiver(post_delete, sender=Appointment)
def delete_unused_packages_on_appointment_delete(sender, instance, **kwargs):
    """
    When an appointment is deleted, check if its associated package:
    1. Has 100% of its minutes remaining
    2. Has no Sale objects bound to it
    If both conditions are met, delete the package directly.
    """
    logger.info(
        f"🔄 Checking for unused packages after deletion of Appointment:{instance.id}"
    )

    try:
        # Check for UserPackage
        if hasattr(instance, "user_package") and instance.user_package:
            user_package = instance.user_package

            # Check if package has 100% of its minutes
            has_full_minutes = user_package.remaining_time == user_package.total_time

            # Check if there are no sales associated with this package
            has_no_sales = not Sale.objects.filter(user_package=user_package).exists()

            if has_full_minutes and has_no_sales:
                package_id = user_package.id
                user_package.delete()
                logger.info(
                    f"✅ Deleted UserPackage:{package_id} with 100% remaining minutes and no sales"
                )

        # Check for SharedPackage
        elif hasattr(instance, "shared_package") and instance.shared_package:
            shared_package = instance.shared_package

            # Check if package has 100% of its minutes
            has_full_minutes = shared_package.remaining_time == shared_package.total_time

            # Check if there are no sales associated with this package
            has_no_sales = not Sale.objects.filter(shared_package=shared_package).exists()

            if has_full_minutes and has_no_sales:
                package_id = shared_package.id
                shared_package.delete()
                logger.info(
                    f"✅ Deleted SharedPackage:{package_id} with 100% remaining minutes and no sales"
                )

        # Check for UnlimitedPackage
        elif hasattr(instance, "unlimited_package") and instance.unlimited_package:
            unlimited_package = instance.unlimited_package

            # For unlimited packages, check if it's unused (no usage count)
            # Adjust this condition if your UnlimitedPackage has a different field for usage
            has_no_usage = getattr(unlimited_package, "usage_count", 0) == 0

            # Check if there are no sales associated with this package
            has_no_sales = not Sale.objects.filter(
                unlimited_package=unlimited_package
            ).exists()

            if has_no_usage and has_no_sales:
                package_id = unlimited_package.id
                unlimited_package.delete()
                logger.info(
                    f"✅ Deleted UnlimitedPackage:{package_id} with no usage and no sales"
                )

        else:
            logger.info(
                f"📝 No package found for Appointment:{instance.id}, no deletion needed"
            )

    except Exception as e:
        log_error(
            e, f"Checking for unused packages after deletion of Appointment:{instance.id}"
        )


@receiver(post_save, sender=Appointment)
def send_appointment_notification(sender, instance, created, **kwargs):
    """
    Send push notification to customer when appointment is created.
    """
    if created:
        logger.info(
            f"🔄 Sending appointment confirmation notification for Appointment:{instance.id}"
        )
        try:
            # Send push notification to customer
            send_appointment_confirmation_notification(instance.customer, instance)

            logger.info(
                f"✅ Sent appointment confirmation notification to {instance.customer.email}"
            )

        except Exception as e:
            log_error(
                e, f"Sending appointment notification for Appointment:{instance.id}"
            )


# @receiver(post_save, sender=Appointment)
# def create_commission_on_appointment_creation(sender, instance, created, **kwargs):
#     """
#     When a new appointment is created and has a therapist, calculate and create a Commission.
#     """
#     if created and instance.therapist:
#         logger.info(
#             f"🔄 Creating commission for new Appointment:{instance.id} with Therapist:{instance.therapist.user.id}"
#         )
#         try:
#             # Get the therapist from the appointment (using your foreign key relation)
#             therapist = instance.therapist.user
#             commission_amount = calculate_commission(therapist, instance)

#             if commission_amount > 0:
#                 commission = Commission.objects.create(
#                     therapist=therapist,
#                     appointment=instance,  # Using the new FK relation
#                     date=instance.date,
#                     amount=commission_amount,
#                     description=f"Commission for appointment {instance.id}",
#                 )
#                 logger.info(
#                     f"✅ Created Commission:{commission.id} for Appointment:{instance.id} - Amount:{commission_amount}"
#                 )
#             else:
#                 logger.info(
#                     f"📝 No commission created for Appointment:{instance.id} - Commission amount is zero"
#                 )
#         except Exception as e:
#             log_error(e, f"Creating commission for Appointment:{instance.id}")

#     if (
#         instance.status == "check_in"
#         and instance.payment_method == "cash"
#         and instance.total_price > 0
#     ):
#         logger.info(
#             f"🔄 Processing cash payment for Appointment:{instance.id} - Amount:{instance.total_price}"
#         )
#         try:
#             with transaction.atomic():
#                 # Find or create cash register for this date and location
#                 cash_register, created = CashRegister.objects.get_or_create(
#                     date=instance.date,
#                     location=instance.location,
#                     defaults={
#                         "current_balance": 0,
#                         "notes": "Auto-created for appointment",
#                     },
#                 )

#                 if created:
#                     logger.info(
#                         f"📝 Created new CashRegister for Date:{instance.date}, Location:{instance.location}"
#                     )

#                 # Add total price to cash register balance
#                 previous_balance = cash_register.current_balance
#                 cash_register.current_balance += instance.total_price
#                 cash_register.save()

#                 logger.info(
#                     f"✅ Updated CashRegister:{cash_register.id} for Appointment:{instance.id}: Balance {previous_balance} → {cash_register.current_balance}"
#                 )
#         except Exception as e:
#             log_error(e, f"Updating cash register for Appointment:{instance.id}")


@receiver(post_delete, sender=Appointment)
def delete_commission_on_appointment_delete(sender, instance, **kwargs):
    """
    When an appointment is deleted, ensure any related Commission is also deleted.
    (This is mostly handled by CASCADE, but we include it to be safe.)
    """
    logger.info(f"🔄 Deleting commissions for Appointment:{instance.id}")
    try:
        deleted_count = Commission.objects.filter(appointment=instance).count()
        Commission.objects.filter(appointment=instance).delete()
        logger.info(
            f"✅ Deleted {deleted_count} commission(s) for Appointment:{instance.id}"
        )
    except Exception as e:
        log_error(e, f"Deleting commissions for Appointment:{instance.id}")


@receiver(post_delete, sender=Appointment)
def update_package_on_appointment_delete(sender, instance, **kwargs):
    """
    When an appointment is deleted, add its total_duration back to the associated package.
    This handles both individual UserPackages and SharedPackages.
    """
    logger.info(f"🔄 Updating package after deletion of Appointment:{instance.id}")

    try:
        # Safely get the package using getattr instead of direct attribute access
        user_package = getattr(instance, "user_package", None)
        shared_package = getattr(instance, "shared_package", None)
        unlimited_package = getattr(instance, "unlimited_package", None)

        if user_package:
            previous_time = user_package.remaining_time
            user_package.remaining_time += instance.total_duration
            user_package.time_deducted = max(
                user_package.time_deducted - instance.total_duration, 0
            )
            user_package.active = user_package.remaining_time > 0
            user_package.save()
            logger.info(
                f"✅ Updated UserPackage:{user_package.id} - Remaining time: {previous_time} → {user_package.remaining_time}"
            )

        elif shared_package:
            previous_time = shared_package.remaining_time
            shared_package.remaining_time += instance.total_duration
            shared_package.active = shared_package.remaining_time > 0
            shared_package.save()
            logger.info(
                f"✅ Updated SharedPackage:{shared_package.id} - Remaining time: {previous_time} → {shared_package.remaining_time}"
            )

        elif unlimited_package:
            # Handle unlimited package if needed
            logger.info(
                f"📝 UnlimitedPackage:{unlimited_package.id} found, no time updates needed"
            )
        else:
            logger.info(
                f"📝 No package found for Appointment:{instance.id}, no updates needed"
            )
    except Exception as e:
        log_error(e, f"Updating package after deletion of Appointment:{instance.id}")


@receiver(post_save, sender=Appointment)
def create_reward_on_appointment_check_in(sender, instance, created, **kwargs):
    """
    When an appointment is saved with status 'check_in' and a positive total_price,
    create (or update) a Reward and update the customer's overall RewardBalance.
    Points are calculated as (total_price in dirhams * factor).
    """
    if instance.status == "check_in" and instance.total_price > 0:
        logger.info(
            f"🔄 Processing rewards for Appointment:{instance.id}, Customer:{instance.customer.id}"
        )
        try:
            factor = 10  # 10 points per dirham
            points = int(instance.total_price) * factor

            reward, reward_created = Reward.objects.get_or_create(
                appointment=instance,
                defaults={
                    "customer": instance.customer,
                    "points": points,
                    "factor": factor,
                    "reward_date": instance.date,  # Use appointment date instead of current date
                },
            )

            if not reward_created:
                # If reward already exists, update it and adjust the overall balance by the difference.
                previous_points = reward.points
                difference = points - previous_points
                reward.points = points

                # Update reward_date if it doesn't match the appointment date
                if reward.reward_date != instance.date:
                    reward.reward_date = instance.date

                reward.save()

                if difference != 0:
                    logger.info(
                        f"🔄 Updating Reward:{reward.id} - Points: {previous_points} → {points}"
                    )
                    reward_balance, created = RewardBalance.objects.get_or_create(
                        customer=instance.customer
                    )
                    previous_balance = reward_balance.total_points
                    reward_balance.total_points += difference
                    reward_balance.save()
                    logger.info(
                        f"✅ Updated RewardBalance for Customer:{instance.customer.id} - Points: {previous_balance} → {reward_balance.total_points}"
                    )
                else:
                    logger.info(
                        f"📝 No point change for Reward:{reward.id}, skipping RewardBalance update"
                    )
            else:
                # New reward created; add points to overall balance.
                logger.info(
                    f"📝 Created new Reward:{reward.id} for Appointment:{instance.id} - Points:{points}"
                )
                reward_balance, created = RewardBalance.objects.get_or_create(
                    customer=instance.customer
                )
                previous_balance = reward_balance.total_points
                reward_balance.total_points += points
                reward_balance.save()
                logger.info(
                    f"✅ Updated RewardBalance for Customer:{instance.customer.id} - Points: {previous_balance} → {reward_balance.total_points}"
                )
        except Exception as e:
            log_error(e, f"Processing rewards for Appointment:{instance.id}")


@receiver(post_delete, sender=Appointment)
def delete_reward_on_appointment_delete(sender, instance, **kwargs):
    """
    When an appointment is deleted, remove the associated Reward and subtract its points
    from the customer's RewardBalance.
    """
    logger.info(f"🔄 Removing rewards for deleted Appointment:{instance.id}")
    try:
        if hasattr(instance, "reward"):
            points = instance.reward.points
            reward_id = instance.reward.id
            instance.reward.delete()
            logger.info(f"📝 Deleted Reward:{reward_id} with {points} points")

            reward_balance, created = RewardBalance.objects.get_or_create(
                customer=instance.customer
            )
            previous_balance = reward_balance.total_points
            reward_balance.total_points -= points
            if reward_balance.total_points < 0:
                reward_balance.total_points = 0
            reward_balance.save()
            logger.info(
                f"✅ Updated RewardBalance for Customer:{instance.customer.id} - Points: {previous_balance} → {reward_balance.total_points}"
            )
        else:
            logger.info(
                f"📝 No reward found for Appointment:{instance.id}, no deletion needed"
            )
    except Exception as e:
        log_error(e, f"Removing rewards for Appointment:{instance.id}")


@receiver(post_save, sender=Appointment)
def create_sale_after_checkin(sender, instance, created, **kwargs):
    """
    When an appointment is checked in and has a positive total price,
    create a Sale object to track the transaction.
    """
    if instance.status == "check_in" and instance.total_price > 0:
        logger.info(
            f"🔄 Creating sale record for Appointment:{instance.id} - Amount:{instance.total_price}"
        )

        try:
            # Check if a Sale already exists for this appointment to prevent duplicates
            existing_sale = Sale.objects.filter(appointment=instance).exists()
            if existing_sale:
                logger.info(
                    f"📝 Sale already exists for Appointment:{instance.id}, skipping creation"
                )
                # Call the update function to ensure price changes are reflected
                update_sale_on_appointment_change(sender, instance, created, **kwargs)
                return

            # Create a datetime object from the appointment date and time
            from datetime import datetime, time

            if instance.time:
                sale_datetime = datetime.combine(instance.date, instance.time)
            else:
                # If no time is provided, use midnight
                sale_datetime = datetime.combine(instance.date, time())

            with transaction.atomic():
                # Make sure to get the correct package_option
                package_option = None

                # First try to get package_option directly from appointment
                if hasattr(instance, "package_option") and instance.package_option:
                    package_option = instance.package_option
                    logger.info(
                        f"📝 Using package_option:{package_option.id} from appointment"
                    )

                # If not found, try to get from associated packages
                elif instance.user_package and instance.user_package.package_option:
                    package_option = instance.user_package.package_option
                    logger.info(
                        f"📝 Using package_option:{package_option.id} from user_package"
                    )
                elif instance.shared_package and instance.shared_package.package_option:
                    package_option = instance.shared_package.package_option
                    logger.info(
                        f"📝 Using package_option:{package_option.id} from shared_package"
                    )
                elif (
                    instance.unlimited_package
                    and instance.unlimited_package.package_option
                ):
                    package_option = instance.unlimited_package.package_option
                    logger.info(
                        f"📝 Using package_option:{package_option.id} from unlimited_package"
                    )

                # Determine sale type
                sale_type = (
                    "package"
                    if package_option
                    or (
                        instance.user_package
                        or instance.shared_package
                        or instance.unlimited_package
                    )
                    else "service"
                )

                # Log package_option status
                if package_option:
                    logger.info(
                        f"✅ Setting package_option:{package_option.id} on new sale"
                    )
                else:
                    logger.info(
                        f"⚠️ No package_option found for Appointment:{instance.id}"
                    )

                # Create sale with appropriate package type
                if instance.user_package:
                    sale = Sale.objects.create(
                        user=instance.customer,
                        sale_type=sale_type,
                        appointment=instance,
                        location=instance.location,
                        user_package=instance.user_package,
                        package_option=package_option,
                        total_price=instance.total_price,
                        discount=instance.discount,
                        payment_method=instance.payment_method,
                        discount_percentage=instance.discount_percentage,
                        created_at=sale_datetime,  # Set created_at explicitly
                    )
                    logger.info(
                        f"✅ Created Sale:{sale.id} with UserPackage:{instance.user_package.id}"
                    )
                elif instance.shared_package:
                    sale = Sale.objects.create(
                        user=instance.customer,
                        sale_type=sale_type,
                        appointment=instance,
                        location=instance.location,
                        shared_package=instance.shared_package,
                        package_option=package_option,
                        total_price=instance.total_price,
                        payment_method=instance.payment_method,
                        discount=instance.discount,
                        discount_percentage=instance.discount_percentage,
                        created_at=sale_datetime,  # Set created_at explicitly
                    )
                    logger.info(
                        f"✅ Created Sale:{sale.id} with SharedPackage:{instance.shared_package.id}"
                    )
                elif instance.unlimited_package:
                    sale = Sale.objects.create(
                        user=instance.customer,
                        sale_type=sale_type,
                        appointment=instance,
                        location=instance.location,
                        unlimited_package=instance.unlimited_package,
                        package_option=package_option,
                        total_price=instance.total_price,
                        payment_method=instance.payment_method,
                        discount=instance.discount,
                        discount_percentage=instance.discount_percentage,
                        created_at=sale_datetime,  # Set created_at explicitly
                    )
                    logger.info(
                        f"✅ Created Sale:{sale.id} with UnlimitedPackage:{instance.unlimited_package.id}"
                    )
                else:
                    sale = Sale.objects.create(
                        user=instance.customer,
                        sale_type=sale_type,
                        appointment=instance,
                        location=instance.location,
                        package_option=package_option,
                        total_price=instance.total_price,
                        payment_method=instance.payment_method,
                        discount=instance.discount,
                        discount_percentage=instance.discount_percentage,
                        created_at=sale_datetime,  # Set created_at explicitly
                    )
                    logger.info(
                        f"✅ Created Service Sale:{sale.id} for Appointment:{instance.id}"
                    )

                # Double-check package_option was set correctly
                if sale.package_option:
                    logger.info(
                        f"✅ Confirmed Sale:{sale.id} has package_option:{sale.package_option.id}"
                    )
                else:
                    logger.warning(f"⚠️ Sale:{sale.id} has no package_option set")
        except Exception as e:
            log_error(e, f"Creating sale for Appointment:{instance.id}")


@receiver(post_save, sender=Appointment)
def update_sale_on_appointment_change(sender, instance, created, **kwargs):
    """
    When an appointment's price is adjusted, update the associated Sale object.
    """
    if not created and instance.status == "check_in" and instance.total_price > 0:
        logger.info(f"🔄 Checking for sale updates for Appointment:{instance.id}")

        try:
            sale = Sale.objects.get(appointment=instance)

            # Create a datetime object from the appointment date and time
            from datetime import datetime, time

            if instance.time:
                sale_datetime = datetime.combine(instance.date, instance.time)
            else:
                # If no time is provided, use midnight
                sale_datetime = datetime.combine(instance.date, time())
            # Update created_at if it doesn't match the appointment date
            if sale.created_at.date() != instance.date:
                sale.created_at = sale_datetime
                logger.info(
                    f"🔄 Updating Sale date: {sale.created_at.date()} → {instance.date}"
                )

            # Make sure to get the correct package_option
            package_option = None

            # First try to get package_option directly from appointment
            if hasattr(instance, "package_option") and instance.package_option:
                package_option = instance.package_option
                logger.info(
                    f"📝 Using package_option:{package_option.id} from appointment"
                )

            # If not found, try to get from associated packages
            elif instance.user_package and instance.user_package.package_option:
                package_option = instance.user_package.package_option
                logger.info(
                    f"📝 Using package_option:{package_option.id} from user_package"
                )
            elif instance.shared_package and instance.shared_package.package_option:
                package_option = instance.shared_package.package_option
                logger.info(
                    f"📝 Using package_option:{package_option.id} from shared_package"
                )
            elif instance.unlimited_package and instance.unlimited_package.package_option:
                package_option = instance.unlimited_package.package_option
                logger.info(
                    f"📝 Using package_option:{package_option.id} from unlimited_package"
                )

            # Check if package_option has changed
            package_option_changed = False
            if package_option != sale.package_option:
                package_option_changed = True
                logger.info(
                    f"🔄 Updating package_option: {sale.package_option} → {package_option}"
                )

            # Check if price or discount has changed
            price_changed = sale.total_price != instance.total_price
            discount_changed = sale.discount_percentage != instance.discount_percentage

            # Check if package associations need updating
            package_changed = False

            # Update sale based on package type
            if instance.user_package and sale.user_package != instance.user_package:
                # Clear other package types
                sale.shared_package = None
                sale.unlimited_package = None
                # Set user package
                sale.user_package = instance.user_package
                sale.sale_type = "package"
                package_changed = True
                logger.info(
                    f"🔄 Updating Sale to use UserPackage:{instance.user_package.id}"
                )
            elif (
                instance.shared_package and sale.shared_package != instance.shared_package
            ):
                # Clear other package types
                sale.user_package = None
                sale.unlimited_package = None
                # Set shared package
                sale.shared_package = instance.shared_package
                sale.sale_type = "package"
                package_changed = True
                logger.info(
                    f"🔄 Updating Sale to use SharedPackage:{instance.shared_package.id}"
                )
            elif (
                instance.unlimited_package
                and sale.unlimited_package != instance.unlimited_package
            ):
                # Clear other package types
                sale.user_package = None
                sale.shared_package = None
                # Set unlimited package
                sale.unlimited_package = instance.unlimited_package
                sale.sale_type = "package"
                package_changed = True
                logger.info(
                    f"🔄 Updating Sale to use UnlimitedPackage:{instance.unlimited_package.id}"
                )
            elif not (
                instance.user_package
                or instance.shared_package
                or instance.unlimited_package
            ):
                # No package associated, make sure sale reflects this
                sale.user_package = None
                sale.shared_package = None
                sale.unlimited_package = None
                sale.sale_type = "service"
                logger.info(f"🔄 Updating Sale to be a service sale (no package)")

            # Update other fields if needed
            if (
                price_changed
                or discount_changed
                or package_changed
                or package_option_changed
                or sale.created_at.date() != instance.date
            ):
                if price_changed:
                    logger.info(
                        f"🔄 Updating Sale price: {sale.total_price} → {instance.total_price}"
                    )
                    sale.total_price = instance.total_price

                if discount_changed:
                    logger.info(
                        f"🔄 Updating Sale discount: {sale.discount_percentage}% → {instance.discount_percentage}%"
                    )
                    sale.discount = instance.discount
                    sale.discount_percentage = instance.discount_percentage

                # Always update package_option
                sale.package_option = package_option

                # Save the updates
                sale.save()

                # Double-check package_option was set correctly
                if sale.package_option:
                    logger.info(
                        f"✅ Confirmed Sale:{sale.id} has package_option:{sale.package_option.id}"
                    )
                else:
                    logger.warning(f"⚠️ Sale:{sale.id} has no package_option set")

                logger.info(f"✅ Updated Sale:{sale.id} for Appointment:{instance.id}")
            else:
                logger.info(f"📝 No updates needed for Sale:{sale.id}")

        except Sale.DoesNotExist:
            # If no sale exists but appointment is checked in with price > 0, create one
            logger.info(
                f"📝 No sale found for checked-in Appointment:{instance.id}, creating new sale"
            )
            create_sale_after_checkin(sender, instance, created, **kwargs)
        except Exception as e:
            log_error(e, f"Updating sale for Appointment:{instance.id}")


@receiver(post_delete, sender=Appointment)
def handle_sale_on_appointment_delete(sender, instance, **kwargs):
    """
    When an appointment is deleted, delete the associated Sale records.
    """
    logger.info(f"🔄 Removing sales for deleted Appointment:{instance.id}")

    try:
        sales = Sale.objects.filter(appointment=instance)
        if sales.exists():
            count = sales.count()
            sale_ids = list(sales.values_list("id", flat=True))
            sales.delete()
            logger.info(
                f"✅ Deleted {count} Sale(s) {sale_ids} for Appointment:{instance.id}"
            )
        else:
            logger.info(
                f"📝 No sales found for Appointment:{instance.id}, no deletion needed"
            )
    except Exception as e:
        log_error(e, f"Removing sales for Appointment:{instance.id}")


@receiver(post_save, sender=Sale)
def update_appointment_after_sale_change(sender, instance, **kwargs):
    """
    Signal to update the related appointment when a sale is changed.
    Updates the total_price and payment_method on the appointment to match the sale.
    """
    # Only proceed if this is a service sale with an appointment
    if instance.appointment:
        try:
            appointment = Appointment.objects.get(id=instance.appointment.id)

            # Update appointment fields to match the sale
            updated = False

            if appointment.total_price != instance.total_price:
                appointment.total_price = instance.total_price
                updated = True

            if appointment.payment_method != instance.payment_method:
                appointment.payment_method = instance.payment_method
                updated = True

            if updated:
                # Save without triggering other signals
                appointment.save(update_fields=["total_price", "payment_method"])
                logger.info(
                    f"✅ Updated Sale:{instance.id} for Appointment:{appointment.id}"
                )
        except Appointment.DoesNotExist:
            logger.info(f"📝 No appointment found for Sale:{instance.id}")
