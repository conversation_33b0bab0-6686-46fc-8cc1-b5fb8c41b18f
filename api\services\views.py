from rest_framework import generics, viewsets, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from .models import Service, ServicePackage, ServiceDuration, PackageOption
from .serializers import (
    ServiceSerializer,
    ServiceCreateUpdateSerializer,
    ServicePackageSerializer,
    ServicePackageCreateUpdateSerializer,
    ServiceDurationSerializer,
    PackageOptionSerializer,
)
from api.core.permissions import IsOwnerOrReceptionist


# Public Views


class ServiceListView(generics.ListAPIView):
    """
    Public view to list all services.
    """

    queryset = Service.objects.prefetch_related("durations").filter(is_public=True)
    serializer_class = ServiceSerializer
    permission_classes = []  # No authentication required


class ServicePackageListView(generics.ListAPIView):
    """
    Public view to list all service packages.
    """

    queryset = ServicePackage.objects.prefetch_related("services_included").filter(
        is_public=True, shared=False, unlimited=False
    )
    serializer_class = ServicePackageSerializer
    permission_classes = []  # No authentication required


class SharedPackageListView(generics.ListAPIView):
    """
    Public view to list all service packages.
    """

    queryset = ServicePackage.objects.prefetch_related("services_included").filter(
        is_public=True, shared=True, unlimited=False
    )
    serializer_class = ServicePackageSerializer
    permission_classes = []  # No authentication required


class UnlimitedPackageListView(generics.ListAPIView):
    """
    Public view to list all service packages.
    """

    queryset = ServicePackage.objects.prefetch_related("services_included").filter(
        is_public=True, shared=False, unlimited=True
    )
    serializer_class = ServicePackageSerializer
    permission_classes = []  # No authentication required


# Admin/Staff Views


class ServiceViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing services.
    """

    queryset = Service.objects.prefetch_related("durations").all()
    permission_classes = [IsOwnerOrReceptionist]

    def get_serializer_class(self):
        if self.action in ["create", "update", "partial_update"]:
            return ServiceCreateUpdateSerializer
        return ServiceSerializer

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)

    @action(detail=True, methods=["get"])
    def durations(self, request, pk=None):
        service = self.get_object()
        durations = service.durations.all()
        serializer = ServiceDurationSerializer(durations, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=["post"])
    def add_duration(self, request, pk=None):
        service = self.get_object()
        serializer = ServiceDurationSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save(service=service)
            return Response(serializer.data, status=201)
        return Response(serializer.errors, status=400)

    @action(detail=False, methods=["get"])
    def therapist_specific(self, request):
        therapist_id = request.query_params.get("therapist_id")
        if therapist_id:
            services = self.queryset.filter(therapist_specific_id=therapist_id)
            serializer = self.get_serializer(services, many=True)
            return Response(serializer.data)
        return Response({"error": "therapist_id parameter is required"}, status=400)


class ServicePackageViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing service packages.
    """

    queryset = ServicePackage.objects.prefetch_related(
        "services_included", "options"
    ).all()
    permission_classes = [IsOwnerOrReceptionist]

    def get_serializer_class(self):
        if self.action in ["create", "update", "partial_update"]:
            return ServicePackageCreateUpdateSerializer
        return ServicePackageSerializer

    def perform_create(self, serializer):
        serializer.save(created_by=self.request.user)

    @action(detail=True, methods=["get"])
    def options(self, request, pk=None):
        package = self.get_object()
        options = package.options.all()
        serializer = PackageOptionSerializer(options, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=["post"])
    def add_option(self, request, pk=None):
        package = self.get_object()
        serializer = PackageOptionSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save(package=package)
            return Response(serializer.data, status=201)
        return Response(serializer.errors, status=400)

    @action(detail=False, methods=["get"])
    def therapist_specific(self, request):
        therapist_id = request.query_params.get("therapist_id")
        if therapist_id:
            packages = self.queryset.filter(therapist_specific_id=therapist_id)
            serializer = self.get_serializer(packages, many=True)
            return Response(serializer.data)
        return Response({"error": "therapist_id parameter is required"}, status=400)

    @action(detail=False, methods=["get"])
    def shared(self, request):
        packages = self.queryset.filter(shared=True)
        serializer = self.get_serializer(packages, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=["get"])
    def unlimited(self, request):
        packages = self.queryset.filter(unlimited=True)
        serializer = self.get_serializer(packages, many=True)
        return Response(serializer.data)

    @action(detail=False, methods=["get"])
    def standard(self, request):
        packages = self.queryset.filter(shared=False, unlimited=False)
        serializer = self.get_serializer(packages, many=True)
        return Response(serializer.data)
