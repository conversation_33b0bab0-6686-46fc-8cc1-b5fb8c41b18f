# Generated by Django 4.2.19 on 2025-03-10 02:32

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='CashRegister',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('current_balance', models.DecimalField(decimal_places=2, max_digits=10)),
                ('location', models.CharField(choices=[('A', 'Studio Al Warqa Mall'), ('B', 'Studio Al mizhar branch')], max_length=1)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'unique_together': {('date', 'location')},
            },
        ),
        migrations.CreateModel(
            name='Product',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255)),
                ('category', models.CharField(choices=[('REVIVE', 'REVIVE'), ('HUMANTRA', 'HUMANTRA'), ('RUSH_CLOTHING', 'RUSH CLOTHING'), ('STRETCH_UP', 'STRETCH UP PRODUCTS')], max_length=20)),
                ('description', models.TextField(blank=True, null=True)),
                ('price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('vat_percentage', models.DecimalField(decimal_places=2, default=5.0, max_digits=5, validators=[django.core.validators.MinValueValidator(0)])),
                ('quantity_in_stock', models.PositiveIntegerField(default=0)),
                ('location', models.CharField(choices=[('A', 'Studio Al Warqa Mall'), ('B', 'Studio Al mizhar branch')], max_length=4)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='ProductSale',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('invoice_number', models.CharField(editable=False, max_length=20, unique=True)),
                ('subtotal_amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('vat_amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('total_amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('payment_method', models.CharField(choices=[('CASH', 'Cash'), ('CARD', 'Card'), ('ONLINE_LINK', 'Online Link')], max_length=20)),
                ('status', models.CharField(choices=[('COMPLETED', 'Completed'), ('REFUNDED', 'Refunded'), ('CANCELLED', 'Cancelled')], default='COMPLETED', max_length=20)),
                ('location', models.CharField(choices=[('A', 'Studio Al Warqa Mall'), ('B', 'Studio Al mizhar branch')], max_length=1)),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('customer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='product_sales', to=settings.AUTH_USER_MODEL)),
                ('staff', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='processed_sales', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='ProductSaleItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('quantity', models.PositiveIntegerField(default=1)),
                ('unit_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('vat_amount', models.DecimalField(decimal_places=2, max_digits=10)),
                ('total_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('product', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sale_items', to='retail.product')),
                ('product_sale', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sale_items', to='retail.productsale')),
            ],
        ),
        migrations.CreateModel(
            name='Expense',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('description', models.CharField(max_length=255)),
                ('category', models.CharField(choices=[('SUPPLIES', 'Supplies'), ('FOOD', 'Food'), ('MAINTENANCE', 'Maintenance'), ('UTILITIES', 'Utilities'), ('TRANSPORTATION', 'Transportation'), ('OTHER', 'Other')], max_length=20)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(0)])),
                ('receipt_image', models.ImageField(blank=True, null=True, upload_to='expense_receipts/')),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('cash_register', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='expenses', to='retail.cashregister')),
                ('staff', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='recorded_expenses', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='CashWithdrawal',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(0)])),
                ('reason', models.TextField()),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('cash_register', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='withdrawals', to='retail.cashregister')),
                ('staff', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='cash_withdrawals', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='CashDeposit',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, validators=[django.core.validators.MinValueValidator(0)])),
                ('reason', models.TextField()),
                ('notes', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('cash_register', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='deposits', to='retail.cashregister')),
                ('staff', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='cash_deposits', to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.CreateModel(
            name='DailySalesReport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('date', models.DateField()),
                ('location', models.CharField(choices=[('A', 'Studio Al Warqa Mall'), ('B', 'Studio Al mizhar branch')], max_length=1)),
                ('gross_sales_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('service_sales_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('product_sales_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('cash_sales_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('card_sales_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('online_link_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('vat_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('card_charges_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('link_charges_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('net_sales_amount', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('expenses_total', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('cash_withdrawals_total', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('starting_cash_balance', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('ending_cash_balance', models.DecimalField(decimal_places=2, default=0, max_digits=10)),
                ('notes', models.TextField(blank=True, null=True)),
                ('email_sent', models.BooleanField(default=False)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_reports', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'unique_together': {('date', 'location')},
            },
        ),
    ]
