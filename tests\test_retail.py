import pytest
from django.urls import reverse
from rest_framework import status
from decimal import Decimal
from django.utils import timezone
from datetime import timedelta

from api.retail.models import (
    Product,
    ProductSale,
    ProductSaleItem,
    CashRegister,
    CashWithdrawal,
    CashDeposit,
    Expense,
)


@pytest.mark.django_db
class TestProductAPI:
    """Test the Product API."""

    def test_create_product(self, client, admin_user):
        """Test creating a product as a staff user."""
        client.force_authenticate(user=admin_user)
        url = reverse("product-list")
        data = {
            "name": "Test Product",
            "category": "REVIVE",
            "description": "A test product",
            "price": "19.99",
            "vat_percentage": "5.00",
            "quantity_in_stock": 10,
            "location": "A",
            "is_active": True,
        }

        response = client.post(url, data, format="json")
        assert response.status_code == status.HTTP_201_CREATED
        assert Product.objects.count() == 1
        assert Product.objects.get().name == "Test Product"

    def test_update_product_stock(self, client, admin_user):
        """Test the update_stock action."""
        product = Product.objects.create(
            name="Test Product",
            category="REVIVE",
            price=Decimal("19.99"),
            quantity_in_stock=10,
            location="A",
        )

        client.force_authenticate(user=admin_user)
        url = reverse("product-update-stock")
        data = {"product_updates": [{"id": product.id, "quantity_change": 5}]}

        response = client.post(url, data, format="json")
        assert response.status_code == status.HTTP_200_OK
        product.refresh_from_db()
        assert product.quantity_in_stock == 15

    def test_prevent_negative_stock(self, client, admin_user):
        """Test that stock can't be reduced below zero."""
        product = Product.objects.create(
            name="Test Product",
            category="REVIVE",
            price=Decimal("19.99"),
            quantity_in_stock=10,
            location="A",
        )

        client.force_authenticate(user=admin_user)
        url = reverse("product-update-stock")
        data = {"product_updates": [{"id": product.id, "quantity_change": -15}]}

        response = client.post(url, data, format="json")
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        product.refresh_from_db()
        assert product.quantity_in_stock == 10  # Unchanged


@pytest.mark.django_db
class TestProductSaleAPI:
    """Test the ProductSale API."""

    def test_create_sale(self, client, admin_user, customer_user):
        """Test creating a sale."""
        product = Product.objects.create(
            name="Test Product",
            category="REVIVE",
            price=Decimal("19.99"),
            vat_percentage=Decimal("5.00"),
            quantity_in_stock=10,
            location="A",
        )

        client.force_authenticate(user=admin_user)
        url = reverse("productsale-list")

        # Calculate values
        unit_price = Decimal("19.99")
        quantity = 2
        subtotal = unit_price * quantity
        vat_amount = subtotal * Decimal("0.05")
        total = subtotal + vat_amount

        data = {
            "customer": customer_user.id,
            "subtotal_amount": str(subtotal),
            "vat_amount": str(vat_amount),
            "total_amount": str(total),
            "payment_method": "CASH",
            "location": "A",
            "sale_items": [
                {
                    "product": product.id,
                    "quantity": quantity,
                    "unit_price": str(unit_price),
                    "vat_amount": str(vat_amount),
                    "total_price": str(total),
                }
            ],
        }

        response = client.post(url, data, format="json")
        assert response.status_code == status.HTTP_201_CREATED
        assert ProductSale.objects.count() == 1
        assert ProductSaleItem.objects.count() == 1

        # Check that inventory was updated
        product.refresh_from_db()
        assert product.quantity_in_stock == 8

    def test_refund_sale(self, client, admin_user, customer_user):
        """Test refunding a sale."""
        product = Product.objects.create(
            name="Test Product",
            category="REVIVE",
            price=Decimal("19.99"),
            quantity_in_stock=8,  # After a sale
            location="A",
        )

        # Create a sale
        sale = ProductSale.objects.create(
            customer=customer_user,
            staff=admin_user,
            subtotal_amount=Decimal("39.98"),
            vat_amount=Decimal("2.00"),
            total_amount=Decimal("41.98"),
            payment_method="CASH",
            status="COMPLETED",
            location="A",
        )

        # Add sale item
        ProductSaleItem.objects.create(
            product_sale=sale,
            product=product,
            quantity=2,
            unit_price=Decimal("19.99"),
            vat_amount=Decimal("2.00"),
            total_price=Decimal("41.98"),
        )

        client.force_authenticate(user=admin_user)
        url = reverse("productsale-refund", args=[sale.id])

        response = client.post(url)
        assert response.status_code == status.HTTP_200_OK

        # Check that sale status was updated
        sale.refresh_from_db()
        assert sale.status == "REFUNDED"

        # Check that inventory was restored
        product.refresh_from_db()
        assert product.quantity_in_stock == 10


@pytest.mark.django_db
class TestCashRegisterAPI:
    """Test the CashRegister API."""

    def test_create_cash_register(self, client, admin_user):
        """Test creating a cash register."""
        client.force_authenticate(user=admin_user)
        url = reverse("cashregister-list")
        data = {
            "date": timezone.now().date().isoformat(),
            "current_balance": "500.00",
            "location": "A",
            "notes": "Starting balance for today",
        }

        response = client.post(url, data, format="json")
        assert response.status_code == status.HTTP_201_CREATED
        assert CashRegister.objects.count() == 1

    def test_get_current_register(self, client, admin_user):
        """Test the current action for getting today's register."""
        # Create yesterday's register
        yesterday = timezone.now().date() - timedelta(days=1)
        CashRegister.objects.create(
            date=yesterday, current_balance=Decimal("100.00"), location="A"
        )

        client.force_authenticate(user=admin_user)
        url = reverse("cashregister-current")
        response = client.get(url, {"location": "A"})

        assert response.status_code == status.HTTP_200_OK
        # Verify a new register for today was created
        assert CashRegister.objects.count() == 2

        # Today's register should have yesterday's ending balance
        today_register = CashRegister.objects.filter(
            date=timezone.now().date(), location="A"
        ).first()

        assert today_register is not None
        assert today_register.current_balance == Decimal("100.00")


@pytest.mark.django_db
class TestCashWithdrawalAPI:
    """Test the CashWithdrawal API."""

    def test_create_withdrawal(self, client, admin_user):
        """Test creating a withdrawal."""
        register = CashRegister.objects.create(
            date=timezone.now().date(), current_balance=Decimal("500.00"), location="A"
        )

        client.force_authenticate(user=admin_user)
        url = reverse("cashwithdrawal-list")
        data = {
            "cash_register": register.id,
            "amount": "100.00",
            "reason": "Test withdrawal",
            "notes": "For testing",
        }

        response = client.post(url, data, format="json")
        assert response.status_code == status.HTTP_201_CREATED

        # Verify register balance was updated
        register.refresh_from_db()
        assert register.current_balance == Decimal("400.00")

    def test_prevent_overdraft(self, client, admin_user):
        """Test that withdrawals can't exceed register balance."""
        register = CashRegister.objects.create(
            date=timezone.now().date(), current_balance=Decimal("50.00"), location="A"
        )

        client.force_authenticate(user=admin_user)
        url = reverse("cashwithdrawal-list")
        data = {
            "cash_register": register.id,
            "amount": "100.00",
            "reason": "Test withdrawal",
            "notes": "For testing",
        }

        response = client.post(url, data, format="json")
        assert response.status_code == status.HTTP_400_BAD_REQUEST

        # Verify register balance was not updated
        register.refresh_from_db()
        assert register.current_balance == Decimal("50.00")


@pytest.mark.django_db
class TestExpenseAPI:
    """Test the Expense API."""

    def test_create_expense(self, client, admin_user):
        """Test creating an expense."""
        register = CashRegister.objects.create(
            date=timezone.now().date(), current_balance=Decimal("500.00"), location="A"
        )

        client.force_authenticate(user=admin_user)
        url = reverse("expense-list")
        data = {
            "cash_register": register.id,
            "description": "Office supplies",
            "category": "SUPPLIES",
            "amount": "50.00",
            "notes": "Purchased pens and paper",
        }

        response = client.post(url, data, format="json")
        assert response.status_code == status.HTTP_201_CREATED

        # Verify register balance was updated
        register.refresh_from_db()
        assert register.current_balance == Decimal("450.00")


@pytest.mark.django_db
class TestDailySalesReportAPI:
    """Test the DailySalesReport API."""

    def test_generate_report(self, client, admin_user, customer_user):
        """Test generating a daily sales report."""
        today = timezone.now().date()

        # Create a cash register
        register = CashRegister.objects.create(
            date=today, current_balance=Decimal("650.00"), location="A"
        )

        # Create a product
        product = Product.objects.create(
            name="Test Product",
            category="REVIVE",
            price=Decimal("19.99"),
            quantity_in_stock=10,
            location="A",
        )

        # Create a sale
        sale = ProductSale.objects.create(
            customer=customer_user,
            staff=admin_user,
            subtotal_amount=Decimal("39.98"),
            vat_amount=Decimal("2.00"),
            total_amount=Decimal("41.98"),
            payment_method="CASH",
            status="COMPLETED",
            location="A",
            created_at=timezone.now(),
        )

        # Add sale item
        ProductSaleItem.objects.create(
            product_sale=sale,
            product=product,
            quantity=2,
            unit_price=Decimal("19.99"),
            vat_amount=Decimal("2.00"),
            total_price=Decimal("41.98"),
        )

        # Create an expense
        Expense.objects.create(
            cash_register=register,
            description="Cleaning supplies",
            category="SUPPLIES",
            amount=Decimal("20.00"),
            staff=admin_user,
        )

        # Create a withdrawal
        CashWithdrawal.objects.create(
            cash_register=register,
            amount=Decimal("30.00"),
            reason="Bank deposit",
            staff=admin_user,
        )

        client.force_authenticate(user=admin_user)
        url = reverse("dailysalesreport-generate")
        data = {"date": today.isoformat(), "location": "A"}

        response = client.post(url, data, format="json")
        assert response.status_code == status.HTTP_201_CREATED

        # Verify report data
        report_data = response.json()
        assert report_data["product_sales_amount"] == "41.98"
        assert report_data["cash_sales_amount"] == "41.98"
        assert report_data["vat_amount"] == "2.00"
        assert report_data["expenses_total"] == "20.00"
        assert report_data["cash_withdrawals_total"] == "30.00"
