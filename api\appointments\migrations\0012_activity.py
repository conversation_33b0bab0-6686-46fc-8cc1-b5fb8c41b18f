# Generated by Django 4.0 on 2025-02-06 20:17

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('authentication', '0005_historicaluser_gender_user_gender'),
        ('appointments', '0011_alter_userpackage_active_alter_userpackage_user'),
    ]

    operations = [
        migrations.CreateModel(
            name='Activity',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('activity_type', models.CharField(choices=[('booking', 'Booking'), ('comfirmation', 'Comfirmation'), ('cancellation', 'Cancellation')], max_length=20)),
                ('description', models.CharField(max_length=50)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='customer', to='authentication.user')),
                ('staff', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='staff', to='authentication.user')),
            ],
        ),
    ]
