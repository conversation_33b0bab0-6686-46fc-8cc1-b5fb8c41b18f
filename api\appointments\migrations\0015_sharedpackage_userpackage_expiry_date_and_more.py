# Generated by Django 4.2.19 on 2025-02-19 11:01

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('services', '0001_initial'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('appointments', '0014_delete_packagebalance'),
    ]

    operations = [
        migrations.CreateModel(
            name='SharedPackage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_time', models.PositiveIntegerField(help_text='Total minutes available for all users in this shared package')),
                ('remaining_time', models.PositiveIntegerField(help_text='Remaining minutes available for use')),
                ('expiry_date', models.DateTimeField(blank=True, help_text='The date when the package will expire.', null=True)),
                ('active', models.BooleanField(default=True, help_text='Indicates if the package is active (i.e. remaining_time > 0)')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('package_option', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='shared_packages', to='services.packageoption')),
            ],
        ),
        migrations.AddField(
            model_name='userpackage',
            name='expiry_date',
            field=models.DateTimeField(blank=True, help_text='The date when the package will expire.', null=True),
        ),
        migrations.AlterField(
            model_name='appointment',
            name='payment_method',
            field=models.CharField(blank=True, choices=[('cash', 'Cash'), ('card', 'Card'), ('link', 'Link')], default='', max_length=20),
        ),
        migrations.AddField(
            model_name='appointment',
            name='shared_package',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='appointments', to='appointments.sharedpackage'),
        ),
        migrations.CreateModel(
            name='SharedPackageUser',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('added_at', models.DateTimeField(auto_now_add=True)),
                ('shared_package', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='users', to='appointments.sharedpackage')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='shared_packages', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'unique_together': {('shared_package', 'user')},
            },
        ),
    ]
