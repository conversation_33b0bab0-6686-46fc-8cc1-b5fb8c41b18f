#!/usr/bin/env python
import os
import sys

def main():
    '''Set the default settings module to 'config.settings.local' for local development.
    This settings module extends from 'config.settings.base' with additional settings
    that are specific to local development, such as DEBUG=True, detailed logging, 
    local database configurations, etc. This helps in isolating production settings
    from development-specific settings.'''
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.local')

    try:
        from django.core.management import execute_from_command_line
    except ImportError as exc:
        raise ImportError(
            "Couldn't import Django. Are you sure it's installed and "
            "available on your PYTHONPATH environment variable? Did you "
            "forget to activate a virtual environment?"
        ) from exc
    execute_from_command_line(sys.argv)

if __name__ == '__main__':
    main()