from rest_framework import serializers
from django.utils import timezone
from decimal import Decimal
from .models import (
    Appointment,
    AppointmentService,
    UserPackage,
    SharedPackage,
    SharedPackageUser,
    UnlimitedPackage,
    AppointmentAdditionalService,
    Discount,
    Reward,
    RewardBalance,
    Sale,
)

from api.services.models import PackageOption
from api.staff.serializers import TherapistProfileAppointmentSerializer
from api.services.serializers import PackageOptionSerializer
from api.authentication.serializers import (
    UserProfileSerializer,
    UserProfileAppointmentSerializer,
)
from django.contrib.auth import get_user_model

User = get_user_model()


class SharedPackageUserSerializer(serializers.ModelSerializer):
    """Serializer for users linked to a shared package."""

    user = serializers.PrimaryKeyRelatedField(read_only=True)
    user_obj = UserProfileSerializer(source="user", read_only=True)

    class Meta:
        model = SharedPackageUser
        fields = ["id", "user", "user_obj", "added_at"]


class SharedPackageSerializer(serializers.ModelSerializer):
    """Serializer for Shared Packages."""

    package_option = PackageOptionSerializer(read_only=True)
    expiry_date = serializers.SerializerMethodField()
    users = SharedPackageUserSerializer(source="users.all", many=True, read_only=True)
    users_count = serializers.SerializerMethodField()

    class Meta:
        model = SharedPackage
        fields = (
            "id",
            "total_time",
            "remaining_time",
            "active",
            "package_option",
            "expiry_date",
            "users",
            "users_count",
        )

    def get_expiry_date(self, obj):
        return obj.expiry_date

    def get_users_count(self, obj):
        """Get the number of users who have access to this package"""
        return SharedPackageUser.objects.filter(shared_package=obj).count()


class UnlimitedPackageSerializer(serializers.ModelSerializer):
    expiry_date = serializers.SerializerMethodField()
    user = UserProfileSerializer(read_only=True)
    package_option = PackageOptionSerializer(read_only=True)

    class Meta:
        model = UnlimitedPackage
        fields = [
            "id",
            "user",
            "total_time",
            "remaining_time",
            "package_option",
            "time_deducted",
            "active",
            "expiry_date",
            "created_at",
            "updated_at",
        ]

    def get_expiry_date(self, obj):
        return obj.expiry_date


class AppointmentServiceSerializer(serializers.ModelSerializer):
    service_name = serializers.ReadOnlyField(source="service.name")

    class Meta:
        model = AppointmentService
        fields = ["id", "service", "service_name", "duration"]


class AppointmentAdditionalServiceSerializer(serializers.ModelSerializer):
    class Meta:
        model = AppointmentAdditionalService
        fields = [
            "id",
            "appointment",
            "service_type",
            "duration",
            "price_per_minute",
            "total_price",
            "added_at",
            "added_by",
        ]
        read_only_fields = ["id", "added_at", "total_price"]


class ServiceInfoSerializer(serializers.Serializer):
    """Simple serializer for service information in usage history"""

    id = serializers.IntegerField(source="service.id")
    name = serializers.CharField(source="service.name")
    duration = serializers.IntegerField()


class PackageUsageSerializer(serializers.ModelSerializer):
    """Serializer for package usage history"""

    user = UserProfileSerializer(source="customer", read_only=True)
    therapist = serializers.SerializerMethodField()
    services = serializers.SerializerMethodField()
    duration = serializers.SerializerMethodField()

    class Meta:
        model = Appointment
        fields = [
            "id",
            "date",
            "time",
            "user",
            "therapist",
            "status",
            "location",
            "duration",  # Keep this in fields
            "services",
            "notes",
        ]

    def get_therapist(self, obj):
        """Get therapist data if available"""
        if not obj.therapist:
            return None

        return {
            "id": obj.therapist.id,
            "first_name": obj.therapist.user.first_name,
            "last_name": obj.therapist.user.last_name,
            "email": obj.therapist.user.email,
        }

    def get_services(self, obj):
        """Get services associated with this appointment"""
        appointment_services = obj.appointment_services.all()
        return ServiceInfoSerializer(appointment_services, many=True).data

    def get_duration(self, obj):
        """Get total duration of the appointment"""
        return obj.total_duration


class AddMinutesServiceSerializer(serializers.Serializer):
    service_type = serializers.ChoiceField(
        choices=["Massage", "Stretching", "Compression", "Red-Light Therapy", "Physio"]
    )
    minutes = serializers.IntegerField(min_value=1)

    def validate(self, data):
        # Validate that minutes is a positive integer
        if data["minutes"] <= 0:
            raise serializers.ValidationError("Minutes must be greater than 0")
        return data


class AddMinutesToAppointmentSerializer(serializers.Serializer):
    appointment_id = serializers.IntegerField()
    services = AddMinutesServiceSerializer(many=True)

    def validate_appointment_id(self, value):
        try:
            appointment = Appointment.objects.get(id=value)

            # Check if this is a service-based appointment
            if appointment.appointment_services.count() == 0:
                raise serializers.ValidationError(
                    "Cannot add minutes to a package-based appointment."
                )

        except Appointment.DoesNotExist:
            raise serializers.ValidationError("Appointment not found")

        return value

    def validate_services(self, services):
        if not services:
            raise serializers.ValidationError("At least one service must be provided")
        return services


class UserPackageSerializer(serializers.ModelSerializer):
    # We want to return the package option data in a similar structure.
    package_option = PackageOptionSerializer(read_only=True)
    expiry_date = serializers.SerializerMethodField()

    class Meta:
        model = UserPackage
        # You can include extra fields if desired
        fields = (
            "id",
            "total_time",
            "remaining_time",
            "time_deducted",
            "active",
            "package_option",
            "expiry_date",
        )

    def get_expiry_date(self, obj):
        # Return the computed expiry_date from the model.
        return obj.expiry_date


class CustomerRewardSerializer(serializers.ModelSerializer):
    class Meta:
        model = Reward
        fields = [
            "id",
            "points",
            "reward_date",
            "created_at",
        ]


class AdminRewardSerializer(serializers.ModelSerializer):
    class Meta:
        model = Reward
        fields = [
            "id",
            "points",
            "factor",
            "reward_date",
            "created_at",
            "updated_at",
        ]


class RewardBalanceSerializer(serializers.ModelSerializer):
    class Meta:
        model = RewardBalance
        fields = ["total_points", "last_updated"]


class DiscountSerializer(serializers.ModelSerializer):
    class Meta:
        model = Discount
        fields = [
            "id",
            "name",
            "code",
            "discount_percentage",
            "description",
            "valid_from",
            "valid_until",
        ]


class DirectPackageSaleSerializer(serializers.Serializer):
    user_id = serializers.IntegerField()
    package_option_id = serializers.IntegerField()
    package_type = serializers.ChoiceField(
        choices=["user_package", "shared_package", "unlimited_package"]
    )
    discount_id = serializers.IntegerField(required=False, allow_null=True)
    discount_percentage = serializers.DecimalField(
        required=False,
        allow_null=False,  # Add this to prevent None values
        default=Decimal("0"),  # Ensure it's a Decimal object
        max_digits=10,
        decimal_places=5,
    )
    total_price = serializers.DecimalField(max_digits=10, decimal_places=5)
    location = serializers.CharField(default="A")
    payment_method = serializers.CharField(default="card")

    def to_internal_value(self, data):
        # First, make a copy of the data to avoid modifying the original
        mutable_data = data.copy() if hasattr(data, "copy") else dict(data)

        # Round the total_price before validation
        if "total_price" in mutable_data and mutable_data["total_price"] is not None:
            mutable_data["total_price"] = round(
                Decimal(str(mutable_data["total_price"])), 5
            )

        # Round the discount_percentage if present
        if (
            "discount_percentage" in mutable_data
            and mutable_data["discount_percentage"] is not None
        ):
            mutable_data["discount_percentage"] = round(
                Decimal(str(mutable_data["discount_percentage"])), 5
            )

        return super().to_internal_value(mutable_data)

    def validate(self, data):
        """
        Additional validation to ensure referenced objects exist.
        """
        # Validate user exists
        try:
            User.objects.get(pk=data["user_id"])
        except User.DoesNotExist:
            raise serializers.ValidationError({"user_id": "User does not exist"})

        # Validate package_option exists
        try:
            PackageOption.objects.get(pk=data["package_option_id"])
        except PackageOption.DoesNotExist:
            raise serializers.ValidationError(
                {"package_option_id": "Package option does not exist"}
            )

        # Validate discount exists if provided
        discount_id = data.get("discount_id")
        discount_percentage = data.get("discount_percentage", Decimal("0"))
        if "discount_percentage" not in data or data["discount_percentage"] is None:
            data["discount_percentage"] = Decimal("0")

        return data


class SaleSerializer(serializers.ModelSerializer):
    """
    Serializer for the Sale model with additional related data.
    """

    user_name = serializers.SerializerMethodField()
    user_email = serializers.SerializerMethodField()
    package_name = serializers.SerializerMethodField()

    class Meta:
        model = Sale
        fields = [
            "id",
            "user",
            "user_name",
            "user_email",
            "sale_type",
            "appointment",
            "user_package",
            "shared_package",
            "unlimited_package",
            "package_option",
            "package_name",
            "total_price",
            "discount",
            "discount_percentage",
            "payment_method",
            "location",
            "invoice",
            "invoice_number",
            "created_at",
        ]

    def get_user_name(self, obj):
        return obj.user.first_name + " " + obj.user.last_name if obj.user else None

    def get_user_email(self, obj):
        return obj.user.email if obj.user else None

    def get_package_name(self, obj):
        if obj.package_option:
            return obj.package_option.package.name
        return None


class SaleUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for partial updates to Sale objects.
    Updated to allow editing of created_at field.
    """

    class Meta:
        model = Sale
        fields = [
            "total_price",
            "payment_method",
            "discount_percentage",
            "location",
            "created_at",  # Added created_at field
        ]

    def validate_total_price(self, value):
        """Validate total_price is positive"""
        if value < 0:
            raise serializers.ValidationError("Total price cannot be negative")
        return value

    def validate_discount_percentage(self, value):
        """Validate discount_percentage is between 0 and 100"""
        if value < 0 or value > 100:
            raise serializers.ValidationError(
                "Discount percentage must be between 0 and 100"
            )
        return value

    def validate_payment_method(self, value):
        """Validate payment_method is one of the allowed choices"""
        allowed_methods = dict(Sale.PAYMENT_METHOD_CHOICES).keys()
        if value not in allowed_methods:
            raise serializers.ValidationError(
                f"Payment method must be one of: {', '.join(allowed_methods)}"
            )
        return value

    def validate_location(self, value):
        """Validate location is one of the allowed choices"""
        allowed_locations = dict(Sale.LOCATION_CHOICES).keys()
        if value not in allowed_locations:
            raise serializers.ValidationError(
                f"Location must be one of: {', '.join(allowed_locations)}"
            )
        return value

    def validate_created_at(self, value):
        """Validate created_at is not in the future"""
        if value > timezone.now():
            raise serializers.ValidationError("Sale date cannot be in the future")
        return value


class AppointmentSerializer(serializers.ModelSerializer):
    # 1) Keep `customer` as an ID
    customer = serializers.PrimaryKeyRelatedField(read_only=True)
    customer_obj = UserProfileAppointmentSerializer(source="customer", read_only=True)

    # 2) Keep `therapist` as an ID
    therapist = serializers.PrimaryKeyRelatedField(read_only=True)
    therapist_obj = TherapistProfileAppointmentSerializer(
        source="therapist", read_only=True
    )

    # 3) Keep `package_option` as an ID
    package_option = serializers.PrimaryKeyRelatedField(read_only=True)
    package_option_obj = PackageOptionSerializer(source="package_option", read_only=True)

    # 4) Keep `user_package` as an ID (OneToOne Field)
    user_package = serializers.PrimaryKeyRelatedField(read_only=True)
    user_package_obj = UserPackageSerializer(source="user_package", read_only=True)

    # 5) Keep `shared_package` as an ID
    shared_package = serializers.PrimaryKeyRelatedField(read_only=True)
    shared_package_obj = SharedPackageSerializer(source="shared_package", read_only=True)

    # 6) Unlimited Package
    unlimited_package = serializers.PrimaryKeyRelatedField(read_only=True)
    unlimited_package_obj = UnlimitedPackageSerializer(
        source="unlimited_package", read_only=True
    )

    # 7) Appointment Services
    appointment_services = AppointmentServiceSerializer(many=True, read_only=True)

    # Additional services (if any)
    additional_services = AppointmentAdditionalServiceSerializer(
        many=True, read_only=True
    )

    discount_obj = DiscountSerializer(source="discount", read_only=True)

    # New field for reward points
    reward_points = serializers.SerializerMethodField()

    def get_reward_points(self, obj):
        # Try to return the reward points from the related Reward object,
        # otherwise return 0 if no reward exists.
        if hasattr(obj, "reward") and obj.reward is not None:
            return obj.reward.points
        return 0

    class Meta:
        model = Appointment
        fields = [
            "id",
            "customer",
            "customer_obj",
            "therapist",
            "therapist_obj",
            "appointment_services",
            "additional_services",
            "package_option",
            "package_option_obj",
            "user_package",
            "user_package_obj",
            "shared_package",
            "shared_package_obj",
            "unlimited_package",
            "unlimited_package_obj",
            "location",
            "date",
            "time",
            "total_duration",
            "total_price",
            "notes",
            "status",
            "needs_manual_split",
            "missing_services",
            "discount_percentage",
            "discount_obj",
            "reward_points",  # Added reward points to the output
            "created_at",
            "updated_at",
        ]
