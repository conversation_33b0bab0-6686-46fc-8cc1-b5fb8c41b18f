# Generated by Django 4.0 on 2024-12-13 15:09

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('authentication', '0003_remove_historicaluser_otp_secret_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='TherapistProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('qualifications', models.TextField(blank=True, help_text="Therapist's qualifications")),
                ('start_year', models.PositiveIntegerField(blank=True, help_text='Year the therapist started practicing', null=True)),
                ('gender_preference', models.CharField(choices=[('M', 'Male'), ('F', 'Female'), ('B', 'Both')], default='B', help_text='Client gender preference: Male, Female, or Both', max_length=1)),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='authentication.user')),
            ],
        ),
        migrations.CreateModel(
            name='WorkingHour',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('day', models.IntegerField(choices=[(0, 'Monday'), (1, 'Tuesday'), (2, 'Wednesday'), (3, 'Thursday'), (4, 'Friday'), (5, 'Saturday'), (6, 'Sunday')])),
                ('start_time', models.TimeField()),
                ('end_time', models.TimeField()),
                ('therapist', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='working_hours', to='staff.therapistprofile')),
            ],
            options={
                'ordering': ['therapist', 'day', 'start_time'],
                'unique_together': {('therapist', 'day', 'start_time', 'end_time')},
            },
        ),
    ]
