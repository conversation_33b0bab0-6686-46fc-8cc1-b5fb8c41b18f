from datetime import datetime, timed<PERSON>ta
import calendar
from django.db.models import Q, Sum
from django.utils.dateparse import parse_date
from rest_framework.exceptions import ValidationError
from rest_framework import status
from rest_framework import generics
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from rest_framework.pagination import PageNumberPagination
from rest_framework.views import APIView
from django.shortcuts import get_object_or_404
from django.contrib.auth import get_user_model
from rest_framework.filters import SearchFilter
from django.http import HttpResponse
from django_filters.rest_framework import DjangoFilterBackend
from api.appointments.models import (
    Activity,
    Appointment,
    AppointmentService,
    UserPackage,
    SharedPackage,
    SharedPackageUser,
    UnlimitedPackage,
    Discount,
    RewardBalance,
)
from api.services.models import Service, ServiceDuration, PackageOption
from api.appointments.serializers import (
    AppointmentSerializer,
    UserPackageSerializer,
    SharedPackageSerializer,
    UnlimitedPackageSerializer,
    AddMinutesToAppointmentSerializer,
    AppointmentAdditionalServiceSerializer,
    AppointmentAdditionalService,
    SharedPackageUserSerializer,
    PackageUsageSerializer,
    DiscountSerializer,
    AdminRewardSerializer,
    RewardBalanceSerializer,
)

from api.core.permissions import IsOwnerOrReceptionist
from api.staff.models import TherapistProfile, WorkingHour
from api.authentication.serializers import UserProfileSerializer
import traceback
from decimal import Decimal
from rest_framework.exceptions import NotFound, APIException, PermissionDenied
from api.staff.serializers import (
    TherapistProfileOptimizedSerializer,
)
from django.utils import timezone
import csv

User = get_user_model()


from utils.logging import api_logger, log_request_data, log_response_data, log_error


class ApplyDiscountView(generics.UpdateAPIView):
    """
    PATCH endpoint to apply a discount to an appointment.
    Either a discount_code or a manual discount_percentage should be provided.
    """

    permission_classes = [IsAuthenticated, IsOwnerOrReceptionist]
    serializer_class = AppointmentSerializer
    queryset = Appointment.objects.all()

    def patch(self, request, *args, **kwargs):
        log_request_data(request, f"📥 Apply Discount Request")
        appointment_id = kwargs.get("appointment_id")

        api_logger.info(f"🔄 Processing discount for Appointment:{appointment_id}")

        try:
            appointment = self.get_queryset().get(id=appointment_id)
            api_logger.info(
                f"🔍 Found appointment for Customer:{appointment.customer.id}"
            )
        except Appointment.DoesNotExist:
            api_logger.warning(f"❌ Appointment not found: {appointment_id}")
            response = Response(
                {"detail": "Appointment not found."}, status=status.HTTP_404_NOT_FOUND
            )
            log_response_data(response, "📤 Apply Discount Error Response")
            return response

        discount_code = request.data.get("discount_code", None)
        manual_discount_percentage = request.data.get("discount_percentage", None)

        api_logger.info(
            f"🔍 Applying discount - Code:{discount_code}, Manual:{manual_discount_percentage}"
        )

        if discount_code:
            try:
                discount = Discount.objects.get(code=discount_code)
                now = timezone.now()
                if discount.valid_from <= now <= discount.valid_until:
                    api_logger.info(
                        f"✅ Valid discount code: {discount_code}, percentage: {discount.discount_percentage}%"
                    )
                    appointment.discount = discount
                    appointment.discount_percentage = discount.discount_percentage
                else:
                    api_logger.warning(f"❌ Expired discount code: {discount_code}")
                    response = Response(
                        {"detail": "Discount code is not currently valid."},
                        status=status.HTTP_400_BAD_REQUEST,
                    )
                    log_response_data(response, "📤 Apply Discount Error Response")
                    return response
            except Discount.DoesNotExist:
                api_logger.warning(f"❌ Invalid discount code: {discount_code}")
                response = Response(
                    {"detail": "Invalid discount code."},
                    status=status.HTTP_400_BAD_REQUEST,
                )
                log_response_data(response, "📤 Apply Discount Error Response")
                return response
        elif manual_discount_percentage is not None:
            try:
                discount_val = Decimal(manual_discount_percentage)
                api_logger.info(f"✅ Manual discount applied: {discount_val}%")
            except Exception as e:
                api_logger.warning(
                    f"❌ Invalid discount percentage: {manual_discount_percentage}"
                )
                log_error(
                    e,
                    f"Converting manual discount for Appointment:{appointment_id}",
                    log_full_trace=False,
                )
                response = Response(
                    {"detail": "Invalid discount percentage."},
                    status=status.HTTP_400_BAD_REQUEST,
                )
                log_response_data(response, "📤 Apply Discount Error Response")
                return response
            appointment.discount_percentage = discount_val
            appointment.discount = None  # Clear any existing discount code reference.
        else:
            api_logger.warning(
                f"❌ No discount provided for Appointment:{appointment_id}"
            )
            response = Response(
                {"detail": "No discount provided."},
                status=status.HTTP_400_BAD_REQUEST,
            )
            log_response_data(response, "📤 Apply Discount Error Response")
            return response

        # Optionally recalculate the total price based on the applied discount.
        original_price = appointment.total_price
        if appointment.discount_percentage > 0:
            appointment.total_price = (
                appointment.total_price
                * (Decimal("100") - appointment.discount_percentage)
                / Decimal("100")
            )
            api_logger.info(
                f"💰 Price adjusted: ${original_price} → ${appointment.total_price} (-{appointment.discount_percentage}%)"
            )

        try:
            appointment.save()
            api_logger.info(
                f"✅ Successfully applied discount to Appointment:{appointment_id}"
            )
        except Exception as e:
            log_error(
                e, f"Saving appointment with discount for Appointment:{appointment_id}"
            )
            response = Response(
                {"detail": "Error saving appointment with discount."},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
            log_response_data(response, "📤 Apply Discount Error Response")
            return response

        serializer = self.get_serializer(appointment)
        response = Response(serializer.data, status=status.HTTP_200_OK)
        log_response_data(response, "📤 Apply Discount Success Response")
        return response


class AvailableDiscountCodesView(generics.ListAPIView):
    """
    GET endpoint to fetch all available discount codes.
    Only discounts with valid dates (valid_from <= now <= valid_until) are returned.
    """

    permission_classes = [IsAuthenticated, IsOwnerOrReceptionist]
    serializer_class = DiscountSerializer

    def get_queryset(self):
        log_request_data(self.request, "📥 Available Discounts Request")
        api_logger.info("🔍 Retrieving active discount codes")

        now = timezone.now()
        discounts = Discount.objects.filter(valid_from__lte=now, valid_until__gte=now)

        api_logger.info(f"✅ Found {discounts.count()} active discount codes")
        return discounts

    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()
        serializer = self.get_serializer(queryset, many=True)
        response = Response(serializer.data)
        log_response_data(response, "📤 Available Discounts Response")
        return response
