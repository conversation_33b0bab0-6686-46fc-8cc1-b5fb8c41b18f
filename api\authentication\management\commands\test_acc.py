from django.core.management.base import BaseCommand

from django.contrib.auth import get_user_model

from api.appointments import models as ap
from api.staff import models as st
from api.services import models as se


User = get_user_model()


class Command(BaseCommand):
    def handle(self, *args, **options):
        user, _ = User.objects.get_or_create(
            email="<EMAIL>",
            defaults={
                "first_name": "Apple Tester",
                "last_name": "Testing",
                "role": "customer",
                "phone_number": f"+97119999999",
                "gender": "male",
            },
        )
        user.is_active = True
        user.save()

        user, _ = User.objects.get_or_create(
            email="<EMAIL>",
            defaults={
                "first_name": "Google Tester",
                "last_name": "Testing",
                "role": "customer",
                "phone_number": f"+97119999998",
                "gender": "male",
            },
        )
        user.is_active = True
        user.save()

        self.stdout.write(self.style.SUCCESS("All specified records have been created."))
