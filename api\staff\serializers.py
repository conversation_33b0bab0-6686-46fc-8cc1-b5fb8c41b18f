from rest_framework import serializers

from api.appointments.models import Activity
from django.contrib.auth import get_user_model
from .models import TherapistProfile, WorkingHour
from api.services.serializers import ServiceSerializer, ServicePackageViewSerializer
from api.services.models import Service, ServicePackage

User = get_user_model()


class UserCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating a user account for a therapist"""

    class Meta:
        model = User
        fields = [
            "email",
            "phone_number",
            "first_name",
            "last_name",
            "gender",
        ]


class TherapistProfileCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating a therapist profile with user account"""

    user = UserCreateSerializer()
    services = serializers.PrimaryKeyRelatedField(
        queryset=Service.objects.all(), many=True, required=False
    )
    eligible_packages = serializers.PrimaryKeyRelatedField(
        queryset=ServicePackage.objects.all(), many=True, required=False
    )

    class Meta:
        model = TherapistProfile
        fields = [
            "id",
            "user",
            "qualifications",
            "start_year",
            "gender_preference",
            "location",
            "services",
            "eligible_packages",
            "is_freelancer",
            "freelancer_service_rate",
            "freelancer_package_rate",
        ]

    def create(self, validated_data):
        user_data = validated_data.pop("user")
        services_data = validated_data.pop("services", [])
        packages_data = validated_data.pop("eligible_packages", [])

        # Set role to therapist
        user_data["role"] = "therapist"

        # Create user
        user = User.objects.create(**user_data)

        # Create therapist profile
        therapist = TherapistProfile.objects.create(user=user, **validated_data)

        # Add services and packages
        if services_data:
            therapist.services.set(services_data)
        if packages_data:
            therapist.eligible_packages.set(packages_data)

        return therapist


class TherapistProfileUpdateSerializer(serializers.ModelSerializer):
    """Serializer for updating a therapist profile"""

    # Create a nested UserSerializer specifically for the update operation
    class UserSerializer(serializers.ModelSerializer):
        class Meta:
            model = User  # Make sure to import the User model
            fields = ["first_name", "last_name", "gender", "email", "phone_number"]
            read_only_fields = [
                "email",
                "phone_number",
            ]  # Make these read-only if you can't update them

    # Keep the original char fields for reading/initializing form data
    first_name = serializers.CharField(source="user.first_name", required=False)
    last_name = serializers.CharField(source="user.last_name", required=False)
    gender = serializers.CharField(source="user.gender", required=False)

    # Add the nested serializer for updates
    user = UserSerializer(required=False, write_only=True)

    services = serializers.PrimaryKeyRelatedField(
        queryset=Service.objects.all(), many=True, required=False
    )
    eligible_packages = serializers.PrimaryKeyRelatedField(
        queryset=ServicePackage.objects.all(), many=True, required=False
    )

    class Meta:
        model = TherapistProfile
        fields = [
            "first_name",
            "last_name",
            "gender",
            "user",  # Include the nested user field (write-only)
            "qualifications",
            "start_year",
            "gender_preference",
            "location",
            "services",
            "eligible_packages",
            "is_freelancer",
            "freelancer_service_rate",
            "freelancer_package_rate",
        ]

    def update(self, instance, validated_data):
        # Handle the nested user data
        if "user" in validated_data:
            user_data = validated_data.pop("user")
            user = instance.user

            # Update user fields with the nested data
            for attr, value in user_data.items():
                if hasattr(user, attr):  # Make sure the attribute exists
                    setattr(user, attr, value)
            user.save()

        # The rest of the method remains the same
        if "services" in validated_data:
            services = validated_data.pop("services")
            instance.services.set(services)

        if "eligible_packages" in validated_data:
            packages = validated_data.pop("eligible_packages")
            instance.eligible_packages.set(packages)

        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        return instance


class WorkingHourCreateUpdateSerializer(serializers.ModelSerializer):
    """Serializer for creating and updating working hours"""

    class Meta:
        model = WorkingHour
        fields = ["id", "day", "start_time", "end_time", "therapist"]
        read_only_fields = ["id"]


class WorkingHourSerializer(serializers.ModelSerializer):
    class Meta:
        model = WorkingHour
        fields = ["id", "day", "start_time", "end_time"]


class TherapistProfileSerializer(serializers.ModelSerializer):
    user = serializers.StringRelatedField(read_only=True)
    first_name = serializers.CharField(source="user.first_name", read_only=True)
    last_name = serializers.CharField(source="user.last_name", read_only=True)
    email = serializers.CharField(source="user.email", read_only=True)
    phone_number = serializers.CharField(source="user.phone_number", read_only=True)
    working_hours = WorkingHourSerializer(many=True, read_only=True)
    full_name = serializers.SerializerMethodField()
    services = ServiceSerializer(many=True, read_only=True)
    eligible_packages = ServicePackageViewSerializer(many=True, read_only=True)
    gender = serializers.CharField(source="user.gender", required=False)

    class Meta:
        model = TherapistProfile
        fields = [
            "id",
            "user",
            "email",
            "phone_number",
            "first_name",
            "last_name",
            "full_name",
            "gender",
            "qualifications",
            "start_year",
            "gender_preference",
            "location",
            "services",
            "eligible_packages",
            "working_hours",
            "is_freelancer",
            "freelancer_service_rate",
            "freelancer_package_rate",
        ]

    def get_full_name(self, obj):
        """Get the full name of the therapist"""
        return f"{obj.user.get_full_name()}"


class TherapistProfileAppointmentSerializer(serializers.ModelSerializer):
    user = serializers.StringRelatedField(read_only=True)
    first_name = serializers.CharField(source="user.first_name", read_only=True)
    last_name = serializers.CharField(source="user.last_name", read_only=True)
    email = serializers.CharField(source="user.email", read_only=True)
    phone_number = serializers.CharField(source="user.phone_number", read_only=True)
    full_name = serializers.SerializerMethodField()
    gender = serializers.CharField(source="user.gender", required=False)

    class Meta:
        model = TherapistProfile
        fields = [
            "id",
            "user",
            "email",
            "phone_number",
            "first_name",
            "last_name",
            "full_name",
            "gender",
            "qualifications",
            "start_year",
            "gender_preference",
            "location",
        ]

    def get_full_name(self, obj):
        """Get the full name of the therapist"""
        return f"{obj.user.get_full_name()}"


class PublicTherapistProfileSerializer(serializers.ModelSerializer):
    user = serializers.StringRelatedField(read_only=True)
    first_name = serializers.CharField(source="user.first_name", read_only=True)
    last_name = serializers.CharField(source="user.last_name", read_only=True)
    full_name = serializers.SerializerMethodField()
    services = ServiceSerializer(many=True, read_only=True)
    eligible_packages = ServicePackageViewSerializer(many=True, read_only=True)
    gender = serializers.CharField(source="user.gender", required=False)

    class Meta:
        model = TherapistProfile
        fields = [
            "id",
            "user",
            "qualifications",
            "first_name",
            "last_name",
            "full_name",
            "gender",
            "start_year",
            "gender_preference",
            "location",
            "services",
            "eligible_packages",
        ]

    def get_full_name(self, obj):
        """Get the full name of the therapist"""
        return f"{obj.user.get_full_name()}"


class DashboardActivitySerializer(serializers.ModelSerializer):
    staff = serializers.SerializerMethodField()
    customer = serializers.SerializerMethodField()

    def get_staff(self, instance):
        if instance.staff:
            return {
                "id": instance.staff.id,
                "first_name": instance.staff.first_name,
                "last_name": instance.staff.last_name,
            }

    def get_customer(self, instance):
        return {
            "id": instance.customer.id,
            "first_name": instance.customer.first_name,
            "last_name": instance.customer.last_name,
        }

    class Meta:
        model = Activity
        fields = ["id", "staff", "customer", "activity_type", "description", "created_at"]


class DashboardCustomerSerializer(serializers.ModelSerializer):
    full_name = serializers.SerializerMethodField()
    profile_picture = serializers.SerializerMethodField()
    appointments = serializers.SerializerMethodField()
    revenue = serializers.SerializerMethodField()

    def get_full_name(self, instance):
        return f"{instance.first_name} {instance.last_name}"

    def get_profile_picture(self, instance):
        if instance.profile_picture:
            return instance.profile_picture.url
        return None

    def get_appointments(self, instance):
        return instance.appointments.count()

    def get_revenue(self, instance):
        total_revenue = 0
        for app in instance.appointments.all():
            if app.payment_method:
                total_revenue += app.total_price
        return total_revenue

    class Meta:
        model = User
        fields = [
            "id",
            "full_name",
            "profile_picture",
            "appointments",
            "revenue",
        ]


class TherapistProfileOptimizedSerializer(serializers.ModelSerializer):
    """
    Optimized serializer for therapist profiles.
    Reduces nested data and prevents duplications.
    """

    user_id = serializers.IntegerField(source="user.id", read_only=True)
    email = serializers.EmailField(source="user.email", read_only=True)
    first_name = serializers.CharField(source="user.first_name", read_only=True)
    last_name = serializers.CharField(source="user.last_name", read_only=True)
    full_name = serializers.SerializerMethodField()
    gender = serializers.CharField(source="user.gender", required=False)
    working_hours = WorkingHourSerializer(many=True, read_only=True)

    # Include only essential service fields to reduce payload size
    services = serializers.SerializerMethodField()

    class Meta:
        model = TherapistProfile
        fields = [
            "id",
            "user_id",
            "email",
            "first_name",
            "last_name",
            "full_name",
            "gender",
            "qualifications",
            "start_year",
            "gender_preference",
            "location",
            "services",
            "working_hours",
        ]

    def get_full_name(self, obj):
        """Get the full name of the therapist"""
        return f"{obj.user.get_full_name()}"

    def get_services(self, obj):
        """
        Get simplified service information, avoiding deep nesting
        """
        services = []
        for service in obj.services.all():
            service_data = {
                "id": service.id,
                "name": service.name,
                "description": service.description,
                "durations": [
                    {
                        "id": duration.id,
                        "time": duration.time,
                        "price": str(duration.price),
                    }
                    for duration in service.durations.all()
                ],
            }
            services.append(service_data)
        return services
