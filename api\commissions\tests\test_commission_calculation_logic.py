"""
Tests for Commission Calculation Logic.
Covers test cases for commission calculation requirements verification.

This test file specifically tests the commission calculation requirements:
1. Commission rules are only applied when total sales exceed minimum threshold
2. Get rule with highest priority
3. Prioritize percentage commission over fixed amount
4. Add 5% VAT to percentage commissions
5. Use fixed amount if no percentage is set
6. Calculate zero commission if neither is set
"""

import pytest
from decimal import Decimal
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import datetime, date

from api.staff.models import TherapistProfile
from api.services.models import Service, ServicePackage
from api.appointments.models import Sale, Appointment, AppointmentService
from api.commissions.models import (
    CommissionProfile, CommissionRule, CommissionEarning, TherapistMonthStats
)
from api.commissions.services import CommissionCalculator

User = get_user_model()


@pytest.fixture
def owner_user(db):
    """Create an owner user for testing"""
    user = User.objects.create_user(
        email="<EMAIL>",
        phone_number="+971501234567",
        first_name="Owner",
        last_name="User",
        role="owner",
        is_staff=True,
    )
    user.set_password("testpass123")
    user.save()
    return user


@pytest.fixture
def therapist_user(db):
    """Create a therapist user for testing"""
    user = User.objects.create_user(
        email="<EMAIL>",
        phone_number="+971501234569",
        first_name="Therapist",
        last_name="User",
        role="therapist",
    )
    user.set_password("testpass123")
    user.save()
    return user


@pytest.fixture
def therapist_profile(therapist_user):
    """Create a therapist profile for testing"""
    return TherapistProfile.objects.create(
        user=therapist_user,
        qualifications="Test qualifications",
        start_year=2020,
        location="A"
    )


@pytest.fixture
def commission_profile(therapist_profile):
    """Create a commission profile for testing"""
    return CommissionProfile.objects.create(
        therapist=therapist_profile,
        name="Test Commission Profile",
        base_percentage=Decimal("15.00"),
        sessions_threshold=10,
        is_active=True,
        is_default=True
    )


@pytest.fixture
def service(db):
    """Create a service for testing"""
    return Service.objects.create(
        name="Test Service",
        description="Test service description",
        is_public=True
    )


@pytest.fixture
def package(db):
    """Create a service package for testing"""
    return ServicePackage.objects.create(
        name="Test Package",
        description="Test package description",
        benefits=["Benefit 1", "Benefit 2"],
        is_public=True
    )


@pytest.fixture
def appointment(therapist_user, therapist_profile, owner_user):
    """Create an appointment for testing"""
    return Appointment.objects.create(
        customer=owner_user,  # Using owner as customer for simplicity
        therapist=therapist_profile,
        date=timezone.now().date(),
        time=timezone.now().time(),
        total_duration=60,
        total_price=Decimal("100.00"),
        status='booked',
        location='A',
        payment_method='cash'
    )


@pytest.fixture
def sale(appointment, owner_user):
    """Create a sale for testing"""
    return Sale.objects.create(
        user=owner_user,
        appointment=appointment,
        total_price=Decimal("100.00"),
        sale_type='service',
        payment_method='cash'
    )


class TestCommissionCalculationThresholds:
    """Test commission calculation threshold requirements"""

    @pytest.mark.django_db
    def test_commission_not_applied_below_profile_threshold(self, commission_profile, therapist_profile):
        """Test that commission is not applied when total sessions are below profile threshold"""
        # Set profile threshold to 10 sessions
        commission_profile.sessions_threshold = 10
        commission_profile.save()

        # Create a mock sale
        current_date = timezone.now()

        # Create month stats with only 5 sessions (below threshold)
        month_stats = TherapistMonthStats.objects.create(
            therapist=therapist_profile,
            year=current_date.year,
            month=current_date.month,
            total_sessions=5,
            total_earnings=Decimal("0.00")
        )

        # Test the logic directly without creating actual sale
        # This tests the threshold checking logic
        assert month_stats.total_sessions < commission_profile.sessions_threshold
        assert month_stats.total_sessions == 5
        assert commission_profile.sessions_threshold == 10

    @pytest.mark.django_db
    def test_commission_applied_above_profile_threshold(self, commission_profile, therapist_profile):
        """Test that commission is applied when total sessions meet profile threshold"""
        # Set profile threshold to 10 sessions
        commission_profile.sessions_threshold = 10
        commission_profile.save()

        # Create a mock sale
        current_date = timezone.now()

        # Create month stats with 15 sessions (above threshold)
        month_stats = TherapistMonthStats.objects.create(
            therapist=therapist_profile,
            year=current_date.year,
            month=current_date.month,
            total_sessions=15,
            total_earnings=Decimal("0.00")
        )

        # Test the logic directly
        assert month_stats.total_sessions >= commission_profile.sessions_threshold
        assert month_stats.total_sessions == 15
        assert commission_profile.sessions_threshold == 10

    @pytest.mark.django_db
    def test_commission_not_applied_below_rule_threshold(self, commission_profile, therapist_profile):
        """Test that commission is not applied when total sessions are below rule threshold"""
        # Create rule with higher threshold than profile
        rule = CommissionRule.objects.create(
            profile=commission_profile,
            name="High Threshold Rule",
            rule_type="global",
            percentage=Decimal("20.00"),
            min_sessions=20,  # Higher than profile threshold
            priority=1,
            is_active=True
        )

        # Create a mock sale
        current_date = timezone.now()

        # Create month stats with 15 sessions (above profile threshold but below rule threshold)
        month_stats = TherapistMonthStats.objects.create(
            therapist=therapist_profile,
            year=current_date.year,
            month=current_date.month,
            total_sessions=15,
            total_earnings=Decimal("0.00")
        )

        # Test the logic directly
        assert month_stats.total_sessions >= commission_profile.sessions_threshold  # Profile threshold met
        assert month_stats.total_sessions < rule.min_sessions  # Rule threshold not met
        assert month_stats.total_sessions == 15
        assert rule.min_sessions == 20

    @pytest.mark.django_db
    def test_commission_applied_above_rule_threshold(self, commission_profile, therapist_profile):
        """Test that commission is applied when total sessions meet rule threshold"""
        # Create rule with specific threshold
        rule = CommissionRule.objects.create(
            profile=commission_profile,
            name="Rule with Threshold",
            rule_type="global",
            percentage=Decimal("20.00"),
            min_sessions=15,
            priority=1,
            is_active=True
        )

        # Create a mock sale
        current_date = timezone.now()

        # Create month stats with 20 sessions (above rule threshold)
        month_stats = TherapistMonthStats.objects.create(
            therapist=therapist_profile,
            year=current_date.year,
            month=current_date.month,
            total_sessions=20,
            total_earnings=Decimal("0.00")
        )

        # Test the logic directly
        assert month_stats.total_sessions >= rule.min_sessions
        assert month_stats.total_sessions == 20
        assert rule.min_sessions == 15


class TestCommissionCalculationPriority:
    """Test commission calculation priority logic"""

    @pytest.mark.django_db
    def test_highest_priority_rule_selected(self, commission_profile):
        """Test that rule with highest priority is selected"""
        # Create multiple rules with different priorities
        rule_low = CommissionRule.objects.create(
            profile=commission_profile,
            name="Low Priority Rule",
            rule_type="global",
            percentage=Decimal("10.00"),
            min_sessions=5,
            priority=1,  # Lower priority
            is_active=True
        )

        rule_high = CommissionRule.objects.create(
            profile=commission_profile,
            name="High Priority Rule",
            rule_type="global",
            percentage=Decimal("25.00"),
            min_sessions=5,
            priority=5,  # Higher priority
            is_active=True
        )

        # Test priority ordering
        rules = CommissionRule.objects.filter(
            profile=commission_profile,
            is_active=True
        ).order_by('-priority')  # Highest priority first

        assert rules.first() == rule_high
        assert rules.first().priority == 5
        assert rules.last() == rule_low
        assert rules.last().priority == 1

    @pytest.mark.django_db
    def test_inactive_rules_ignored_in_priority_selection(self, commission_profile):
        """Test that inactive rules are ignored in priority selection"""
        # Create active rule with lower priority
        active_rule = CommissionRule.objects.create(
            profile=commission_profile,
            name="Active Rule",
            rule_type="global",
            percentage=Decimal("15.00"),
            min_sessions=10,
            priority=2,
            is_active=True
        )

        # Create inactive rule with higher priority
        inactive_rule = CommissionRule.objects.create(
            profile=commission_profile,
            name="Inactive Rule",
            rule_type="global",
            percentage=Decimal("25.00"),
            min_sessions=10,
            priority=5,
            is_active=False  # Inactive
        )

        # Test that only active rules are considered
        active_rules = CommissionRule.objects.filter(
            profile=commission_profile,
            is_active=True
        ).order_by('-priority')

        assert active_rules.count() == 1
        assert active_rules.first() == active_rule
        assert inactive_rule not in active_rules


class TestCommissionCalculationPercentageVsFixed:
    """Test percentage vs fixed amount priority logic"""

    @pytest.mark.django_db
    def test_percentage_prioritized_over_fixed_amount(self, commission_profile):
        """Test that percentage commission takes priority over fixed amount in same rule"""
        # Create rule with both percentage and fixed amount
        rule = CommissionRule.objects.create(
            profile=commission_profile,
            name="Mixed Rule",
            rule_type="global",
            percentage=Decimal("20.00"),  # Should be used
            fixed_amount=Decimal("50.00"),  # Should be ignored
            min_sessions=5,
            priority=1,
            is_active=True
        )

        # Test the priority logic directly
        assert rule.percentage is not None
        assert rule.fixed_amount is not None
        assert rule.percentage > 0
        assert rule.fixed_amount > 0

        # In the calculation logic, percentage should take priority
        # This tests the business rule: "if percentage is set, use it; else use fixed amount"

    @pytest.mark.django_db
    def test_fixed_amount_used_when_no_percentage(self, commission_profile):
        """Test that fixed amount is used when no percentage is set"""
        # Create rule with only fixed amount
        rule = CommissionRule.objects.create(
            profile=commission_profile,
            name="Fixed Amount Rule",
            rule_type="global",
            percentage=None,  # No percentage
            fixed_amount=Decimal("30.00"),  # Should be used
            min_sessions=5,
            priority=1,
            is_active=True
        )

        # Test the logic directly
        assert rule.percentage is None
        assert rule.fixed_amount is not None
        assert rule.fixed_amount == Decimal("30.00")

    @pytest.mark.django_db
    def test_zero_commission_when_neither_set(self, commission_profile):
        """Test that zero commission is calculated when neither percentage nor fixed amount is set"""
        # Create rule with neither percentage nor fixed amount
        rule = CommissionRule.objects.create(
            profile=commission_profile,
            name="Empty Rule",
            rule_type="global",
            percentage=None,
            fixed_amount=None,
            min_sessions=5,
            priority=1,
            is_active=True
        )

        # Test the logic directly
        assert rule.percentage is None
        assert rule.fixed_amount is None

        # In the calculation logic, this should result in zero commission

    @pytest.mark.django_db
    def test_zero_percentage_treated_as_no_percentage(self, commission_profile):
        """Test that 0% percentage is treated as no percentage set"""
        # Create rule with 0% percentage
        rule = CommissionRule.objects.create(
            profile=commission_profile,
            name="Zero Percentage Rule",
            rule_type="global",
            percentage=Decimal("0.00"),
            fixed_amount=Decimal("25.00"),
            min_sessions=5,
            priority=1,
            is_active=True
        )

        # Test the logic - 0% should be treated as "no percentage"
        # so fixed amount should be used instead
        assert rule.percentage == Decimal("0.00")
        assert rule.fixed_amount == Decimal("25.00")

        # In calculation logic: if percentage <= 0, use fixed amount


class TestCommissionCalculationVAT:
    """Test VAT calculation logic"""

    @pytest.mark.django_db
    def test_vat_addition_calculation(self):
        """Test that 5% VAT is correctly calculated"""
        # Test VAT addition method directly
        base_amount = Decimal("20.00")
        vat_rate = Decimal("0.05")
        expected_vat = base_amount * vat_rate
        expected_total = base_amount + expected_vat

        # Test the calculation
        actual_total = CommissionCalculator._apply_vat_addition(base_amount)

        assert actual_total == expected_total
        assert actual_total == Decimal("21.00")

    @pytest.mark.django_db
    def test_vat_addition_with_different_amounts(self):
        """Test VAT addition with various amounts"""
        test_cases = [
            (Decimal("10.00"), Decimal("10.50")),  # 10 + 5% = 10.50
            (Decimal("100.00"), Decimal("105.00")),  # 100 + 5% = 105.00
            (Decimal("33.33"), Decimal("35.00")),  # 33.33 + 5% = 34.9965, quantized to 35.00
        ]

        for base_amount, expected_total in test_cases:
            actual_total = CommissionCalculator._apply_vat_addition(base_amount)
            assert actual_total == expected_total

    @pytest.mark.django_db
    def test_vat_deduction_calculation(self):
        """Test that 5% VAT deduction is correctly calculated (legacy method)"""
        # Test VAT deduction method
        base_amount = Decimal("21.00")
        vat_rate = Decimal("0.05")
        expected_vat = base_amount * vat_rate
        expected_total = base_amount - expected_vat

        # Test the calculation
        actual_total = CommissionCalculator._apply_vat_deduction(base_amount)

        assert actual_total == expected_total
        assert actual_total == Decimal("19.95")

    @pytest.mark.django_db
    def test_percentage_commission_should_use_vat_addition(self, commission_profile):
        """Test that percentage-based commissions should use VAT addition"""
        # Create rule with percentage
        rule = CommissionRule.objects.create(
            profile=commission_profile,
            name="Percentage Rule",
            rule_type="global",
            percentage=Decimal("20.00"),
            min_sessions=5,
            priority=1,
            is_active=True
        )

        # Test that the rule is set up for percentage commission
        assert rule.percentage is not None
        assert rule.percentage > 0
        assert rule.fixed_amount is None

        # In the calculation logic, this should use VAT addition

    @pytest.mark.django_db
    def test_fixed_amount_commission_should_not_use_vat(self, commission_profile):
        """Test that fixed amount commissions should not use VAT"""
        # Create rule with fixed amount
        rule = CommissionRule.objects.create(
            profile=commission_profile,
            name="Fixed Amount Rule",
            rule_type="global",
            fixed_amount=Decimal("25.00"),
            min_sessions=5,
            priority=1,
            is_active=True
        )

        # Test that the rule is set up for fixed amount commission
        assert rule.percentage is None
        assert rule.fixed_amount is not None
        assert rule.fixed_amount == Decimal("25.00")

        # In the calculation logic, this should NOT use VAT addition


class TestCommissionCalculationFallback:
    """Test fallback to profile base percentage"""

    @pytest.mark.django_db
    def test_fallback_to_profile_base_percentage(self, commission_profile):
        """Test that profile base percentage is used when no rules apply"""
        # No commission rules created - should fall back to profile base percentage
        # Profile has base_percentage=15.00 from fixture

        # Test the logic directly
        assert commission_profile.base_percentage == Decimal("15.00")

        # Get active rules for this profile
        active_rules = CommissionRule.objects.filter(
            profile=commission_profile,
            is_active=True
        )

        # Should have no active rules, so fallback to base percentage
        assert active_rules.count() == 0

    @pytest.mark.django_db
    def test_no_commission_when_no_rules_and_no_base_percentage(self, commission_profile):
        """Test that no commission is calculated when no rules and no base percentage"""
        # Set base percentage to 0
        commission_profile.base_percentage = Decimal("0.00")
        commission_profile.save()

        # Test the logic directly
        assert commission_profile.base_percentage == Decimal("0.00")

        # Get active rules for this profile
        active_rules = CommissionRule.objects.filter(
            profile=commission_profile,
            is_active=True
        )

        # Should have no active rules and no base percentage
        assert active_rules.count() == 0
        assert commission_profile.base_percentage == Decimal("0.00")


class TestCommissionCalculationEdgeCases:
    """Test edge cases and special scenarios"""

    @pytest.mark.django_db
    def test_zero_percentage_rule_logic(self, commission_profile):
        """Test that rule with 0% percentage should result in no commission"""
        # Create rule with 0% percentage
        rule = CommissionRule.objects.create(
            profile=commission_profile,
            name="Zero Percentage Rule",
            rule_type="global",
            percentage=Decimal("0.00"),
            min_sessions=5,
            priority=1,
            is_active=True
        )

        # Test the logic - 0% should be treated as no commission
        assert rule.percentage == Decimal("0.00")
        assert rule.percentage <= 0  # This condition should trigger "no commission"

    @pytest.mark.django_db
    def test_zero_fixed_amount_rule_logic(self, commission_profile):
        """Test that rule with 0 fixed amount should result in no commission"""
        # Create rule with 0 fixed amount
        rule = CommissionRule.objects.create(
            profile=commission_profile,
            name="Zero Fixed Amount Rule",
            rule_type="global",
            fixed_amount=Decimal("0.00"),
            min_sessions=5,
            priority=1,
            is_active=True
        )

        # Test the logic - 0 fixed amount should result in no commission
        assert rule.fixed_amount == Decimal("0.00")
        assert rule.fixed_amount <= 0  # This condition should trigger "no commission"

    @pytest.mark.django_db
    def test_payment_method_fee_calculations(self):
        """Test payment method fee calculations"""
        sale_amount = Decimal("100.00")

        # Card payment fee: 2.1%
        card_fee_rate = Decimal("0.021")
        expected_card_fee = (sale_amount * card_fee_rate).quantize(Decimal('0.01'))
        assert expected_card_fee == Decimal("2.10")

        # Link payment fee: 3%
        link_fee_rate = Decimal("0.03")
        expected_link_fee = (sale_amount * link_fee_rate).quantize(Decimal('0.01'))
        assert expected_link_fee == Decimal("3.00")

    @pytest.mark.django_db
    def test_commission_amount_not_negative_logic(self):
        """Test that commission amount should not go negative"""
        # Test the logic for ensuring non-negative commission
        commission_amount = Decimal("-5.00")  # Negative after fees

        # Logic: if commission_amount < 0, set to 0
        if commission_amount < 0:
            commission_amount = Decimal("0.00")

        assert commission_amount == Decimal("0.00")
        assert commission_amount >= 0


class TestCommissionCalculationBusinessRules:
    """Test business rules for commission calculation"""

    @pytest.mark.django_db
    def test_commission_calculation_requirements_summary(self, commission_profile):
        """Test that all commission calculation requirements are properly structured"""

        # Requirement 1: Commission rules only applied when total sales exceed minimum threshold
        # This is tested in TestCommissionCalculationThresholds

        # Requirement 2: Get rule with highest priority
        # Create multiple rules to test priority ordering
        rule_low = CommissionRule.objects.create(
            profile=commission_profile,
            name="Low Priority",
            rule_type="global",
            percentage=Decimal("10.00"),
            priority=1,
            is_active=True
        )

        rule_high = CommissionRule.objects.create(
            profile=commission_profile,
            name="High Priority",
            rule_type="global",
            percentage=Decimal("20.00"),
            priority=5,
            is_active=True
        )

        # Test priority ordering
        highest_priority_rule = CommissionRule.objects.filter(
            profile=commission_profile,
            is_active=True
        ).order_by('-priority').first()

        assert highest_priority_rule == rule_high
        assert highest_priority_rule.priority == 5

        # Requirement 3: Prioritize percentage commission over fixed amount
        mixed_rule = CommissionRule.objects.create(
            profile=commission_profile,
            name="Mixed Rule",
            rule_type="global",
            percentage=Decimal("15.00"),  # Should take priority
            fixed_amount=Decimal("50.00"),  # Should be ignored
            priority=3,
            is_active=True
        )

        assert mixed_rule.percentage is not None
        assert mixed_rule.fixed_amount is not None
        # In calculation logic: if percentage > 0, use percentage; else use fixed_amount

        # Requirement 4: Add 5% VAT to percentage commissions
        base_commission = Decimal("20.00")
        vat_added = CommissionCalculator._apply_vat_addition(base_commission)
        expected_with_vat = Decimal("21.00")  # 20 + (20 * 5%) = 21
        assert vat_added == expected_with_vat

        # Requirement 5: Use fixed amount if no percentage is set
        fixed_only_rule = CommissionRule.objects.create(
            profile=commission_profile,
            name="Fixed Only Rule",
            rule_type="global",
            percentage=None,
            fixed_amount=Decimal("30.00"),
            priority=2,
            is_active=True
        )

        assert fixed_only_rule.percentage is None
        assert fixed_only_rule.fixed_amount == Decimal("30.00")

        # Requirement 6: Calculate zero commission if neither is set
        empty_rule = CommissionRule.objects.create(
            profile=commission_profile,
            name="Empty Rule",
            rule_type="global",
            percentage=None,
            fixed_amount=None,
            priority=1,
            is_active=True
        )

        assert empty_rule.percentage is None
        assert empty_rule.fixed_amount is None
        # In calculation logic: this should result in zero commission


class TestOmarSpecialCommissionStructure:
    """Test Omar's special commission structure for expert sessions and home services"""

    @pytest.fixture
    def omar_user(self, db):
        """Create Omar user (ID=1)"""
        user = User.objects.create_user(
            email="<EMAIL>",
            phone_number="+971501234567",
            first_name="Omar",
            last_name="Owner",
            role="owner",
            is_staff=True,
        )
        user.set_password("testpass123")
        user.save()
        # Ensure Omar has ID=1 for the commission logic
        if user.id != 1:
            # Update the user ID to 1 if needed (for testing purposes)
            user.id = 1
            user.save()
        return user

    @pytest.fixture
    def basil_user(self, db):
        """Create Basil therapist user"""
        user = User.objects.create_user(
            email="<EMAIL>",
            phone_number="+971501234568",
            first_name="Basil",
            last_name="Therapist",
            role="therapist",
        )
        user.set_password("testpass123")
        user.save()
        return user

    @pytest.fixture
    def expert_service(self, db):
        """Create an expert service"""
        return Service.objects.create(
            name="Expert Stretching Session",
            description="Expert level stretching session",
            is_public=True
        )

    @pytest.fixture
    def expert_package(self, db):
        """Create an expert package"""
        return ServicePackage.objects.create(
            name="Expert Package",
            description="Expert level package",
            benefits=["Expert sessions", "Advanced techniques"],
            is_public=True
        )

    @pytest.mark.django_db
    def test_omar_expert_session_commission_logic(self, expert_service):
        """Test that Omar gets 30 AED for every expert session"""
        # Test the logic for detecting expert services
        # This would be used in _is_expert_service method
        service_name = expert_service.name.lower()
        assert 'expert' in service_name

        # Test commission amount for expert sessions
        expected_commission = Decimal("30.00")
        assert expected_commission == Decimal("30.00")

    @pytest.mark.django_db
    def test_omar_expert_package_commission_logic(self, expert_package):
        """Test that Omar gets 50 AED for every expert package"""
        # Test the logic for detecting expert packages
        package_name = expert_package.name.lower()
        assert 'expert' in package_name

        # Test commission amount for expert packages
        expected_commission = Decimal("50.00")
        assert expected_commission == Decimal("50.00")

    @pytest.mark.django_db
    def test_omar_home_session_commission_logic(self):
        """Test that Omar gets 50% for ALL home sessions regardless of who performs them"""
        # Test home service detection logic
        location = 'home'
        assert location == 'home'

        # Test commission calculation for home sessions
        sale_price = Decimal("100.00")
        commission_rate = Decimal("0.5")  # 50%
        base_commission = (sale_price * commission_rate).quantize(Decimal('0.01'))

        # Add 5% VAT
        vat_rate = Decimal("0.05")
        vat_amount = (base_commission * vat_rate).quantize(Decimal('0.01'))
        expected_commission = base_commission + vat_amount

        assert base_commission == Decimal("50.00")
        assert expected_commission == Decimal("52.50")  # 50 + 2.5 VAT

    @pytest.mark.django_db
    def test_omar_home_package_commission_logic(self):
        """Test that Omar gets 40% for ALL home packages regardless of who performs them"""
        # Test commission calculation for home packages
        sale_price = Decimal("1000.00")
        commission_rate = Decimal("0.4")  # 40%
        base_commission = (sale_price * commission_rate).quantize(Decimal('0.01'))

        # Add 5% VAT
        vat_rate = Decimal("0.05")
        vat_amount = (base_commission * vat_rate).quantize(Decimal('0.01'))
        expected_commission = base_commission + vat_amount

        assert base_commission == Decimal("400.00")
        assert expected_commission == Decimal("420.00")  # 400 + 20 VAT

    @pytest.mark.django_db
    def test_expert_service_detection_methods(self):
        """Test the helper methods for detecting expert services"""
        # Test _is_expert_service logic
        service_names = [
            "Expert Stretching Session",
            "Advanced Expert Therapy",
            "Regular Stretching Session",
            "Basic Massage"
        ]

        expert_services = [name for name in service_names if 'expert' in name.lower()]
        non_expert_services = [name for name in service_names if 'expert' not in name.lower()]

        assert len(expert_services) == 2
        assert len(non_expert_services) == 2
        assert "Expert Stretching Session" in expert_services
        assert "Regular Stretching Session" in non_expert_services

    @pytest.mark.django_db
    def test_home_service_detection_methods(self):
        """Test the helper methods for detecting home services"""
        # Test _is_home_service logic
        locations = ['home', 'clinic', 'A', 'B']

        home_locations = [loc for loc in locations if loc == 'home']
        clinic_locations = [loc for loc in locations if loc != 'home']

        assert len(home_locations) == 1
        assert len(clinic_locations) == 3
        assert 'home' in home_locations

    @pytest.mark.django_db
    def test_commission_type_classification(self):
        """Test commission type classification for Omar's commissions"""
        # Test commission types
        omar_expert_type = 'omar_expert'
        omar_home_type = 'omar_home'
        standard_type = 'standard'

        assert omar_expert_type == 'omar_expert'
        assert omar_home_type == 'omar_home'
        assert standard_type == 'standard'

        # Test is_primary flag logic
        # Expert commissions are secondary (Omar gets them in addition to therapist's commission)
        expert_is_primary = False
        # Home commissions are secondary when Omar is not the therapist, primary when Omar is the therapist
        home_is_primary_when_omar_therapist = True
        home_is_primary_when_other_therapist = False  # Omar gets secondary commission

        assert expert_is_primary == False
        assert home_is_primary_when_omar_therapist == True
        assert home_is_primary_when_other_therapist == False

    @pytest.mark.django_db
    def test_home_service_dual_commission_logic(self):
        """Test that home services can generate dual commissions when therapist is not Omar"""
        # Scenario: Basil performs home session, both Omar and Basil should get commission
        therapist = 'Basil'
        location = 'home'
        sale_price = Decimal("200.00")

        # Omar gets his home commission (50% + VAT)
        omar_commission_rate = Decimal("0.5")
        omar_base_commission = (sale_price * omar_commission_rate).quantize(Decimal('0.01'))
        omar_vat = (omar_base_commission * Decimal("0.05")).quantize(Decimal('0.01'))
        omar_total_commission = omar_base_commission + omar_vat

        # Basil gets standard commission (if he meets threshold)
        # This would be calculated based on his commission profile rules

        assert omar_base_commission == Decimal("100.00")
        assert omar_total_commission == Decimal("105.00")  # 100 + 5 VAT

        # Both commissions should be created for home services when therapist != Omar

    @pytest.mark.django_db
    def test_home_service_single_commission_logic(self):
        """Test that home services generate only Omar's commission when Omar is the therapist"""
        # Scenario: Omar performs home session, only Omar should get commission
        therapist = 'Omar'
        location = 'home'
        sale_price = Decimal("150.00")

        # Only Omar gets commission (50% + VAT)
        omar_commission_rate = Decimal("0.5")
        omar_base_commission = (sale_price * omar_commission_rate).quantize(Decimal('0.01'))
        omar_vat = (omar_base_commission * Decimal("0.05")).quantize(Decimal('0.01'))
        omar_total_commission = omar_base_commission + omar_vat

        assert omar_base_commission == Decimal("75.00")
        assert omar_total_commission == Decimal("78.75")  # 75 + 3.75 VAT

        # No additional therapist commission should be created when Omar is the therapist


class TestOmarCommissionExamples:
    """Test the specific examples mentioned in the requirements"""

    @pytest.mark.django_db
    def test_basil_expert_session_with_mustafa_omar_gets_30_aed(self):
        """
        Example: Basil is doing Expert Session with Mustafa (client) - Omar will take 30 AED
        """
        # Test the business logic for this scenario
        sale_type = 'service'
        service_name = 'Expert Stretching Session'
        therapist = 'Basil'
        client = 'Mustafa'

        # Omar should get commission regardless of who performs the expert session
        is_expert_service = 'expert' in service_name.lower()
        assert is_expert_service == True

        if is_expert_service and sale_type == 'service':
            omar_commission = Decimal("30.00")
        else:
            omar_commission = Decimal("0.00")

        assert omar_commission == Decimal("30.00")

    @pytest.mark.django_db
    def test_mustafa_buys_expert_package_omar_gets_50_aed(self):
        """
        Example: Mustafa (Client) buys one expert package in Al Mizhar/Al Warqa - Omar will take 50 AED
        """
        # Test the business logic for this scenario
        sale_type = 'package'
        package_name = 'Expert Package'
        client = 'Mustafa'
        location = 'A'  # Al Warqa

        # Omar should get commission on all expert packages
        is_expert_package = 'expert' in package_name.lower()
        assert is_expert_package == True

        if is_expert_package and sale_type == 'package':
            omar_commission = Decimal("50.00")
        else:
            omar_commission = Decimal("0.00")

        assert omar_commission == Decimal("50.00")

    @pytest.mark.django_db
    def test_omar_home_session_100_aed_gets_50_aed_commission(self):
        """
        Example: Omar is having a home session at Mustafa Home for Stretching for 100 AED - Omar will take 52.50 AED
        """
        # Test the business logic for this scenario
        sale_type = 'service'
        location = 'home'
        therapist = 'Omar'
        sale_price = Decimal("100.00")

        # Omar gets 50% for ALL home sessions (regardless of who performs them)
        is_home_service = location == 'home'
        is_omar_therapist = therapist == 'Omar'

        assert is_home_service == True
        assert is_omar_therapist == True

        # Omar always gets commission for home services
        if is_home_service and sale_type == 'service':
            commission_rate = Decimal("0.5")  # 50%
            base_commission = (sale_price * commission_rate).quantize(Decimal('0.01'))
            # Add 5% VAT
            vat_amount = (base_commission * Decimal("0.05")).quantize(Decimal('0.01'))
            total_commission = base_commission + vat_amount
        else:
            total_commission = Decimal("0.00")

        assert base_commission == Decimal("50.00")
        assert total_commission == Decimal("52.50")  # 50 + 2.5 VAT

        # Since Omar is the therapist, only Omar gets commission (no additional therapist commission)

    @pytest.mark.django_db
    def test_khaled_home_package_1000_aed_omar_gets_420_aed_commission(self):
        """
        Example: Khaled buys Home Package for 1000 AED and Omar will do every appointment with him - Omar will take 420 AED
        """
        # Test the business logic for this scenario
        sale_type = 'package'
        location = 'home'  # Assuming home package means appointments at home
        therapist = 'Omar'
        sale_price = Decimal("1000.00")

        # Omar gets 40% for ALL home packages (regardless of who performs them)
        is_home_package = location == 'home'
        is_omar_therapist = therapist == 'Omar'

        assert is_home_package == True
        assert is_omar_therapist == True

        # Omar always gets commission for home services
        if is_home_package and sale_type == 'package':
            commission_rate = Decimal("0.4")  # 40%
            base_commission = (sale_price * commission_rate).quantize(Decimal('0.01'))
            # Add 5% VAT
            vat_amount = (base_commission * Decimal("0.05")).quantize(Decimal('0.01'))
            total_commission = base_commission + vat_amount
        else:
            total_commission = Decimal("0.00")

        assert base_commission == Decimal("400.00")
        assert total_commission == Decimal("420.00")  # 400 + 20 VAT

        # Since Omar is the therapist, only Omar gets commission (no additional therapist commission)

    @pytest.mark.django_db
    def test_basil_home_session_dual_commission_scenario(self):
        """
        New Example: Basil performs home session for 200 AED - Omar gets 105 AED, Basil gets commission if he meets threshold
        """
        # Test the business logic for this scenario
        sale_type = 'service'
        location = 'home'
        therapist = 'Basil'
        sale_price = Decimal("200.00")

        # Omar gets 50% for ALL home sessions (regardless of who performs them)
        is_home_service = location == 'home'
        is_omar_therapist = therapist == 'Omar'

        assert is_home_service == True
        assert is_omar_therapist == False

        # Omar always gets commission for home services
        if is_home_service and sale_type == 'service':
            omar_commission_rate = Decimal("0.5")  # 50%
            omar_base_commission = (sale_price * omar_commission_rate).quantize(Decimal('0.01'))
            # Add 5% VAT
            omar_vat_amount = (omar_base_commission * Decimal("0.05")).quantize(Decimal('0.01'))
            omar_total_commission = omar_base_commission + omar_vat_amount
        else:
            omar_total_commission = Decimal("0.00")

        assert omar_base_commission == Decimal("100.00")
        assert omar_total_commission == Decimal("105.00")  # 100 + 5 VAT

        # Since Basil is the therapist (not Omar), Basil also gets commission if he meets threshold
        # This would be calculated based on Basil's commission profile rules


class TestNotesBasedCommissionCalculation:
    """
    Test cases for notes-based commission calculation.
    Tests the new functionality that extracts duration from appointment notes
    and excludes red light and compression sessions from commission calculation.

    Test Cases:
    - TC-N-001: Single service notes parsing
    - TC-N-002: Mixed services with red light exclusion
    - TC-N-003: Compression session exclusion
    - TC-N-004: Multiple commission services
    - TC-N-005: Mixed services with compression exclusion
    - TC-N-006: Empty notes fallback
    - TC-N-007: No notes fallback
    - TC-N-008: Session-based package commission calculation
    """

    @pytest.mark.django_db
    def test_tc_n_001_notes_parsing_single_service(self):
        """TC-N-001: Test parsing single service from notes"""
        from api.commissions.services import CommissionCalculator

        # Mock appointment with single service
        class MockAppointment:
            def __init__(self, notes, total_duration):
                self.notes = notes
                self.total_duration = total_duration

        # Test case: Single commission-eligible service
        apt = MockAppointment("Stretching: 30 min", 60)
        result = CommissionCalculator._extract_commission_duration_from_notes(apt)
        assert result == 30, f"Expected 30 minutes, got {result}"

    @pytest.mark.django_db
    def test_tc_n_002_notes_parsing_mixed_services_with_red_light_exclusion(self):
        """TC-N-002: Test parsing mixed services with red light exclusion"""
        from api.commissions.services import CommissionCalculator

        class MockAppointment:
            def __init__(self, notes, total_duration):
                self.notes = notes
                self.total_duration = total_duration

        # Test case: Mixed services - exclude red light
        apt = MockAppointment("Massage: 45 min, Red-Light: 15 min", 60)
        result = CommissionCalculator._extract_commission_duration_from_notes(apt)
        assert result == 45, f"Expected 45 minutes (excluding red light), got {result}"

    @pytest.mark.django_db
    def test_tc_n_003_notes_parsing_compression_exclusion(self):
        """TC-N-003: Test that compression sessions are excluded from commission"""
        from api.commissions.services import CommissionCalculator

        class MockAppointment:
            def __init__(self, notes, total_duration):
                self.notes = notes
                self.total_duration = total_duration

        # Test case: Only compression service - should fallback to total_duration
        apt = MockAppointment("Compression: 20 min", 30)
        result = CommissionCalculator._extract_commission_duration_from_notes(apt)
        assert result == 30, f"Expected 30 minutes (fallback), got {result}"

    @pytest.mark.django_db
    def test_tc_n_004_notes_parsing_multiple_commission_services(self):
        """TC-N-004: Test parsing multiple commission-eligible services"""
        from api.commissions.services import CommissionCalculator

        class MockAppointment:
            def __init__(self, notes, total_duration):
                self.notes = notes
                self.total_duration = total_duration

        # Test case: Multiple commission services
        apt = MockAppointment("Stretching: 25 min, Massage: 35 min", 60)
        result = CommissionCalculator._extract_commission_duration_from_notes(apt)
        assert result == 60, f"Expected 60 minutes (25+35), got {result}"

    @pytest.mark.django_db
    def test_tc_n_005_notes_parsing_mixed_with_compression(self):
        """TC-N-005: Test parsing services mixed with compression"""
        from api.commissions.services import CommissionCalculator

        class MockAppointment:
            def __init__(self, notes, total_duration):
                self.notes = notes
                self.total_duration = total_duration

        # Test case: Commission services + compression
        apt = MockAppointment("Stretching: 20 min, Compression: 10 min, Massage: 30 min", 60)
        result = CommissionCalculator._extract_commission_duration_from_notes(apt)
        assert result == 50, f"Expected 50 minutes (20+30, excluding compression), got {result}"

    @pytest.mark.django_db
    def test_tc_n_006_notes_parsing_empty_notes_fallback(self):
        """TC-N-006: Test fallback to total_duration when notes are empty"""
        from api.commissions.services import CommissionCalculator

        class MockAppointment:
            def __init__(self, notes, total_duration):
                self.notes = notes
                self.total_duration = total_duration

        # Test case: Empty notes - fallback to total_duration
        apt = MockAppointment("", 45)
        result = CommissionCalculator._extract_commission_duration_from_notes(apt)
        assert result == 45, f"Expected 45 minutes (fallback), got {result}"

    @pytest.mark.django_db
    def test_tc_n_007_notes_parsing_no_notes_fallback(self):
        """TC-N-007: Test fallback to total_duration when notes are None"""
        from api.commissions.services import CommissionCalculator

        class MockAppointment:
            def __init__(self, notes, total_duration):
                self.notes = notes
                self.total_duration = total_duration

        # Test case: No notes - fallback to total_duration
        apt = MockAppointment(None, 30)
        result = CommissionCalculator._extract_commission_duration_from_notes(apt)
        assert result == 30, f"Expected 30 minutes (fallback), got {result}"

    @pytest.mark.django_db
    def test_tc_n_008_session_based_package_commission_calculation(self):
        """TC-N-008: Test session-based package commission calculation using notes duration"""
        from api.commissions.services import CommissionCalculator
        from api.appointments.models import Sale, Appointment
        from api.services.models import ServicePackage, PackageOption
        from api.staff.models import TherapistProfile
        from decimal import Decimal

        # Create test data
        User = get_user_model()

        # Create therapist
        therapist_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='Therapist',
            role='therapist'
        )
        therapist_profile = TherapistProfile.objects.create(
            user=therapist_user,
            is_freelancer=False
        )

        # Create customer
        customer = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='Customer',
            role='customer'
        )

        # Create package
        package = ServicePackage.objects.create(
            name='Test Package for Notes',
            description='Test package for notes-based commission'
        )
        package_option = PackageOption.objects.create(
            package=package,
            time=100,  # 100 minutes
            price=Decimal('615.00')  # 615 AED
        )

        # Create user package
        from api.appointments.models import UserPackage
        user_package = UserPackage.objects.create(
            user=customer,
            package_option=package_option,
            total_time=100,
            remaining_time=70  # 30 minutes used
        )

        # Create appointment with notes
        appointment = Appointment.objects.create(
            customer=customer,
            therapist=therapist_profile,
            date=date.today(),
            time=datetime.now().time(),
            total_duration=30,  # Total appointment duration
            total_price=Decimal('184.50'),  # Proportional price
            location='A',
            notes="Stretching: 20 min, Red-Light: 10 min"  # Only 20 min eligible for commission
        )

        # Create sale
        sale = Sale.objects.create(
            user=customer,
            sale_type='package',
            appointment=appointment,
            user_package=user_package,
            total_price=Decimal('184.50'),
            payment_method='card'
        )

        # Test session-based price calculation
        session_price = CommissionCalculator._calculate_session_based_package_price(sale, None)

        # Expected calculation:
        # Package: 615 AED for 100 minutes = 6.15 AED per minute
        # Commission duration: 20 minutes (from notes, excluding red light)
        # Session price: 6.15 × 20 = 123 AED
        expected_price = Decimal('123.00')

        assert session_price == expected_price, f"Expected {expected_price} AED, got {session_price} AED"

    @pytest.mark.django_db
    def test_tc_n_009_red_light_variations_exclusion(self):
        """TC-N-009: Test various red light naming variations are excluded"""
        from api.commissions.services import CommissionCalculator

        class MockAppointment:
            def __init__(self, notes, total_duration):
                self.notes = notes
                self.total_duration = total_duration

        test_cases = [
            ("Massage: 30 min, Red-Light: 15 min", 30),  # Standard format
            ("Massage: 30 min, Red Light: 15 min", 30),   # Space variant
            ("Massage: 30 min, RedLight: 15 min", 30),    # No space variant
            ("Massage: 30 min, red-light: 15 min", 30),   # Lowercase
        ]

        for notes, expected in test_cases:
            apt = MockAppointment(notes, 45)
            result = CommissionCalculator._extract_commission_duration_from_notes(apt)
            assert result == expected, f"Notes '{notes}' expected {expected} min, got {result} min"

    @pytest.mark.django_db
    def test_tc_n_010_compression_variations_exclusion(self):
        """TC-N-010: Test various compression naming variations are excluded"""
        from api.commissions.services import CommissionCalculator

        class MockAppointment:
            def __init__(self, notes, total_duration):
                self.notes = notes
                self.total_duration = total_duration

        test_cases = [
            ("Massage: 30 min, Compression: 15 min", 30),  # Standard format
            ("Massage: 30 min, Compress: 15 min", 30),     # Short variant
            ("Massage: 30 min, compression: 15 min", 30),  # Lowercase
        ]

        for notes, expected in test_cases:
            apt = MockAppointment(notes, 45)
            result = CommissionCalculator._extract_commission_duration_from_notes(apt)
            assert result == expected, f"Notes '{notes}' expected {expected} min, got {result} min"
