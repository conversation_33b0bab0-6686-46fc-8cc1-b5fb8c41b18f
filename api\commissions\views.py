from rest_framework import viewsets, permissions, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db.models import Sum
from django.db import transaction
from django.utils import timezone
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
from .models import (
    CommissionProfile, CommissionRule, ManualCommission,
    CommissionEarning, TherapistStats, TherapistYearStats, TherapistMonthStats
)
from .serializers import (
    CommissionProfileSerializer, CommissionRuleSerializer,
    ManualCommissionSerializer, CommissionEarningSerializer,
    TherapistStatsSerializer, TherapistYearStatsSerializer, TherapistMonthStatsSerializer
)
from .services import CommissionCalculator
from api.staff.models import TherapistProfile
from api.appointments.models import Sale
from rest_framework.views import APIView
from rest_framework.permissions import IsAuthenticated, IsAdminUser
from rest_framework.exceptions import PermissionDenied


class CommissionProfileViewSet(viewsets.ModelViewSet):
    """API endpoint for Commission Profiles"""
    queryset = CommissionProfile.objects.all()
    serializer_class = CommissionProfileSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'therapist__user__first_name', 'therapist__user__last_name']
    ordering_fields = ['name', 'created_at', 'updated_at']

    def get_queryset(self):
        user = self.request.user

        # Check if inactive profiles should be included (for admin purposes)
        include_inactive = self.request.query_params.get('include_inactive', 'false').lower() == 'true'

        # Only owners can see all profiles (TC-P-004)
        if user.role in ['owner']:
            queryset = CommissionProfile.objects.all()
            # TC-P-006: By default, only return active profiles unless explicitly requested
            if not include_inactive:
                queryset = queryset.filter(is_active=True)
            return queryset

        # Therapists can only see their own profiles
        if user.role == 'therapist':
            try:
                therapist = TherapistProfile.objects.get(user=user)
                queryset = CommissionProfile.objects.filter(therapist=therapist)
                # TC-P-006: Therapists should also only see active profiles by default
                if not include_inactive:
                    queryset = queryset.filter(is_active=True)
                return queryset
            except TherapistProfile.DoesNotExist:
                return CommissionProfile.objects.none()

        # Receptionists, customers, and others cannot see any profiles
        return CommissionProfile.objects.none()

    def perform_create(self, serializer):
        # Only allow creation by owners
        if self.request.user.role not in ['owner']:
            raise PermissionDenied("You do not have permission to create commission profiles.")

        # Check if this is a default profile creation
        is_default = serializer.validated_data.get('is_default', False)
        threshold = serializer.validated_data.get('sessions_threshold', 0)

        if threshold < 15:
            raise PermissionDenied("Sessions threshold must be at least 15 for default profiles.")

        if is_default:
            # Handle default profile creation with business logic
            profile = self._create_default_profile(serializer.validated_data)
            serializer.instance = profile
        else:
            # Regular profile creation
            serializer.save()

    @transaction.atomic
    def _create_default_profile(self, validated_data):
        """
        Create a default profile with business logic:
        1. Find previous default profile for the same therapist
        2. Copy all rules from previous default profile
        3. Set previous default profile's is_default to False
        4. Create new default profile
        """
        from .models import CommissionRule

        therapist = validated_data['therapist']

        try:
            # Step 1: Get previous default profile for this therapist
            previous_default = CommissionProfile.objects.filter(
                therapist=therapist,
                is_default=True
            ).first()

            # Step 2: Create new profile (the model's save() method will handle setting previous default to False)
            new_profile = CommissionProfile.objects.create(**validated_data)

            # Step 3: If previous default exists, copy all its rules to new profile
            if previous_default:
                self._copy_rules_from_profile(previous_default, new_profile)

                # Note: User assignment is automatic since TherapistProfile -> CommissionProfile relationship
                # The therapist automatically gets the new default profile

            return new_profile

        except Exception as e:
            # Log the error and re-raise with a more user-friendly message
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error creating default profile for therapist {therapist.id}: {str(e)}")

            from rest_framework.exceptions import ValidationError
            raise ValidationError(f"Failed to create default profile: {str(e)}")

    def _copy_rules_from_profile(self, source_profile, target_profile):
        """
        Copy all rules from source profile to target profile.
        Creates new rule instances with the same attributes but linked to the new profile.
        """
        from .models import CommissionRule

        try:
            # Get all rules from the source profile
            source_rules = CommissionRule.objects.filter(profile=source_profile)

            # Copy each rule to the new profile
            for rule in source_rules:
                CommissionRule.objects.create(
                    profile=target_profile,
                    name=rule.name,
                    rule_type=rule.rule_type,
                    service=rule.service,
                    package=rule.package,
                    percentage=rule.percentage,
                    fixed_amount=rule.fixed_amount,
                    min_sessions=rule.min_sessions,
                    priority=rule.priority,
                    is_active=rule.is_active,
                    # Note: created_at and updated_at will be auto-generated for new rules
                )

        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error copying rules from profile {source_profile.id} to {target_profile.id}: {str(e)}")
            raise

    def perform_update(self, serializer):
        if self.request.user.role not in ['owner']:
            raise PermissionDenied("You do not have permission to update commission profiles.")
        serializer.save()

    def perform_destroy(self, instance):
        if self.request.user.role not in ['owner']:
            raise PermissionDenied("You do not have permission to delete commission profiles.")

        if instance.is_default:
            raise PermissionDenied("Default profile cannot be deleted. Please create a new default profile or update any other profile to be default first.")

        instance.delete()

    @action(detail=True, methods=['post'])
    def activate(self, request, pk=None):
        """
        Activate a commission profile.
        Only owners can activate profiles.
        """
        if request.user.role not in ['owner']:
            return Response(
                {"detail": "You do not have permission to activate commission profiles."},
                status=status.HTTP_403_FORBIDDEN
            )

        # For activate/deactivate actions, we need to access all profiles (including inactive ones)
        # to allow activation of inactive profiles
        try:
            if request.user.role == 'owner':
                profile = CommissionProfile.objects.get(pk=pk)
            else:
                # This shouldn't happen due to permission check above, but just in case
                return Response(
                    {"detail": "You do not have permission to activate commission profiles."},
                    status=status.HTTP_403_FORBIDDEN
                )
        except CommissionProfile.DoesNotExist:
            return Response(
                {"detail": "Commission profile not found."},
                status=status.HTTP_404_NOT_FOUND
            )

        if profile.is_active:
            return Response(
                {"detail": "Commission profile is already active."},
                status=status.HTTP_400_BAD_REQUEST
            )

        profile.is_active = True
        profile.save(update_fields=['is_active'])

        return Response({
            "detail": f"Commission profile '{profile.name}' has been activated.",
            "profile_id": profile.id,
            "is_active": profile.is_active
        }, status=status.HTTP_200_OK)

    @action(detail=True, methods=['post'])
    def deactivate(self, request, pk=None):
        """
        Deactivate a commission profile.
        Only owners can deactivate profiles.
        """
        if request.user.role not in ['owner']:
            return Response(
                {"detail": "You do not have permission to deactivate commission profiles."},
                status=status.HTTP_403_FORBIDDEN
            )

        # For activate/deactivate actions, we need to access all profiles (including inactive ones)
        try:
            if request.user.role == 'owner':
                profile = CommissionProfile.objects.get(pk=pk)
            else:
                # This shouldn't happen due to permission check above, but just in case
                return Response(
                    {"detail": "You do not have permission to deactivate commission profiles."},
                    status=status.HTTP_403_FORBIDDEN
                )
        except CommissionProfile.DoesNotExist:
            return Response(
                {"detail": "Commission profile not found."},
                status=status.HTTP_404_NOT_FOUND
            )

        if not profile.is_active:
            return Response(
                {"detail": "Commission profile is already inactive."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # TC-P-008: Verify active and default profiles cannot have status changed
        if profile.is_default:
            return Response(
                {"detail": "Default profile cannot be deactivated. Please create a new default profile or update any other profile to be default first."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # TC-P-009: Verify profiles with assigned therapists cannot be deactivated until therapists are reassigned
        # Only prevent deactivation if this is the therapist's only active profile or if therapist would have no active profiles left
        if profile.therapist.is_active:
            # Count active profiles for this therapist
            active_profiles_count = CommissionProfile.objects.filter(
                therapist=profile.therapist,
                is_active=True
            ).count()

            # If this is the only active profile for an active therapist, prevent deactivation
            if active_profiles_count <= 1:
                return Response(
                    {"detail": f"Commission profile '{profile.name}' cannot be deactivated as it is the only active profile for therapist '{profile.therapist.user.get_full_name()}'. Please create another active profile for this therapist first or deactivate the therapist."},
                    status=status.HTTP_400_BAD_REQUEST
                )

        profile.is_active = False
        profile.save(update_fields=['is_active'])

        return Response({
            "detail": f"Commission profile '{profile.name}' has been deactivated.",
            "profile_id": profile.id,
            "is_active": profile.is_active
        }, status=status.HTTP_200_OK)

    @action(detail=True, methods=['post'])
    def assign(self, request, pk=None):
        """
        Assign a commission profile to a therapist.
        Only owners can assign profiles.
        """
        # Only allow assignment by owners
        if request.user.role not in ['owner']:
            return Response(
                {"detail": "You do not have permission to assign commission profiles."},
                status=status.HTTP_403_FORBIDDEN
            )

        profile = self.get_object()
        therapist_id = request.data.get('therapist_id')

        if not therapist_id:
            return Response(
                {"detail": "therapist_id is required."},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            from api.staff.models import TherapistProfile
            therapist = TherapistProfile.objects.get(id=therapist_id)
        except TherapistProfile.DoesNotExist:
            return Response(
                {"detail": "Therapist not found."},
                status=status.HTTP_404_NOT_FOUND
            )

        # TC-P-011: Verify therapist can be assigned only one active profile
        if profile.is_active:
            # Check if therapist already has an active profile
            existing_active_profile = CommissionProfile.objects.filter(
                therapist=therapist,
                is_active=True
            ).exclude(pk=profile.pk).first()

            if existing_active_profile:
                return Response(
                    {"detail": f"Therapist '{therapist.user.get_full_name()}' already has an active commission profile: '{existing_active_profile.name}'. Please deactivate it first or assign an inactive profile."},
                    status=status.HTTP_400_BAD_REQUEST
                )

        # Assign the profile to the therapist
        old_therapist = profile.therapist
        profile.therapist = therapist
        profile.save(update_fields=['therapist'])

        return Response({
            "detail": f"Commission profile '{profile.name}' has been assigned to therapist '{therapist.user.get_full_name()}' successfully.",
            "old_therapist": old_therapist.user.get_full_name() if old_therapist else None,
            "new_therapist": therapist.user.get_full_name(),
            "profile_id": profile.id,
            "profile_name": profile.name
        })

    @action(detail=True, methods=['post'])
    def reassign(self, request, pk=None):
        """
        Reassign a commission profile to a different therapist.
        Only owners can reassign profiles.
        """
        # Only allow reassignment by owners
        if request.user.role not in ['owner']:
            return Response(
                {"detail": "You do not have permission to reassign commission profiles."},
                status=status.HTTP_403_FORBIDDEN
            )

        profile = self.get_object()
        therapist_id = request.data.get('therapist_id')

        if not therapist_id:
            return Response(
                {"detail": "therapist_id is required."},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            from api.staff.models import TherapistProfile
            new_therapist = TherapistProfile.objects.get(id=therapist_id)
        except TherapistProfile.DoesNotExist:
            return Response(
                {"detail": "Therapist not found."},
                status=status.HTTP_404_NOT_FOUND
            )

        # Check if trying to assign to the same therapist
        if profile.therapist.id == new_therapist.id:
            return Response(
                {"detail": f"Commission profile '{profile.name}' is already assigned to therapist '{new_therapist.user.get_full_name()}'."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # TC-P-011: Verify therapist can be assigned only one active profile
        if profile.is_active:
            # Check if new therapist already has an active profile
            existing_active_profile = CommissionProfile.objects.filter(
                therapist=new_therapist,
                is_active=True
            ).first()

            if existing_active_profile:
                return Response(
                    {"detail": f"Therapist '{new_therapist.user.get_full_name()}' already has an active commission profile: '{existing_active_profile.name}'. Please deactivate it first or assign an inactive profile."},
                    status=status.HTTP_400_BAD_REQUEST
                )

        # Reassign the profile to the new therapist
        old_therapist = profile.therapist
        profile.therapist = new_therapist
        profile.save(update_fields=['therapist'])

        return Response({
            "detail": f"Commission profile '{profile.name}' has been reassigned from '{old_therapist.user.get_full_name()}' to '{new_therapist.user.get_full_name()}' successfully.",
            "old_therapist": old_therapist.user.get_full_name(),
            "new_therapist": new_therapist.user.get_full_name(),
            "profile_id": profile.id,
            "profile_name": profile.name
        })


class CommissionRuleViewSet(viewsets.ModelViewSet):
    """API endpoint for Commission Rules"""
    queryset = CommissionRule.objects.all()
    serializer_class = CommissionRuleSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'profile__name']
    ordering_fields = ['name', 'priority', 'created_at']

    def get_queryset(self):
        user = self.request.user

        # Check if inactive rules should be included (for admin purposes)
        include_inactive = self.request.query_params.get('include_inactive', 'false').lower() == 'true'

        # For detail views (retrieve, update, delete), always include inactive rules for owners
        # This allows owners to activate/deactivate rules
        if self.action in ['retrieve', 'update', 'partial_update', 'destroy']:
            if user.role in ['owner']:
                return CommissionRule.objects.all()
            elif user.role == 'therapist':
                try:
                    therapist = TherapistProfile.objects.get(user=user)
                    profiles = CommissionProfile.objects.filter(therapist=therapist)
                    return CommissionRule.objects.filter(profile__in=profiles)
                except TherapistProfile.DoesNotExist:
                    return CommissionRule.objects.none()
            else:
                return CommissionRule.objects.none()

        # For list views, apply the active/inactive filtering
        # Only owners can see all rules (matching Commission Profile pattern)
        if user.role in ['owner']:
            queryset = CommissionRule.objects.all()
            # By default, only return active rules unless explicitly requested
            if not include_inactive:
                queryset = queryset.filter(is_active=True)
            return queryset

        # Therapists can only see rules attached to their profiles
        if user.role == 'therapist':
            try:
                therapist = TherapistProfile.objects.get(user=user)
                profiles = CommissionProfile.objects.filter(therapist=therapist)
                queryset = CommissionRule.objects.filter(profile__in=profiles)
                # Therapists should also only see active rules by default
                if not include_inactive:
                    queryset = queryset.filter(is_active=True)
                return queryset
            except TherapistProfile.DoesNotExist:
                return CommissionRule.objects.none()

        # Receptionists, customers, and others cannot see any rules (matching Commission Profile pattern)
        return CommissionRule.objects.none()

    def perform_create(self, serializer):
        # Only allow creation by owners
        if self.request.user.role not in ['owner']:
            raise PermissionDenied("You do not have permission to create commission rules.")
        serializer.save()

    def perform_update(self, serializer):
        # Only allow updates by owners
        if self.request.user.role not in ['owner']:
            raise PermissionDenied("You do not have permission to update commission rules.")
        serializer.save()

    def perform_destroy(self, instance):
        # Only allow deletion by owners
        if self.request.user.role not in ['owner']:
            raise PermissionDenied("You do not have permission to delete commission rules.")
        instance.delete()


class ManualCommissionViewSet(viewsets.ModelViewSet):
    """API endpoint for Manual Commissions"""
    queryset = ManualCommission.objects.all()
    serializer_class = ManualCommissionSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['therapist__user__first_name', 'therapist__user__last_name', 'notes']
    ordering_fields = ['created_at']

    def get_queryset(self):
        user = self.request.user

        # Only owners and receptionists can see all manual commissions
        if user.role in ['owner', 'receptionist']:
            return ManualCommission.objects.all()

        # Therapists can only see their own manual commissions
        if user.role == 'therapist':
            try:
                therapist = TherapistProfile.objects.get(user=user)
                return ManualCommission.objects.filter(therapist=therapist)
            except TherapistProfile.DoesNotExist:
                return ManualCommission.objects.none()

        return ManualCommission.objects.none()

    def perform_create(self, serializer):
        # Only allow creation by owners
        if self.request.user.role not in ['owner']:
            raise PermissionDenied("You do not have permission to create manual commissions.")
        serializer.save(created_by=self.request.user)

        # Recalculate commission for this sale
        manual_commission = serializer.instance
        CommissionCalculator.calculate_for_sale(manual_commission.sale)


class CommissionEarningViewSet(viewsets.ModelViewSet):
    """API endpoint for Commission Earnings"""
    queryset = CommissionEarning.objects.all()
    serializer_class = CommissionEarningSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['therapist__user__first_name', 'therapist__user__last_name', 'notes']
    ordering_fields = ['date_earned', 'amount', 'created_at']

    def get_queryset(self):
        user = self.request.user

        # Only owners and receptionists can see all earnings
        if user.role in ['owner', 'receptionist']:
            queryset = CommissionEarning.objects.all()
        # Therapists can only see their own earnings
        elif user.role == 'therapist':
            try:
                therapist = TherapistProfile.objects.get(user=user)
                queryset = CommissionEarning.objects.filter(therapist=therapist)
            except TherapistProfile.DoesNotExist:
                return CommissionEarning.objects.none()
        else:
            return CommissionEarning.objects.none()

        # Filter by date range if provided
        start_date = self.request.query_params.get('start_date')
        end_date = self.request.query_params.get('end_date')

        if start_date:
            try:
                start_date = datetime.strptime(start_date, '%Y-%m-%d').date()
                queryset = queryset.filter(date_earned__gte=start_date)
            except ValueError:
                pass

        if end_date:
            try:
                end_date = datetime.strptime(end_date, '%Y-%m-%d').date()
                queryset = queryset.filter(date_earned__lte=end_date)
            except ValueError:
                pass

        return queryset

    @action(detail=False, methods=['get'])
    def summary(self, request):
        """Get summary of commission earnings."""
        user = request.user

        # Determine time period
        period = request.query_params.get('period', 'month')
        today = timezone.now().date()

        if period == 'month':
            start_date = today.replace(day=1)
        elif period == 'quarter':
            month = today.month
            if month <= 3:
                start_date = today.replace(month=1, day=1)
            elif month <= 6:
                start_date = today.replace(month=4, day=1)
            elif month <= 9:
                start_date = today.replace(month=7, day=1)
            else:
                start_date = today.replace(month=10, day=1)
        elif period == 'year':
            start_date = today.replace(month=1, day=1)
        elif period == 'all':
            start_date = None
        else:
            return Response(
                {"detail": "Invalid period parameter. Use 'month', 'quarter', 'year', or 'all'."},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get earnings
        if user.role in ['owner', 'receptionist']:
            # For owners/receptionists, return overall summary and per-therapist
            queryset = CommissionEarning.objects.all()
            if start_date:
                queryset = queryset.filter(date_earned__gte=start_date)

            total = queryset.aggregate(Sum('amount'))['amount__sum'] or 0

            # Group by therapist
            therapist_summary = []
            for therapist in TherapistProfile.objects.all():
                therapist_earnings = queryset.filter(therapist=therapist)
                therapist_total = therapist_earnings.aggregate(Sum('amount'))['amount__sum'] or 0

                therapist_summary.append({
                    'therapist_id': therapist.id,
                    'name': therapist.user.get_full_name(),
                    'email': therapist.user.email,
                    'total_earnings': therapist_total,
                    'is_freelancer': getattr(therapist, 'is_freelancer', False)
                })

            return Response({
                'period': period,
                'start_date': start_date,
                'end_date': today,
                'total_earnings': total,
                'therapists': therapist_summary
            })

        elif user.role == 'therapist':
            # For therapists, return only their earnings
            try:
                therapist = TherapistProfile.objects.get(user=user)
                queryset = CommissionEarning.objects.filter(therapist=therapist)
                if start_date:
                    queryset = queryset.filter(date_earned__gte=start_date)

                total = queryset.aggregate(Sum('amount'))['amount__sum'] or 0

                # Get monthly breakdown for the last 6 months
                monthly_breakdown = []
                for i in range(6):
                    month_end = today - relativedelta(months=i)
                    month_start = month_end.replace(day=1)
                    month_next = month_end + relativedelta(months=1, day=1)

                    month_earnings = queryset.filter(
                        date_earned__gte=month_start,
                        date_earned__lt=month_next
                    )
                    month_total = month_earnings.aggregate(Sum('amount'))['amount__sum'] or 0

                    monthly_breakdown.append({
                        'month': month_start.strftime('%B %Y'),
                        'earnings': month_total
                    })

                return Response({
                    'period': period,
                    'start_date': start_date,
                    'end_date': today,
                    'total_earnings': total,
                    'monthly_breakdown': monthly_breakdown
                })

            except TherapistProfile.DoesNotExist:
                return Response(
                    {"detail": "Therapist profile not found."},
                    status=status.HTTP_404_NOT_FOUND
                )
        else:
            return Response(
                {"detail": "You do not have permission to view commission summaries."},
                status=status.HTTP_403_FORBIDDEN
            )

    @action(detail=False, methods=['post'])
    def mark_paid(self, request):
        """Mark multiple commission earnings as paid."""
        if request.user.role not in ['owner', 'receptionist']:
            return Response(
                {"detail": "You do not have permission to mark commissions as paid."},
                status=status.HTTP_403_FORBIDDEN
            )

        commission_ids = request.data.get('commission_ids', [])
        payment_date = request.data.get('payment_date')
        notes = request.data.get('notes', '')

        if not commission_ids:
            return Response(
                {"detail": "No commission IDs provided."},
                status=status.HTTP_400_BAD_REQUEST
            )

        if not payment_date:
            payment_date = timezone.now().date()
        else:
            try:
                payment_date = datetime.strptime(payment_date, '%Y-%m-%d').date()
            except ValueError:
                return Response(
                    {"detail": "Invalid payment date format. Use YYYY-MM-DD."},
                    status=status.HTTP_400_BAD_REQUEST
                )

        # Update commissions
        updated_count = CommissionEarning.objects.filter(id__in=commission_ids).update(
            is_paid=True,
            payment_date=payment_date,
            notes=notes
        )

        return Response({
            "detail": f"Marked {updated_count} commissions as paid.",
            "updated_count": updated_count
        })


class TherapistStatsViewSet(viewsets.ReadOnlyModelViewSet):
    """API endpoint for Therapist Stats"""
    queryset = TherapistStats.objects.all()
    serializer_class = TherapistStatsSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_queryset(self):
        user = self.request.user

        # Only owners and receptionists can see all stats
        if user.role in ['owner', 'receptionist']:
            return TherapistStats.objects.all()

        # Therapists can only see their own stats
        if user.role == 'therapist':
            try:
                therapist = TherapistProfile.objects.get(user=user)
                return TherapistStats.objects.filter(therapist=therapist)
            except TherapistProfile.DoesNotExist:
                return TherapistStats.objects.none()

        return TherapistStats.objects.none()


class TherapistYearStatsViewSet(viewsets.ReadOnlyModelViewSet):
    """API endpoint for Therapist Year Stats"""
    queryset = TherapistYearStats.objects.all()
    serializer_class = TherapistYearStatsSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.OrderingFilter]
    ordering_fields = ['year', 'total_sessions', 'total_earnings']
    ordering = ['-year']

    def get_queryset(self):
        user = self.request.user

        # Only owners and receptionists can see all stats
        if user.role in ['owner', 'receptionist']:
            queryset = TherapistYearStats.objects.all()
        # Therapists can only see their own stats
        elif user.role == 'therapist':
            try:
                therapist = TherapistProfile.objects.get(user=user)
                queryset = TherapistYearStats.objects.filter(therapist=therapist)
            except TherapistProfile.DoesNotExist:
                return TherapistYearStats.objects.none()
        else:
            return TherapistYearStats.objects.none()

        # Filter by year if provided
        year = self.request.query_params.get('year')
        if year:
            try:
                year = int(year)
                queryset = queryset.filter(year=year)
            except ValueError:
                pass

        return queryset


class TherapistMonthStatsViewSet(viewsets.ModelViewSet):
    """API endpoint for Therapist Month Stats"""
    queryset = TherapistMonthStats.objects.all()
    serializer_class = TherapistMonthStatsSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.OrderingFilter]
    ordering_fields = ['year', 'month', 'total_sessions', 'total_earnings']
    ordering = ['-year', '-month']

    def get_queryset(self):
        user = self.request.user

        # Only owners and receptionists can see all stats
        if user.role in ['owner', 'receptionist']:
            queryset = TherapistMonthStats.objects.all()
        # Therapists can only see their own stats
        elif user.role == 'therapist':
            try:
                therapist = TherapistProfile.objects.get(user=user)
                queryset = TherapistMonthStats.objects.filter(therapist=therapist)
            except TherapistProfile.DoesNotExist:
                return TherapistMonthStats.objects.none()
        else:
            return TherapistMonthStats.objects.none()

        # Filter by year and month if provided
        year = self.request.query_params.get('year')
        month = self.request.query_params.get('month')

        if year:
            try:
                year = int(year)
                queryset = queryset.filter(year=year)
            except ValueError:
                pass

        if month:
            try:
                month = int(month)
                queryset = queryset.filter(month=month)
            except ValueError:
                pass

        return queryset

    @action(detail=True, methods=['post'])
    def mark_paid(self, request, pk=None):
        """Mark a month's commissions as paid."""
        if request.user.role not in ['owner', 'receptionist']:
            return Response(
                {"detail": "You do not have permission to mark commissions as paid."},
                status=status.HTTP_403_FORBIDDEN
            )

        month_stat = self.get_object()
        payment_date = request.data.get('payment_date')
        notes = request.data.get('notes', '')

        if not payment_date:
            payment_date = timezone.now().date()
        else:
            try:
                payment_date = datetime.strptime(payment_date, '%Y-%m-%d').date()
            except ValueError:
                return Response(
                    {"detail": "Invalid payment date format. Use YYYY-MM-DD."},
                    status=status.HTTP_400_BAD_REQUEST
                )

        # Update month stat and all related commissions
        with transaction.atomic():
            month_stat.is_paid = True
            month_stat.payment_date = payment_date
            month_stat.save()

            # Update all commissions for this month
            updated_count = CommissionEarning.objects.filter(
                month_stat=month_stat
            ).update(
                is_paid=True,
                payment_date=payment_date,
                notes=notes
            )

        return Response({
            "detail": f"Marked {month_stat.month_name} {month_stat.year} as paid with {updated_count} commissions.",
            "updated_count": updated_count
        })


class EarningsExportView(APIView):
    permission_classes = [IsAuthenticated, IsAdminUser]

    def get(self, request, format=None):
        """
        Export earnings overview as CSV
        """
        # Get query parameters
        year = request.query_params.get('year', datetime.now().year)
        month = request.query_params.get('month')
        therapist_id = request.query_params.get('therapist_id')

        # Build queryset
        queryset = CommissionEarning.objects.filter(year=year)
        if month:
            queryset = queryset.filter(month=month)
        if therapist_id:
            queryset = queryset.filter(therapist_id=therapist_id)

        # Create CSV response
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = f'attachment; filename="earnings_export_{year}_{month or "all"}.csv"'

        writer = csv.writer(response)
        writer.writerow([
            'Date', 'Therapist', 'Service/Package', 'Sale Amount',
            'Commission Amount', 'Commission %', 'Type', 'Location'
        ])

        for earning in queryset:
            writer.writerow([
                earning.sale.date,
                earning.therapist.get_full_name(),
                earning.sale.get_service_name(),
                earning.sale.total_price,
                earning.amount,
                earning.percentage,
                earning.commission_type,
                earning.sale.appointment.location
            ])

        return response
