from django.contrib import admin
from .models import (
    Product,
    ProductSale,
    ProductSaleItem,
    CashRegister,
    CashWithdrawal,
    CashDeposit,
    Expense,
    DailySalesReport,
)


class ProductSaleItemInline(admin.TabularInline):
    model = ProductSaleItem
    extra = 1
    readonly_fields = ["created_at"]


@admin.register(Product)
class ProductAdmin(admin.ModelAdmin):
    list_display = [
        "name",
        "category",
        "price",
        "quantity_in_stock",
        "location",
        "is_active",
    ]
    list_filter = ["category", "location", "is_active"]
    search_fields = ["name", "description"]
    ordering = ["name"]


@admin.register(ProductSale)
class ProductSaleAdmin(admin.ModelAdmin):
    list_display = [
        "invoice_number",
        "get_customer_name",
        "total_amount",
        "payment_method",
        "location",
        "status",
        "created_at",
    ]
    list_filter = ["location", "status", "payment_method", "created_at"]
    search_fields = ["invoice_number", "customer__first_name", "customer__last_name"]
    readonly_fields = ["invoice_number", "created_at"]
    inlines = [ProductSaleItemInline]

    def get_customer_name(self, obj):
        if obj.customer:
            return f"{obj.customer.first_name} {obj.customer.last_name}"
        return "No Customer"

    get_customer_name.short_description = "Customer"


@admin.register(CashRegister)
class CashRegisterAdmin(admin.ModelAdmin):
    list_display = ["date", "location", "current_balance", "created_at"]
    list_filter = ["location", "date"]
    search_fields = ["notes"]
    readonly_fields = ["created_at", "updated_at"]


class CashWithdrawalAdmin(admin.ModelAdmin):
    list_display = ["cash_register", "amount", "reason", "get_staff_name", "created_at"]
    list_filter = ["cash_register__location", "created_at"]
    search_fields = ["reason", "notes"]
    readonly_fields = ["created_at"]

    def get_staff_name(self, obj):
        return f"{obj.staff.first_name} {obj.staff.last_name}"

    get_staff_name.short_description = "Staff"


class CashDepositAdmin(admin.ModelAdmin):
    list_display = ["cash_register", "amount", "reason", "get_staff_name", "created_at"]
    list_filter = ["cash_register__location", "created_at"]
    search_fields = ["reason", "notes"]
    readonly_fields = ["created_at"]

    def get_staff_name(self, obj):
        return f"{obj.staff.first_name} {obj.staff.last_name}"

    get_staff_name.short_description = "Staff"


@admin.register(Expense)
class ExpenseAdmin(admin.ModelAdmin):
    list_display = [
        "description",
        "category",
        "amount",
        "cash_register",
        "get_staff_name",
        "created_at",
    ]
    list_filter = ["category", "cash_register__location", "created_at"]
    search_fields = ["description", "notes"]
    readonly_fields = ["created_at"]

    def get_staff_name(self, obj):
        return f"{obj.staff.first_name} {obj.staff.last_name}"

    get_staff_name.short_description = "Staff"


@admin.register(DailySalesReport)
class DailySalesReportAdmin(admin.ModelAdmin):
    list_display = [
        "date",
        "location",
        "gross_sales_amount",
        "net_sales_amount",
        "created_at",
    ]
    list_filter = ["location", "date", "email_sent"]
    search_fields = ["notes"]
    readonly_fields = ["created_at", "updated_at"]


# Register the remaining models
admin.site.register(CashWithdrawal, CashWithdrawalAdmin)
admin.site.register(CashDeposit, CashDepositAdmin)
