import csv
import os
from datetime import datetime

from django.conf import settings
from django.core.management.base import BaseCommand
from django.db import transaction
from django.db.models import Q

from django.contrib.auth import get_user_model

User = get_user_model()


class Command(BaseCommand):
    help = (
        "Import user contacts from a cleaned CSV file with roles "
        "into the User model."
    )

    def add_arguments(self, parser):
        parser.add_argument(
            "--csv",
            type=str,
            default="contacts_cleaned.csv",
            help="Filename of the cleaned CSV file (default: contacts_cleaned.csv)",
        )
        parser.add_argument(
            "--password",
            type=str,
            default="password123",
            help="Default password for created users (default: password123)",
        )

    def handle(self, *args, **options):
        csv_filename = options["csv"]
        default_password = options["password"]
        csv_path = os.path.join(settings.BASE_DIR, csv_filename)

        if not os.path.exists(csv_path):
            self.stdout.write(self.style.ERROR(f"CSV file not found at: {csv_path}"))
            return

        created_count = 0
        updated_count = 0
        skipped_count = 0

        with open(csv_path, mode="r", encoding="utf-8") as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                # Get phone number
                normalized_phone = str(row.get("normalized_phone", "")).strip()
                phone = normalized_phone or str(row.get("Phone", "")).strip()

                # Get names and convert to proper case
                first_name = str(row.get("first_name", "")).strip()
                last_name = str(row.get("last_name", "")).strip()

                if first_name:
                    first_name = first_name.lower().capitalize()
                else:
                    first_name = "NoName"

                if last_name:
                    last_name = last_name.lower().capitalize()
                else:
                    last_name = "NoSurname"

                # Get email
                email = str(row.get("Email", "")).strip()
                if not email:
                    if phone:
                        email = f"{phone}@example.com"
                    else:
                        self.stdout.write(
                            self.style.WARNING(
                                f"Skipping row because both email and phone are missing: {row}"
                            )
                        )
                        skipped_count += 1
                        continue

                email = email.lower()

                # Get role
                role = str(row.get("role", "")).strip().lower()
                if role not in ["customer", "therapist", "owner", "receptionist"]:
                    role = "customer"

                # Check if user exists
                try:
                    user = User.objects.get(Q(email=email) | Q(phone_number=phone))
                    updated = False
                    if user.email != email:
                        user.email = email
                        updated = True
                    if user.phone_number != phone:
                        user.phone_number = phone
                        updated = True
                    if user.first_name != first_name:
                        user.first_name = first_name
                        updated = True
                    if user.last_name != last_name:
                        user.last_name = last_name
                        updated = True
                    if user.role != role:
                        user.role = role
                        updated = True
                    
                    # Set staff status for admin roles
                    if role in ["owner", "receptionist"]:
                        if not user.is_staff:
                            user.is_staff = True
                            updated = True
                    
                    # Set password if not set
                    if not user.password:
                        user.set_password(default_password)
                        updated = True

                    if updated:
                        user.save()
                        updated_count += 1
                        self.stdout.write(self.style.SUCCESS(f"Updated user: {email}"))
                    else:
                        self.stdout.write(
                            f"User already exists and is up-to-date: {email}"
                        )
                except User.DoesNotExist:
                    try:
                        with transaction.atomic():
                            user = User.objects.create(
                                email=email,
                                phone_number=phone,
                                first_name=first_name,
                                last_name=last_name,
                                is_active=True,
                                role=role,
                            )
                            
                            # Set password
                            user.set_password(default_password)
                            
                            # Set staff status for admin roles
                            if role in ["owner", "receptionist"]:
                                user.is_staff = True
                            
                            # Set superuser for owner role
                            if role == "owner" and email == "<EMAIL>":
                                user.is_superuser = True
                                
                            user.save()
                                
                        created_count += 1
                        self.stdout.write(self.style.SUCCESS(f"Created user: {email}"))
                    except Exception as e:
                        self.stdout.write(
                            self.style.ERROR(f"Error creating user for row {row}: {e}")
                        )
                        skipped_count += 1

        self.stdout.write(
            self.style.SUCCESS(
                f"Import complete. Created: {created_count}, Updated: {updated_count}, Skipped: {skipped_count}"
            )
        )