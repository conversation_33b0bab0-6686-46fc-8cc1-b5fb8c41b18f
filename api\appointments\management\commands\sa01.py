from django.core.management.base import BaseCommand
from django.db.models import F
import datetime
from django.utils import timezone
from api.appointments.models import Sale, Appointment


class Command(BaseCommand):
    help = "Fix Sale objects with dates different from their associated Appointment dates"

    def add_arguments(self, parser):
        parser.add_argument(
            "--dry-run",
            action="store_true",
            help="Perform a dry run without making any changes",
        )

    def handle(self, *args, **options):
        dry_run = options.get("dry_run", False)

        # Get all Sale objects that have an appointment
        sales_with_appointment = Sale.objects.filter(
            appointment__isnull=False
        ).select_related("appointment")

        fixed_count = 0
        total_count = sales_with_appointment.count()

        self.stdout.write(
            self.style.SUCCESS(f"Checking {total_count} Sales with appointments...")
        )

        for sale in sales_with_appointment:
            # Convert created_at to date only for comparison
            sale_date = sale.created_at.date()
            appointment_date = sale.appointment.date

            if sale_date != appointment_date:
                # Create a datetime from the appointment date and time
                from datetime import datetime, time

                if sale.appointment.time:
                    new_datetime = datetime.combine(
                        appointment_date,
                        sale.appointment.time,
                        tzinfo=timezone.get_current_timezone(),
                    )
                else:
                    # If no time, use midnight
                    new_datetime = datetime.combine(
                        appointment_date, time(), tzinfo=timezone.get_current_timezone()
                    )

                self.stdout.write(
                    self.style.WARNING(
                        f"Fixing - Sale ID: {sale.id}, "
                        f"Sale date: {sale_date} → {appointment_date}, "
                        f"Customer: {sale.user.email}"
                    )
                )

                if not dry_run:
                    sale.created_at = new_datetime
                    sale.save(update_fields=["created_at"])

                fixed_count += 1

        action = "Would fix" if dry_run else "Fixed"
        self.stdout.write(
            self.style.SUCCESS(
                f"{action} {fixed_count} Sales with dates different from their Appointment dates "
                f"out of {total_count} total Sales with appointments."
            )
        )

        if dry_run:
            self.stdout.write(
                self.style.SUCCESS(
                    "This was a dry run. No changes were made. "
                    "Run without --dry-run to apply the changes."
                )
            )
