# tests/test_middleware.py
import json
import pytest
from rest_framework.test import APIClient
from django.urls import reverse
from django.conf import settings
from unittest.mock import patch, call
from utils.sanitize_input_middleware import SanitizeInputMiddleware


@pytest.fixture
def api_client():
    return APIClient()

@patch('utils.sanitize_input_middleware.send_email')
def test_sanitize_input_middleware_post_request(mock_send_email, api_client):
    url = reverse('test-xss')
    # Mock request with XSS attempt
    response = api_client.post(
        url, 
        {"key": "<script>alert('XSS')</script>"}, 
        format='json'
    )

    # Check response data
    assert response.status_code == 200
    assert response.json() == {'key': ""}
    
    # Check that XSS was detected and email was sent
    mock_send_email.assert_called_once()
    
    expected_message = (
        "An XSS attempt was detected.\n\nOriginal Data:\n{'key': \"<script>alert('XSS')</script>\"}\n\n"
        "Sanitized Data:\n{'key': \'\'}"
    )
    assert mock_send_email.call_args == call(
        "XSS Attempt Detected",
        expected_message,
        [settings.DEFAULT_FROM_EMAIL]
    )

    
@patch('utils.sanitize_input_middleware.send_email')
def test_sanitize_input_middleware_post_request_no_xss(mock_send_email, api_client):
    url = reverse('test-xss')
    # Mock request without XSS attempt
    response = api_client.post(
        url, 
        {"key": "This is a normal message"}, 
        format='json'
    )

    # Check response data
    assert response.status_code == 200
    assert response.json() == {'key': 'This is a normal message'}
    
    # Check that no XSS was detected and email was not sent
    mock_send_email.assert_not_called()
    
