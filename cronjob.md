# Setting up a Cron Job for Hourly Appointment Reminders

## Prerequisites

- Access to the server where your Django application is running
- Permissions to create cron jobs

## Step 1: Create the Cron Job

1. Connect to your server via SSH
2. Open the crontab configuration:

   ```bash
   crontab -e
   ```

3. Add the following line to run the command every hour:

   ```
   0 * * * * cd /path/to/your/django/project && /path/to/your/virtualenv/bin/python manage.py send_appointment_reminders >> /path/to/your/logs/appointment_reminders.log 2>&1
   ```

   Replace:

   - `/path/to/your/django/project` with the actual path to your Django project
   - `/path/to/your/virtualenv/bin/python` with the path to your virtual environment's Python interpreter
   - `/path/to/your/logs` with your preferred log directory

## Step 2: Check Cron Job Status

To verify that your cron job is set up correctly:

```bash
crontab -l
```

## Step 3: Monitor Logs

Monitor your log file to ensure the script is running as expected:

```bash
tail -f /path/to/your/logs/appointment_reminders.log
```

## Step 4: Setup for Production Environments

For production environments, you might want to use a more robust solution:

### Using Supervisor (recommended for production)

1. Install supervisor if not already installed:

   ```bash
   sudo apt-get install supervisor
   ```

2. Create a new supervisor configuration file:

   ```bash
   sudo nano /etc/supervisor/conf.d/appointment_reminders.conf
   ```

3. Add the following configuration:

   ```
   [program:appointment_reminders]
   command=bash -c "cd /path/to/your/django/project && /path/to/your/virtualenv/bin/python manage.py send_appointment_reminders"
   directory=/path/to/your/django/project
   user=your_user
   numprocs=1
   stdout_logfile=/path/to/your/logs/appointment_reminders_out.log
   stderr_logfile=/path/to/your/logs/appointment_reminders_err.log
   autostart=false
   autorestart=false
   startsecs=0
   stopwaitsecs=600
   stopasgroup=true
   killasgroup=true
   ```

4. Set up a cron job to trigger the supervisor task hourly:
   ```
   0 * * * * supervisorctl start appointment_reminders
   ```

## Considerations for Database Load

In a high-traffic application, consider:

1. Adding indexes to your database for the fields used in filtering (appointment date, time, and status)
2. Implementing rate limiting or staggering for email sending
3. Splitting the task into batches if dealing with a large number of appointments

## Troubleshooting

1. **Cron job not running:**

   - Check cron service status: `service cron status`
   - Verify cron log: `grep CRON /var/log/syslog`

2. **Script executes but no emails sent:**
   - Check Django email settings
   - Verify email backend configuration
   - Try running the command manually to see detailed output
