# conftest.py

import pytest
from datetime import time
from django.contrib.auth import get_user_model
from rest_framework.test import APIClient

User = get_user_model()


@pytest.fixture
def api_client():
    return APIClient()


@pytest.fixture
def staff_user(db):
    return User.objects.create_user(
        email="<EMAIL>",
        phone_number="1234567890",  # Add phone_number here
        role="owner",
        is_staff=True,
    )


@pytest.fixture
def non_staff_user(db):
    return User.objects.create_user(
        email="<EMAIL>",
        phone_number="1234567890",  # Add phone_number here
        role="customer",
    )


@pytest.fixture
def admin_user():
    """Create an admin user with the receptionist role."""
    user = User.objects.create(
        email="<EMAIL>",
        phone_number="+1234567890",
        first_name="Admin",
        last_name="User",
        role="receptionist",
        is_staff=True,
    )
    user.set_password("password123")
    user.save()
    return user


@pytest.fixture
def owner_user():
    """Create a user with the owner role."""
    user = User.objects.create(
        email="<EMAIL>",
        phone_number="+9876543210",
        first_name="Owner",
        last_name="User",
        role="owner",
        is_staff=True,
    )
    user.set_password("password123")
    user.save()
    return user


@pytest.fixture
def therapist_user():
    """Create a user with the therapist role."""
    user = User.objects.create(
        email="<EMAIL>",
        phone_number="+5555555555",
        first_name="Therapist",
        last_name="User",
        role="therapist",
    )
    user.set_password("password123")
    user.save()
    return user


@pytest.fixture
def customer_user():
    """Create a user with the customer role."""
    user = User.objects.create(
        email="<EMAIL>",
        phone_number="+1111111111",
        first_name="Customer",
        last_name="User",
        role="customer",
    )
    user.set_password("password123")
    user.save()
    return user
