# utils/sales_calculations.py
from decimal import Decimal
from datetime import datetime, timedelta
from django.db.models import Sum, Q
from django.utils import timezone

from api.retail.models import ProductSale, Expense, CashWithdrawal
from api.appointments.models import Sale


def calculate_service_sales(date=None, start_date=None, end_date=None, location=None):
    """
    Calculate service sales for a specific date or date range.
    If location is None, includes all locations.
    Returns gross amount.
    """
    query = Sale.objects.filter(sale_type="service")

    if date:
        query = query.filter(created_at__date=date)
    elif start_date and end_date:
        query = query.filter(created_at__date__range=[start_date, end_date])
    elif start_date:
        query = query.filter(created_at__date__gte=start_date)
    elif end_date:
        query = query.filter(created_at__date__lte=end_date)

    if location:
        query = query.filter(location=location)

    total = query.aggregate(total=Sum("total_price"))["total"] or Decimal("0.00")
    return total


def calculate_package_sales(date=None, start_date=None, end_date=None, location=None):
    """
    Calculate package sales for a specific date or date range.
    If location is None, includes all locations.
    Returns gross amount.
    """
    query = Sale.objects.filter(sale_type="package")

    if date:
        query = query.filter(created_at__date=date)
    elif start_date and end_date:
        query = query.filter(created_at__date__range=[start_date, end_date])
    elif start_date:
        query = query.filter(created_at__date__gte=start_date)
    elif end_date:
        query = query.filter(created_at__date__lte=end_date)

    if location:
        query = query.filter(location=location)

    total = query.aggregate(total=Sum("total_price"))["total"] or Decimal("0.00")
    return total


def calculate_product_sales(date=None, start_date=None, end_date=None, location=None):
    """
    Calculate product sales for a specific date or date range.
    If location is None, includes all locations.
    Returns gross amount.
    """
    query = ProductSale.objects.filter(status="COMPLETED")

    if date:
        query = query.filter(created_at__date=date)
    elif start_date and end_date:
        query = query.filter(created_at__date__range=[start_date, end_date])
    elif start_date:
        query = query.filter(created_at__date__gte=start_date)
    elif end_date:
        query = query.filter(created_at__date__lte=end_date)

    if location:
        query = query.filter(location=location)

    total = query.aggregate(total=Sum("total_amount"))["total"] or Decimal("0.00")
    return total


def calculate_gross_sales(date=None, start_date=None, end_date=None, location=None):
    """
    Calculate total gross sales (services + packages + products).
    """
    service_sales = calculate_service_sales(date, start_date, end_date, location)
    package_sales = calculate_package_sales(date, start_date, end_date, location)
    product_sales = calculate_product_sales(date, start_date, end_date, location)

    return service_sales + package_sales + product_sales


def calculate_payment_method_totals(
    date=None, start_date=None, end_date=None, location=None
):
    """
    Calculate sales by payment method.
    Returns dict with cash, card, and link totals.
    """
    # Service and Package sales
    sale_query = Sale.objects.all()

    if date:
        sale_query = sale_query.filter(created_at__date=date)
    elif start_date and end_date:
        sale_query = sale_query.filter(created_at__date__range=[start_date, end_date])
    elif start_date:
        sale_query = sale_query.filter(created_at__date__gte=start_date)
    elif end_date:
        sale_query = sale_query.filter(created_at__date__lte=end_date)

    if location:
        sale_query = sale_query.filter(location=location)

    # Product sales
    product_query = ProductSale.objects.filter(status="COMPLETED")

    if date:
        product_query = product_query.filter(created_at__date=date)
    elif start_date and end_date:
        product_query = product_query.filter(
            created_at__date__range=[start_date, end_date]
        )
    elif start_date:
        product_query = product_query.filter(created_at__date__gte=start_date)
    elif end_date:
        product_query = product_query.filter(created_at__date__lte=end_date)

    if location:
        product_query = product_query.filter(location=location)

    # Calculate totals
    cash_sales = sale_query.filter(payment_method="cash").aggregate(
        total=Sum("total_price")
    )["total"] or Decimal("0.00")
    cash_sales += product_query.filter(payment_method="CASH").aggregate(
        total=Sum("total_amount")
    )["total"] or Decimal("0.00")

    card_sales = sale_query.filter(payment_method="card").aggregate(
        total=Sum("total_price")
    )["total"] or Decimal("0.00")
    card_sales += product_query.filter(payment_method="CARD").aggregate(
        total=Sum("total_amount")
    )["total"] or Decimal("0.00")

    link_sales = sale_query.filter(payment_method="link").aggregate(
        total=Sum("total_price")
    )["total"] or Decimal("0.00")
    link_sales += product_query.filter(payment_method="ONLINE_LINK").aggregate(
        total=Sum("total_amount")
    )["total"] or Decimal("0.00")

    return {"cash": cash_sales, "card": card_sales, "link": link_sales}


def calculate_deductions(gross_sales, payment_totals):
    """
    Calculate all deductions (VAT, card charges, link charges).
    Returns dict with individual deductions and total.
    """
    vat = gross_sales * Decimal("0.05")  # 5% VAT
    card_charges = payment_totals["card"] * Decimal("0.021")  # 2.1%
    link_charges = payment_totals["link"] * Decimal("0.03")  # 3%

    return {
        "vat": vat,
        "card_charges": card_charges,
        "link_charges": link_charges,
        "total": vat + card_charges + link_charges,
    }


def calculate_net_sales(gross_sales, deductions):
    """
    Calculate net sales (gross - all deductions).
    """
    return gross_sales - deductions["total"]


def get_all_sales_items(date=None, start_date=None, end_date=None, location=None):
    """
    Get all individual sale items (services and packages).
    """
    query = Sale.objects.all()

    if date:
        query = query.filter(created_at__date=date)
    elif start_date and end_date:
        query = query.filter(created_at__date__range=[start_date, end_date])
    elif start_date:
        query = query.filter(created_at__date__gte=start_date)
    elif end_date:
        query = query.filter(created_at__date__lte=end_date)

    if location:
        query = query.filter(location=location)

    return (
        query.select_related(
            "user",
            "appointment",
            "appointment__customer",
            "appointment__therapist",
            "appointment__therapist__user",
            "package_option",
            "package_option__package",
            "discount",
        )
        .prefetch_related("appointment__appointment_services__service")
        .order_by("-created_at")
    )


def get_all_product_sales(date=None, start_date=None, end_date=None, location=None):
    """
    Get all individual product sales.
    """
    query = ProductSale.objects.filter(status="COMPLETED")

    if date:
        query = query.filter(created_at__date=date)
    elif start_date and end_date:
        query = query.filter(created_at__date__range=[start_date, end_date])
    elif start_date:
        query = query.filter(created_at__date__gte=start_date)
    elif end_date:
        query = query.filter(created_at__date__lte=end_date)

    if location:
        query = query.filter(location=location)

    # Changed from 'items__product' to 'sale_items__product'
    return query.prefetch_related("sale_items__product").order_by("-created_at")


def get_all_expenses(date=None, start_date=None, end_date=None, location=None):
    """
    Get all individual expenses filtered by location through cash_register relationship.
    """
    query = Expense.objects.all()

    # Filter by date
    if date:
        query = query.filter(created_at__date=date)
    elif start_date and end_date:
        query = query.filter(created_at__date__range=[start_date, end_date])
    elif start_date:
        query = query.filter(created_at__date__gte=start_date)
    elif end_date:
        query = query.filter(created_at__date__lte=end_date)

    # Filter by location through cash_register relationship
    if location:
        query = query.filter(cash_register__location=location)

    return query.order_by("-created_at")


def get_all_withdrawals(date=None, start_date=None, end_date=None, location=None):
    """
    Get all individual cash withdrawals filtered by location through cash_register relationship.
    """
    query = CashWithdrawal.objects.all()

    # Filter by date
    if date:
        query = query.filter(created_at__date=date)
    elif start_date and end_date:
        query = query.filter(created_at__date__range=[start_date, end_date])
    elif start_date:
        query = query.filter(created_at__date__gte=start_date)
    elif end_date:
        query = query.filter(created_at__date__lte=end_date)

    # Filter by location through cash_register relationship
    if location:
        query = query.filter(cash_register__location=location)

    return query.order_by("-created_at")


def calculate_expenses_total(date=None, start_date=None, end_date=None, location=None):
    """
    Calculate total expenses filtered by location.
    """
    expenses = get_all_expenses(date, start_date, end_date, location)
    total = expenses.aggregate(total=Sum("amount"))["total"] or Decimal("0.00")
    return total


def calculate_withdrawals_total(date=None, start_date=None, end_date=None, location=None):
    """
    Calculate total withdrawals filtered by location.
    """
    withdrawals = get_all_withdrawals(date, start_date, end_date, location)
    total = withdrawals.aggregate(total=Sum("amount"))["total"] or Decimal("0.00")
    return total


def get_monthly_sales_data(month, year, location=None):
    """
    Get all sales data for a specific month.
    Returns both summary and detailed items.
    """
    # Calculate date range
    start_date = datetime(year, month, 1).date()
    if month == 12:
        end_date = datetime(year + 1, 1, 1).date() - timedelta(days=1)
    else:
        end_date = datetime(year, month + 1, 1).date() - timedelta(days=1)

    # Don't go beyond today
    today = timezone.now().date()
    end_date = min(end_date, today)

    # Calculate totals
    service_sales = calculate_service_sales(
        start_date=start_date, end_date=end_date, location=location
    )
    package_sales = calculate_package_sales(
        start_date=start_date, end_date=end_date, location=location
    )
    product_sales = calculate_product_sales(
        start_date=start_date, end_date=end_date, location=location
    )
    gross_sales = service_sales + package_sales + product_sales

    # Payment methods
    payment_totals = calculate_payment_method_totals(
        start_date=start_date, end_date=end_date, location=location
    )

    # Deductions
    deductions = calculate_deductions(gross_sales, payment_totals)
    net_sales = calculate_net_sales(gross_sales, deductions)

    # Expenses and withdrawals - pass location instead of cash_register for monthly data
    expenses_total = calculate_expenses_total(
        start_date=start_date, end_date=end_date, location=location
    )
    withdrawals_total = calculate_withdrawals_total(
        start_date=start_date, end_date=end_date, location=location
    )

    # Get all items
    all_sales = get_all_sales_items(
        start_date=start_date, end_date=end_date, location=location
    )
    all_product_sales = get_all_product_sales(
        start_date=start_date, end_date=end_date, location=location
    )
    all_expenses = get_all_expenses(
        start_date=start_date, end_date=end_date, location=location
    )
    all_withdrawals = get_all_withdrawals(
        start_date=start_date, end_date=end_date, location=location
    )

    return {
        "summary": {
            "service_sales": service_sales,
            "package_sales": package_sales,
            "product_sales": product_sales,
            "gross_sales": gross_sales,
            "payment_totals": payment_totals,
            "deductions": deductions,
            "net_sales": net_sales,
            "expenses_total": expenses_total,
            "withdrawals_total": withdrawals_total,
        },
        "items": {
            "sales": all_sales,
            "product_sales": all_product_sales,
            "expenses": all_expenses,
            "withdrawals": all_withdrawals,
        },
        "meta": {
            "month": month,
            "year": year,
            "month_name": start_date.strftime("%B"),
            "start_date": start_date,
            "end_date": end_date,
            "days_in_period": (end_date - start_date).days + 1,
        },
    }
