from django.core.management.base import BaseCommand
from django.utils import timezone
from django.db import transaction
from decimal import Decimal
from api.retail.models import CashRegister  # Replace with your actual app name
import datetime


class Command(BaseCommand):
    help = "Generate cash register entries for the next 500 days"

    def add_arguments(self, parser):
        parser.add_argument(
            "--start-date",
            type=str,
            help="Starting date for generation (YYYY-MM-DD). Defaults to today if not provided.",
            required=False,
        )
        parser.add_argument(
            "--initial-balance",
            type=float,
            help="Initial balance for cash registers. Defaults to 0.00 if not provided.",
            default=1990.00,
            required=False,
        )

    def handle(self, *args, **options):
        # Determine start date
        start_date = options.get("start_date")
        if start_date:
            try:
                start_date = datetime.datetime.strptime(start_date, "%Y-%m-%d").date()
            except ValueError:
                self.stdout.write(
                    self.style.ERROR("Invalid date format. Use YYYY-MM-DD.")
                )
                return
        else:
            start_date = timezone.now().date()

        # Get initial balance
        initial_balance = Decimal(str(options.get("initial_balance", 0.00)))

        # Get locations from the model's choices
        locations = dict(CashRegister.LOCATION_CHOICES)

        # Prepare cash registers to be created
        cash_registers_to_create = []

        # Generate for next 500 days
        for days in range(500):
            current_date = start_date + datetime.timedelta(days=days)

            for location_code, location_name in locations.items():
                # Check if cash register already exists for this date and location
                existing = CashRegister.objects.filter(
                    date=current_date, location=location_code
                ).exists()

                if not existing:
                    cash_register = CashRegister(
                        date=current_date,
                        location=location_code,
                        current_balance=initial_balance,
                        notes=f"Auto-generated for {location_name}",
                    )
                    cash_registers_to_create.append(cash_register)
                    print(cash_register.date, cash_register.location)

        # Bulk create to improve performance
        with transaction.atomic():
            CashRegister.objects.bulk_create(cash_registers_to_create)

        self.stdout.write(
            self.style.SUCCESS(
                f"Successfully generated {len(cash_registers_to_create)} cash register entries "
                f"from {start_date} for the next 500 days"
            )
        )
