# Generated by Django 4.0 on 2024-12-14 08:45

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('appointments', '0002_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='packagebalance',
            name='appointment',
            field=models.OneToOneField(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='package_balance', to='appointments.appointment'),
        ),
        migrations.AddField(
            model_name='packagebalance',
            name='time_deducted',
            field=models.PositiveIntegerField(default=0, help_text='Time deducted from the package in minutes'),
            preserve_default=False,
        ),
        migrations.AlterField(
            model_name='packagebalance',
            name='remaining_time',
            field=models.PositiveIntegerField(help_text='Remaining time in minutes after this appointment'),
        ),
    ]
