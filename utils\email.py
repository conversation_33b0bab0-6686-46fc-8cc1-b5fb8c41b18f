import logging
from django.core.mail import send_mail, BadHeaderError
from django.template.loader import render_to_string
from django.conf import settings

# info_logger = logging.getLogger('info_logger')
# exception_logger = logging.getLogger('exception_logger')


def send_email(subject, message, recipient_list, html_template=None, context=None):
    from_email = settings.DEFAULT_FROM_EMAIL
    html_message = None

    if html_template and context:
        html_message = render_to_string(html_template, context)

    try:
        send_mail(subject, message, from_email, recipient_list, html_message=html_message)
        # info_logger.info(f'Email sent to {", ".join(recipient_list)}')
    except BadHeaderError as e:
        # exception_logger.error(
        #     f'Invalid header found when sending email to {", ".join(recipient_list)}: {str(e)}'
        # )
        raise
    except Exception as e:
        # exception_logger.error(
        #     f'Failed to send email to {", ".join(recipient_list)}: {str(e)}'
        # )
        raise
