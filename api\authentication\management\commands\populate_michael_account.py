from django.core.management.base import BaseCommand
from datetime import date, time, timedelta
from api.staff.models import TherapistProfile
from api.appointments.models import Appointment, AppointmentService
from django.contrib.auth import get_user_model
from api.services.models import Service, ServiceDuration, ServicePackage, PackageOption

User = get_user_model()

THERAPISTS = [
    {"name": "<PERSON>sman <PERSON>dan", "phone_number": "+971 234 124"},
    {"name": "<PERSON>", "phone_number": "+971 234 125"},
    {"name": "<PERSON>", "phone_number": "+971 234 126"},
    {"name": "<PERSON>", "phone_number": "+971 234 128"},
]

APPOINTMENTS = [
    {
        "therapist_name": "<PERSON><PERSON>",
        "date": "2024-12-23",
        "time": "09:30",
        "customer_email": "<EMAIL>",
        "duration": 30,
        "phone_number": "+971 234 328",
    },
    {
        "therapist_name": "<PERSON><PERSON>",
        "date": "2024-12-24",
        "time": "10:30",
        "customer_email": "<EMAIL>",
        "duration": 60,
        "phone_number": "+971 234 328",
    },
    {
        "therapist_name": "Omar Kyauta",
        "date": "2024-12-25",
        "time": "11:00",
        "customer_email": "<EMAIL>",
        "duration": 40,
        "phone_number": "+971 234 328",
    },
]


# python manage.py populate_therapists_and_appointments
class Command(BaseCommand):
    help = "Populate therapists and sample appointments"

    def handle(self, *args, **kwargs):

        Appointment.objects.all().delete()
        # Add appointments
        for appointment_data in APPOINTMENTS:
            try:
                therapist = TherapistProfile.objects.get(
                    user__email=f"{appointment_data['therapist_name'].replace(' ', '').lower()}@example.com"
                )
                customer, _ = User.objects.get_or_create(
                    email=appointment_data["customer_email"],
                    phone_number=appointment_data["phone_number"],
                    defaults={
                        "first_name": "Enitan",
                        "last_name": "Peter",
                    },
                )
                appointment = Appointment.objects.create(
                    customer=customer,
                    therapist=therapist,
                    date=appointment_data["date"],
                    time=appointment_data["time"],
                    total_duration=appointment_data["duration"],
                    status="booked",
                )

                service = Service.objects.get(name="Stretching")
                AppointmentService.objects.create(
                    appointment=appointment,
                    service=service,
                    duration=appointment_data["duration"],
                )
                self.stdout.write(
                    self.style.SUCCESS(
                        f"Appointment created for {appointment_data['therapist_name']} on {appointment_data['date']}"
                    )
                )
            except Exception as e:
                self.stderr.write(self.style.ERROR(f"Failed to create appointment: {e}"))
