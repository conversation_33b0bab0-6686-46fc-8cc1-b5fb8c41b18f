from datetime import datetime, timedelta
from django.utils.dateparse import parse_date
from rest_framework.exceptions import ValidationError
from rest_framework import status
from rest_framework import generics
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.views import APIView
from django.shortcuts import get_object_or_404
from django.contrib.auth import get_user_model
from api.appointments.models import (
    Activity,
    Appointment,
    AppointmentService,
    UserPackage,
)
from api.services.models import Service, ServiceDuration, PackageOption
from api.appointments.serializers import (
    AppointmentSerializer,
    AddMinutesToAppointmentSerializer,
    AppointmentAdditionalServiceSerializer,
    AppointmentAdditionalService,
)

import pytz

from api.core.permissions import IsOwnerOrReceptionist
from api.staff.models import TherapistProfile
import traceback
from decimal import Decimal
from rest_framework.decorators import api_view, permission_classes
from django.db import transaction
from django.utils import timezone

User = get_user_model()

# Constants
BREAK_TIME = 10  # minutes break between appointments


from utils.logging import (
    api_logger,
    logger,
    log_request_data,
    log_response_data,
    log_error,
)


class AppointmentListCreateView(generics.ListCreateAPIView):
    queryset = Appointment.objects.all()
    serializer_class = AppointmentSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        user = self.request.user
        allowed_statuses = ["booked", "check_in"]

        api_logger.info(
            f"🔍 User {user.id} with role {user.role} retrieving appointments"
        )

        if user.role == "customer":
            queryset = Appointment.objects.filter(
                customer=user, status__in=allowed_statuses
            ).order_by("-created_at")
            api_logger.debug(
                f"Customer {user.id} retrieved {queryset.count()} appointments"
            )
            return queryset
        elif user.role == "therapist":
            queryset = Appointment.objects.filter(
                therapist__user=user, status__in=allowed_statuses
            ).order_by("-created_at")
            api_logger.debug(
                f"Therapist {user.id} retrieved {queryset.count()} appointments"
            )
            return queryset
        elif user.role in ["owner", "receptionist"]:
            queryset = Appointment.objects.all().order_by("-created_at")
            api_logger.debug(f"Admin {user.id} retrieved {queryset.count()} appointments")
            return queryset

        api_logger.warning(
            f"User {user.id} with unsupported role {user.role} attempted to access appointments"
        )
        return Appointment.objects.none()

    def perform_create(self, serializer):
        try:
            # Log request data
            log_request_data(self.request, "📥 Appointment Creation Request")

            data = self.request.data
            service_type = data.get("serviceType")
            services = data.get("services", [])
            therapist_id = data.get("therapist_id")
            location = data.get("location")
            notes = data.get("notes")
            package_option = None
            user_package = None

            api_logger.info(
                f"🔄 Creating appointment of type {service_type} with {len(services)} services"
            )

            # Determine the customer
            if self.request.user.role in ["owner", "receptionist"]:
                customer_data = data.get("customer")  # Expecting customer object
                if not customer_data:
                    api_logger.warning("❌ Customer data missing from admin booking")
                    raise ValidationError({"detail": "Customer data is required."})

                email = customer_data.get("email", "").strip().lower()
                phone_number = customer_data.get("phone_number", "").strip()

                api_logger.info(
                    f"🔍 Finding customer by email: {email} or phone: {phone_number}"
                )
                customer = None
                if email:
                    customer = User.objects.filter(email=email).first()
                if not customer and phone_number:
                    customer = User.objects.filter(phone_number=phone_number).first()
                if not customer:
                    api_logger.info(f"🔄 Creating new customer with email: {email}")
                    customer = User.objects.create(
                        email=email,
                        first_name=customer_data.get("first_name", ""),
                        last_name=customer_data.get("last_name", ""),
                        phone_number=phone_number,
                        role="customer",
                    )
                    customer.set_unusable_password()
                    customer.save()
                    api_logger.info(f"✅ New customer created with ID: {customer.id}")
                else:
                    api_logger.info(f"✅ Using existing customer with ID: {customer.id}")
            else:
                customer = self.request.user
                api_logger.info(f"🔍 Customer is request user with ID: {customer.id}")

            # Calculate totals for all requested services.
            total_duration = 0
            total_price = Decimal("0")
            for service_data in services:
                total_duration += service_data["time"]
                total_price += Decimal(service_data["price"])

            api_logger.debug(f"Initial total: {total_duration} minutes, ${total_price}")

            # Handle package or mypackage booking if applicable.
            if service_type in ["package", "mypackage"]:
                api_logger.info(f"🔄 Processing {service_type} booking")
                if service_type == "package":
                    api_logger.debug(
                        f"Processing new package booking for customer {customer.id}"
                    )
                    total_duration, total_price, user_package = (
                        self.handle_new_package_booking(data, services, customer)
                    )
                else:
                    api_logger.debug(
                        f"Processing existing package booking for customer {customer.id}"
                    )
                    total_duration, total_price, user_package = (
                        self.handle_mypackage_booking(data, services, customer)
                    )
                api_logger.info(
                    f"✅ Package processed: {total_duration} minutes, ${total_price}"
                )

            # Retrieve the selected therapist.
            try:
                therapist = TherapistProfile.objects.get(id=therapist_id)
                api_logger.info(
                    f"🔍 Selected therapist ID: {therapist_id}, Name: {therapist.user.first_name} {therapist.user.last_name}"
                )
            except TherapistProfile.DoesNotExist:
                api_logger.error(f"❌ Therapist with ID {therapist_id} not found")
                raise ValidationError(
                    {"detail": f"Therapist with ID {therapist_id} not found"}
                )

            # For "service" type bookings, check which services the therapist cannot perform.
            missing_services = []
            if service_type == "service":
                api_logger.debug(f"Checking therapist services compatibility")
                therapist_services = therapist.services.all()
                for service_data in services:
                    # Check if this is a custom duration service
                    is_custom = service_data.get("isCustom", False)

                    if is_custom:
                        # For custom duration, find the service by name
                        service_name = service_data.get("name")
                        try:
                            service_obj = Service.objects.get(name=service_name)
                            if not service_obj.is_public:
                                continue
                            # Check if therapist can perform this service
                            if service_obj not in therapist_services:
                                api_logger.warning(
                                    f"⚠️ Therapist {therapist_id} cannot perform custom service: {service_name}"
                                )
                                missing_services.append(
                                    {
                                        "id": service_obj.id,
                                        "name": service_obj.name,
                                        "duration": service_data["time"],
                                        "price": service_data["price"],
                                    }
                                )
                        except Service.DoesNotExist:
                            api_logger.error(f"❌ Service {service_name} does not exist")
                            raise ValidationError(
                                {"detail": f"Service {service_name} does not exist"}
                            )
                    else:
                        # Original code for standard durations
                        try:
                            service_duration_obj = ServiceDuration.objects.get(
                                id=service_data["id"]
                            )
                            if not service_duration_obj.service.is_public:
                                continue
                            if service_duration_obj.service not in therapist_services:
                                api_logger.warning(
                                    f"⚠️ Therapist {therapist_id} cannot perform service: {service_duration_obj.service.name}"
                                )
                                missing_services.append(
                                    {
                                        "id": service_duration_obj.service.id,
                                        "name": service_duration_obj.service.name,
                                        "duration": service_data["time"],
                                        "price": service_data["price"],
                                    }
                                )
                        except ServiceDuration.DoesNotExist:
                            api_logger.error(
                                f"❌ ServiceDuration with ID {service_data['id']} not found"
                            )
                            raise ValidationError(
                                {
                                    "detail": f"Service duration with ID {service_data['id']} not found"
                                }
                            )

                if missing_services:
                    missing_names = ", ".join([s["name"] for s in missing_services])
                    api_logger.warning(
                        f"⚠️ Appointment will need manual split due to missing services: {missing_names}"
                    )

            # Create the appointment with all requested totals and missing services info.
            api_logger.info(
                f"🔄 Creating appointment for date: {data['date']}, time: {data['time']}"
            )
            appointment = Appointment.objects.create(
                date=data["date"],
                time=data["time"],
                customer=customer,
                total_duration=total_duration,
                total_price=total_price,
                package_option=package_option,
                user_package=user_package,
                therapist=therapist,
                location=location,
                notes=notes,
                needs_manual_split=True if missing_services else False,
                missing_services=missing_services if missing_services else None,
            )
            api_logger.info(f"✅ Appointment created with ID: {appointment.id}")

            if service_type == "service":
                api_logger.debug(
                    f"Adding {len(services)} services to appointment {appointment.id}"
                )
                # Create AppointmentService entries for ALL requested services.
                for service_data in services:
                    is_custom = service_data.get("isCustom", False)

                    if is_custom:
                        # For custom duration, find the service by name
                        service_name = service_data.get("name")
                        try:
                            service_obj = Service.objects.get(name=service_name)
                            # Create appointment service with custom duration and price
                            appointment_service = AppointmentService.objects.create(
                                appointment=appointment,
                                service=service_obj,
                                duration=service_data["time"],
                                price=Decimal(service_data["price"]),
                            )
                            api_logger.debug(
                                f"Added custom service: {service_name}, duration: {service_data['time']}, price: {service_data['price']} to appointment {appointment.id}"
                            )
                        except Service.DoesNotExist:
                            api_logger.error(f"❌ Service {service_name} does not exist")
                            raise ValidationError(
                                {"detail": f"Service {service_name} does not exist"}
                            )
                    else:
                        # Original code for standard durations
                        try:
                            service_duration_obj = ServiceDuration.objects.get(
                                id=service_data["id"]
                            )
                            appointment_service = AppointmentService.objects.create(
                                appointment=appointment,
                                service=service_duration_obj.service,
                                duration=service_data["time"],
                                price=Decimal(service_data["price"]),
                            )
                            api_logger.debug(
                                f"Added service: {service_duration_obj.service.name}, duration: {service_data['time']}, price: {service_data['price']} to appointment {appointment.id}"
                            )
                        except ServiceDuration.DoesNotExist:
                            api_logger.error(
                                f"❌ ServiceDuration with ID {service_data['id']} not found"
                            )
                            raise ValidationError(
                                {
                                    "detail": f"Service duration with ID {service_data['id']} not found"
                                }
                            )
            # Log activity.
            api_logger.info(f"🔄 Creating activity records for booking")
            if service_type == "service":
                if missing_services:
                    missing_names = ", ".join([s["name"] for s in missing_services])
                    Activity.objects.create(
                        customer=self.request.user,
                        activity_type="booking",
                        description=f"Appointment flagged for manual split due to missing services: {missing_names}",
                    )
                    api_logger.info(f"✅ Created activity record for manual split")
                else:
                    for service_data in services:
                        is_custom = service_data.get("isCustom", False)

                        if is_custom:
                            service_name = service_data.get("name")
                            Activity.objects.create(
                                customer=self.request.user,
                                activity_type="booking",
                                description=f"{self.request.user.first_name} {self.request.user.last_name} booked {service_name} service with custom duration ({service_data['time']} min)",
                            )
                            api_logger.debug(
                                f"Created activity record for custom service booking: {service_name}"
                            )
                        else:
                            try:
                                service_duration_obj = ServiceDuration.objects.get(
                                    id=service_data["id"]
                                )
                                Activity.objects.create(
                                    customer=self.request.user,
                                    activity_type="booking",
                                    description=f"{self.request.user.first_name} {self.request.user.last_name} booked {service_duration_obj.service.name} service",
                                )
                                api_logger.debug(
                                    f"Created activity record for service booking: {service_duration_obj.service.name}"
                                )
                            except ServiceDuration.DoesNotExist:
                                api_logger.error(
                                    f"❌ ServiceDuration with ID {service_data['id']} not found when creating activity"
                                )
            else:
                Activity.objects.create(
                    customer=self.request.user,
                    activity_type="booking",
                    description=f"{self.request.user.first_name} {self.request.user.last_name} booked a package",
                )
                api_logger.info(f"✅ Created activity record for package booking")

            try:
                api_logger.info(f"🔄 Preparing appointment confirmation email")
                serialized_appointment = AppointmentSerializer(appointment).data
                # send_appointment_confirmation_email(serialized_appointment)
                api_logger.info(f"✅ Appointment confirmation email would be sent")
            except Exception as email_error:
                # Log the error but don't fail the booking process
                log_error(
                    email_error,
                    f"Email confirmation for appointment ID: {appointment.id}",
                    log_full_trace=True,
                )

            api_logger.info(
                f"✅ Appointment booking process completed successfully for ID: {appointment.id}"
            )
            return appointment

        except Exception as e:
            log_error(e, "AppointmentListCreateView.perform_create", log_full_trace=True)
            raise e

    def dispatch(self, request, *args, **kwargs):
        """Override dispatch to log requests and responses"""
        log_request_data(request, "📥 Appointment API Request")
        response = super().dispatch(request, *args, **kwargs)
        log_response_data(response, "📤 Appointment API Response")
        return response

    def handle_service_booking(self, services):
        total_duration = 0
        total_price = Decimal("0")
        api_logger.debug(
            f"🔄 Calculating service booking totals for {len(services)} services"
        )
        for service_data in services:
            total_duration += service_data["time"]
            total_price += Decimal(service_data["price"])
        api_logger.debug(
            f"✅ Service booking totals: {total_duration} minutes, ${total_price}"
        )
        return total_duration, total_price

    def handle_new_package_booking(self, data, services, customer):
        api_logger.info(f"🔄 Handling new package booking for customer {customer.id}")
        if services and len(services) != 1:
            api_logger.warning(f"❌ Multiple package services selected: {len(services)}")
            raise ValidationError(
                {"detail": "Only one service package can be booked at a time."}
            )
        package_data = services[0]
        package_option_id = package_data["id"]

        try:
            package_option = PackageOption.objects.get(id=package_option_id)
            api_logger.debug(
                f"Package option found: {package_option.package.name}, ID: {package_option_id}"
            )
        except PackageOption.DoesNotExist:
            api_logger.error(f"❌ Package option with ID {package_option_id} not found")
            raise ValidationError(
                {"detail": f"Package option with ID {package_option_id} not found"}
            )

        package_total_time = int(package_data["time"])
        try:
            total_duration = int(data.get("duration"))
            api_logger.debug(
                f"Using duration: {total_duration} from requested total: {package_total_time}"
            )
        except (TypeError, ValueError):
            api_logger.warning(f"❌ Invalid duration provided: {data.get('duration')}")
            raise ValidationError({"detail": "Invalid duration provided."})

        total_price = Decimal(package_option.price)

        api_logger.debug(
            f"🔍 Checking for existing active packages for customer {customer.id}"
        )

        remaining_time = package_total_time - total_duration
        if remaining_time < 0:
            api_logger.warning(
                f"❌ Booked duration ({total_duration}) exceeds package total time ({package_total_time})"
            )
            raise ValidationError(
                {"detail": "Booked duration exceeds package total time."}
            )

        api_logger.info(f"🔄 Creating user package for customer {customer.id}")
        user_package = UserPackage.objects.create(
            user=customer,
            package_option=package_option,
            total_time=package_total_time,
            remaining_time=remaining_time,
            time_deducted=total_duration,
            active=True,
        )
        api_logger.info(
            f"✅ Created user package ID: {user_package.id}, remaining time: {remaining_time}"
        )
        return total_duration, total_price, user_package

    def handle_mypackage_booking(self, data, services, customer):
        api_logger.info(
            f"🔄 Handling existing package booking for customer {customer.id}"
        )
        user_package_id = data.get("user_package_id")
        if user_package_id:
            api_logger.debug(f"Looking for specific package ID: {user_package_id}")
            try:
                active_package = UserPackage.objects.get(
                    id=user_package_id, user=customer, active=True
                )
                api_logger.debug(f"Found specified package ID: {user_package_id}")
            except UserPackage.DoesNotExist:
                api_logger.warning(
                    f"❌ User package with ID {user_package_id} not found for customer {customer.id}"
                )
                raise ValidationError(
                    {"detail": "The specified active package does not exist."}
                )
        else:
            api_logger.debug(f"No package ID specified, looking for any active package")
            active_package = UserPackage.objects.filter(
                user=customer, active=True
            ).first()
            if not active_package:
                api_logger.warning(
                    f"❌ No active package found for customer {customer.id}"
                )
                raise ValidationError(
                    {"detail": "No active package found. Please purchase a new package."}
                )
            api_logger.debug(f"Found active package ID: {active_package.id}")

        try:
            total_duration = int(data.get("duration"))
            api_logger.debug(f"Requested duration: {total_duration} minutes")
        except (TypeError, ValueError):
            api_logger.warning(f"❌ Invalid booking duration: {data.get('duration')}")
            raise ValidationError({"detail": "Invalid booking duration."})

        total_price = Decimal("0")

        api_logger.debug(
            f"Checking if package has enough time: remaining {active_package.remaining_time}, needed {total_duration}"
        )
        if active_package.remaining_time < total_duration:
            api_logger.warning(
                f"❌ Not enough time in package: remaining {active_package.remaining_time}, needed {total_duration}"
            )
            raise ValidationError(
                {
                    "detail": f"Not enough remaining time in your active package. Remaining: {active_package.remaining_time} minutes."
                }
            )

        api_logger.info(f"🔄 Updating user package ID: {active_package.id}")
        active_package.remaining_time -= total_duration
        active_package.time_deducted += total_duration
        if active_package.remaining_time <= 0:
            active_package.active = False
            api_logger.info(
                f"Package ID: {active_package.id} depleted and marked inactive"
            )
        active_package.save()
        api_logger.info(
            f"✅ Updated package ID: {active_package.id}, remaining time: {active_package.remaining_time}"
        )
        return total_duration, total_price, active_package


class SplitAppointmentView(APIView):
    permission_classes = [
        IsAuthenticated,
        IsOwnerOrReceptionist,
    ]  # Optionally restrict to receptionist/owner roles

    def post(self, request, *args, **kwargs):
        log_request_data(request, "📥 Split Appointment Request")
        api_logger.info("🔄 Starting appointment split process")

        try:
            appointment_id = request.data.get("appointment_id")
            new_therapist_id = request.data.get("therapist_id")
            break_time = int(request.data.get("break_time", 0))  # default 0 minutes break

            api_logger.info(
                f"Split request details - Appointment:{appointment_id}, New Therapist:{new_therapist_id}, Break:{break_time}min"
            )

            if not appointment_id or not new_therapist_id:
                api_logger.warning(
                    f"❌ Missing required fields in split request - appointment_id:{appointment_id}, therapist_id:{new_therapist_id}"
                )
                response = Response(
                    {"detail": "appointment_id and therapist_id are required."},
                    status=400,
                )
                log_response_data(
                    response, "📤 Split Appointment Response (Validation Error)"
                )
                return response

            appointment = Appointment.objects.get(id=appointment_id)
            api_logger.debug(
                f"🔍 Found appointment - ID:{appointment_id}, Customer:{appointment.customer.id}"
            )

            # Check if the appointment requires splitting.
            if not appointment.needs_manual_split or not appointment.missing_services:
                api_logger.warning(
                    f"❌ Appointment {appointment_id} does not require splitting"
                )
                response = Response(
                    {"detail": "This appointment does not require splitting."}, status=400
                )
                log_response_data(response, "📤 Split Appointment Response (Not Needed)")
                return response

            new_therapist = TherapistProfile.objects.get(id=new_therapist_id)
            api_logger.debug(
                f"🔍 Found therapist - ID:{new_therapist_id}, Name:{new_therapist.user.first_name} {new_therapist.user.last_name}"
            )

            missing_services = appointment.missing_services  # Expecting a list of dicts
            api_logger.info(
                f"🔄 Processing {len(missing_services)} missing services to split"
            )

            # Calculate new appointment start time.
            original_datetime = datetime.combine(appointment.date, appointment.time)
            # Calculate the end time of the original appointment (after services are removed)
            remaining_services = AppointmentService.objects.filter(
                appointment=appointment
            ).exclude(service__id__in=[s["id"] for s in missing_services])
            additional_services = AppointmentAdditionalService.objects.filter(
                appointment=appointment
            )

            services_duration = sum(s.duration for s in remaining_services)
            additional_duration = sum(a.duration for a in additional_services)
            remaining_duration = services_duration + additional_duration

            # Original appointment end time
            original_end_datetime = original_datetime + timedelta(
                minutes=remaining_duration
            )
            # New appointment start time (end time + break)
            new_start_datetime = original_end_datetime + timedelta(minutes=break_time)
            new_date = new_start_datetime.date()
            new_time = new_start_datetime.time()

            api_logger.debug(
                f"🔄 New appointment time calculated - Date:{new_date}, Time:{new_time}"
            )

            # Compute totals for missing services.
            missing_total_duration = sum(s["duration"] for s in missing_services)
            missing_total_price = sum(Decimal(s["price"]) for s in missing_services)

            api_logger.debug(
                f"🔄 Missing services totals - Duration:{missing_total_duration}min, Price:{missing_total_price}"
            )

            # Create the new appointment for missing services.
            new_appointment = Appointment.objects.create(
                date=new_date,
                time=new_time,
                customer=appointment.customer,
                total_duration=missing_total_duration,
                total_price=missing_total_price,
                therapist=new_therapist,
                location=appointment.location,
                needs_manual_split=False,  # now properly assigned
                missing_services=None,
            )

            api_logger.info(f"✅ Created new appointment - ID:{new_appointment.id}")

            # Create AppointmentService entries for each missing service in the new appointment.
            for service_info in missing_services:
                service = Service.objects.get(id=service_info["id"])
                AppointmentService.objects.create(
                    appointment=new_appointment,
                    service=service,
                    duration=service_info["duration"],
                    price=Decimal(service_info["price"]),  # Added price field
                )
                api_logger.debug(
                    f"✅ Added service to new appointment - Service:{service.id}, Duration:{service_info['duration']}, Price:{service_info['price']}"
                )

            # Remove missing services from the original appointment.
            missing_service_ids = [s["id"] for s in missing_services]
            deleted_count = AppointmentService.objects.filter(
                appointment=appointment, service__id__in=missing_service_ids
            ).delete()[0]
            api_logger.info(
                f"🔄 Removed {deleted_count} services from original appointment"
            )

            # Recalculate original appointment totals based on remaining services and additional services
            remaining_services = AppointmentService.objects.filter(
                appointment=appointment
            )
            services_duration = sum(s.duration for s in remaining_services)

            # Get all additional services for the original appointment
            additional_services = AppointmentAdditionalService.objects.filter(
                appointment=appointment
            )
            additional_duration = sum(a.duration for a in additional_services)
            additional_price = sum(a.total_price for a in additional_services)

            api_logger.debug(
                f"Original appointment has {additional_services.count()} additional services with total duration: {additional_duration} min, price: ${additional_price}"
            )

            # If AppointmentService has price field
            if hasattr(AppointmentService, "price"):
                services_price = sum(s.price for s in remaining_services)
            else:
                # Fallback to using ServiceDuration
                services_price = Decimal("0.00")
                for service_obj in remaining_services:
                    service_duration = ServiceDuration.objects.filter(
                        service=service_obj.service, time=service_obj.duration
                    ).first()
                    if service_duration:
                        services_price += service_duration.price
                    else:
                        api_logger.warning(
                            f"No matching ServiceDuration found for service {service_obj.service.id} with duration {service_obj.duration}"
                        )

            additional_price = sum(a.total_price for a in additional_services)
            new_total_duration = services_duration + additional_duration
            new_total_price = services_price + additional_price

            appointment.total_duration = new_total_duration
            appointment.total_price = new_total_price
            appointment.missing_services = None
            appointment.needs_manual_split = False
            appointment.save()

            api_logger.info(
                f"✅ Updated original appointment - New Duration:{new_total_duration}min, New Price:{new_total_price}"
            )

            # Log an activity for the split.
            activity_description = f"Appointment {appointment_id} split. New appointment {new_appointment.id} created with therapist {new_therapist.user.first_name} {new_therapist.user.last_name}."

            Activity.objects.create(
                customer=request.user,
                activity_type="split",
                description=activity_description,
            )
            api_logger.info(f"✅ Created activity log for split operation")

            response = Response(
                {
                    "detail": "Appointment split successfully.",
                    "new_appointment_id": new_appointment.id,
                },
                status=200,
            )
            api_logger.info(f"✅ Appointment split completed successfully")
            log_response_data(response, "📤 Split Appointment Response (Success)")
            return response

        except Appointment.DoesNotExist:
            api_logger.error(
                f"❌ Appointment not found - ID:{request.data.get('appointment_id')}"
            )
            response = Response({"detail": "Appointment not found."}, status=404)
            log_response_data(response, "📤 Split Appointment Response (Not Found)")
            return response
        except TherapistProfile.DoesNotExist:
            api_logger.error(
                f"❌ Therapist not found - ID:{request.data.get('therapist_id')}"
            )
            response = Response({"detail": "Therapist not found."}, status=404)
            log_response_data(response, "📤 Split Appointment Response (Not Found)")
            return response
        except Exception as e:
            log_error(e, "❌ ERROR in SplitAppointmentView", log_full_trace=True)
            response = Response(
                {"detail": "An error occurred during appointment splitting."}, status=500
            )
            log_response_data(response, "📤 Split Appointment Response (Server Error)")
            return response


class AppointmentDayView(generics.ListAPIView):
    """
    View to list appointments for a specific day, filtered by user role.
    Time-based filtering is applied for customer and therapist roles.
    All time operations use Dubai time zone.
    """

    serializer_class = AppointmentSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        user = self.request.user
        # Retrieve the date from query parameters
        date_str = self.request.query_params.get("date")

        api_logger.info(
            f"🔍 Fetching appointments for Date:{date_str}, User:{user.id} (Role:{user.role})"
        )

        if not date_str:
            api_logger.warning("❌ No date parameter provided for appointment day view")
            return (
                Appointment.objects.none()
            )  # Return an empty queryset if no date provided

        try:
            # Parse the date string
            date = parse_date(date_str)
            if not date:
                api_logger.warning(f"❌ Invalid date format: {date_str}")
                raise ValueError("Invalid date format.")

            api_logger.debug(f"🔍 Parsed date: {date}")
        except ValueError:
            api_logger.error(f"❌ Failed to parse date: {date_str}")
            return Appointment.objects.none()  # Return an empty queryset for invalid date

        # Get current date and time in Dubai time zone
        dubai_timezone = pytz.timezone("Asia/Dubai")
        current_datetime = timezone.now().astimezone(dubai_timezone)
        current_date = current_datetime.date()
        current_time = current_datetime.time()

        api_logger.debug(f"🔍 Current Dubai datetime: {current_datetime}")

        # Filter appointments based on user role
        if user.role == "customer":
            api_logger.debug(
                f"🔍 Filtering appointments for customer role - User:{user.id}, Date:{date}"
            )
            queryset = Appointment.objects.filter(customer=user, date=date)

            # Apply time-based filtering if the appointment is for today
            if date == current_date:
                api_logger.debug(
                    f"🔍 Applying time filter for customer - Current Dubai time: {current_time}"
                )
                queryset = queryset.filter(time__gte=current_time)

            queryset = queryset.order_by("-created_at")

        elif user.role == "therapist":
            api_logger.debug(
                f"🔍 Filtering appointments for therapist role - User:{user.id}, Date:{date}"
            )
            queryset = Appointment.objects.filter(therapist=user, date=date)

            # Apply time-based filtering if the appointment is for today
            if date == current_date:
                api_logger.debug(
                    f"🔍 Applying time filter for therapist - Current Dubai time: {current_time}"
                )
                queryset = queryset.filter(time__gte=current_time)

            queryset = queryset.order_by("-created_at")

        elif user.role in ["owner", "receptionist"]:
            api_logger.debug(
                f"🔍 Fetching all appointments for {user.role} role - Date:{date}"
            )
            queryset = Appointment.objects.filter(date=date).order_by("-created_at")
        else:
            api_logger.warning(
                f"❌ User with unhandled role tried to access appointments - User:{user.id}, Role:{user.role}"
            )
            queryset = Appointment.objects.none()

        api_logger.info(
            f"✅ Found {queryset.count()} appointments for Date:{date}, User:{user.id}"
        )
        return queryset

    def get(self, request, *args, **kwargs):
        log_request_data(request, "📥 Appointment Day View Request")
        api_logger.info(f"🔄 Processing appointment day request")

        queryset = self.get_queryset()
        if queryset.exists():
            try:
                api_logger.debug(f"🔄 Serializing {queryset.count()} appointments")
                serializer = self.get_serializer(queryset, many=True)
                response = Response(serializer.data, status=status.HTTP_200_OK)
                log_response_data(response, "📤 Appointment Day View Response (Success)")
                api_logger.info(
                    f"✅ Successfully returned {queryset.count()} appointments"
                )
                return response
            except Exception as e:
                log_error(
                    e, "❌ ERROR in AppointmentDayView serialization", log_full_trace=True
                )
                response = Response(
                    {"detail": "An error occurred while retrieving appointments."},
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )
                log_response_data(
                    response, "📤 Appointment Day View Response (Server Error)"
                )
                return response

        api_logger.info("❌ No appointments found for the selected day")
        response = Response(
            {"detail": "No appointments found for the selected day."},
            status=status.HTTP_404_NOT_FOUND,
        )
        log_response_data(response, "📤 Appointment Day View Response (Not Found)")
        return response


class AppointmentManageView(generics.RetrieveUpdateDestroyAPIView):
    """
    View to confirm, update, or delete appointments.
    Only accessible by Owners or Receptionists.
    """

    queryset = Appointment.objects.all()
    serializer_class = AppointmentSerializer
    permission_classes = [IsAuthenticated, IsOwnerOrReceptionist]

    def patch(self, request, *args, **kwargs):
        log_request_data(request, "📥 Appointment Update Request")
        appointment = get_object_or_404(Appointment, pk=kwargs["pk"])
        data = request.data

        api_logger.info(
            f"🔄 Processing appointment update for Appointment:{appointment.id}, Customer:{appointment.customer.first_name} {appointment.customer.last_name}"
        )

        # If "check_in" was requested:
        if data.get("action") == "check_in":
            payment_method = data.get("payment_method", "")
            if payment_method not in ["cash", "card", "link"]:
                api_logger.warning(
                    f"❌ Invalid payment method '{payment_method}' for Appointment:{appointment.id}"
                )
                response = Response(
                    {"error": "Invalid payment method."},
                    status=status.HTTP_400_BAD_REQUEST,
                )
                log_response_data(response, "📤 Invalid Payment Method Response")
                return response

            appointment.status = "check_in"  # or "checked_in", up to you
            appointment.payment_method = payment_method
            appointment.save()

            api_logger.info(
                f"✅ Appointment:{appointment.id} checked in with payment method: {payment_method}"
            )

            # add activity record
            activity = Activity.objects.create(
                staff=self.request.user,
                customer=appointment.customer,
                activity_type="confirmation",
                description=f"{appointment.customer.first_name} {appointment.customer.last_name} confirmed AED {appointment.total_price} appointment",
            )

            response = Response(
                {"message": f"Appointment checked in with payment: {payment_method}"},
                status=status.HTTP_200_OK,
            )
            log_response_data(response, "📤 Check-in Success Response")
            return response

        # If "cancel" was requested:
        if data.get("action") == "no_show":
            appointment.status = "cancelled"
            appointment.save()

            api_logger.info(
                f"🔄 Marked Appointment:{appointment.id} as no-show/cancelled"
            )

            # add activity record
            activity = Activity.objects.create(
                staff=self.request.user,
                customer=appointment.customer,
                activity_type="cancellation",
                description=f"{appointment.customer.first_name} {appointment.customer.last_name} did not appear to the appointment",
            )

            response = Response(
                {"message": "Appointment cancelled successfully."},
                status=status.HTTP_200_OK,
            )
            log_response_data(response, "📤 Cancellation Success Response")
            return response

        # ... existing confirm / update logic ...
        if "status" in data:
            appointment.status = data["status"]
            appointment.save()

            api_logger.info(
                f"✅ Updated Appointment:{appointment.id} status to '{appointment.status}'"
            )

            response = Response(
                {"message": f"Appointment status updated to {appointment.status}"},
                status=status.HTTP_200_OK,
            )
            log_response_data(response, "📤 Status Update Response")
            return response

        try:
            serializer = self.get_serializer(appointment, data=data, partial=True)
            serializer.is_valid(raise_exception=True)
            serializer.save()

            api_logger.info(
                f"✅ Updated Appointment:{appointment.id} details successfully"
            )

            response = Response(serializer.data, status=status.HTTP_200_OK)
            log_response_data(response, "📤 Update Success Response")
            return response
        except Exception as e:
            log_error(e, f"Updating appointment ID:{appointment.id}")
            raise

    def delete(self, request, *args, **kwargs):
        log_request_data(request, "📥 Appointment Delete Request")
        try:
            appointment = self.get_object()
            api_logger.info(
                f"🔄 Deleting Appointment:{appointment.id} for Customer:{appointment.customer.first_name} {appointment.customer.last_name}"
            )

            appointment.delete()

            api_logger.info(f"✅ Successfully deleted Appointment:{appointment.id}")

            response = Response(
                {"message": "Appointment deleted successfully"},
                status=status.HTTP_200_OK,
            )
            log_response_data(response, "📤 Delete Success Response")
            return response
        except Exception as e:
            log_error(e, "Deleting appointment")
            raise


class AddMinutesToAppointmentView(generics.CreateAPIView):
    """
    API view to add minutes to an existing appointment.
    Only receptionist and owner roles can use this endpoint.
    """

    serializer_class = AddMinutesToAppointmentSerializer
    permission_classes = [IsAuthenticated]

    # Define service rates
    SERVICE_RATES = {
        "Massage": Decimal("6.7"),
        "Stretching": Decimal("7.5"),
        "Compression": Decimal("7.5"),
        "Red-Light Therapy": Decimal("12.6"),
        "Physio": Decimal("12.6"),
    }

    def create(self, request, *args, **kwargs):
        log_request_data(request, "📥 Add Minutes Request")

        # Only receptionist and owner can add minutes
        if request.user.role not in ["owner", "receptionist"]:
            api_logger.warning(
                f"❌ Unauthorized attempt to add minutes by user:{request.user.id} with role:{request.user.role}"
            )
            response = Response(
                {
                    "detail": "Only receptionist and owner can add minutes to appointments."
                },
                status=status.HTTP_403_FORBIDDEN,
            )
            log_response_data(response, "📤 Unauthorized Response")
            return response

        serializer = self.get_serializer(data=request.data)

        try:
            serializer.is_valid(raise_exception=True)

            # Get validated data
            appointment_id = serializer.validated_data["appointment_id"]
            services_data = serializer.validated_data["services"]

            # Retrieve the appointment
            appointment = Appointment.objects.get(id=appointment_id)

            api_logger.info(
                f"🔄 Adding minutes to Appointment:{appointment_id} for Customer:{appointment.customer.first_name} {appointment.customer.last_name}"
            )

            # Process each service and add minutes
            added_services = []
            total_added_minutes = 0
            total_added_cost = Decimal("0")

            for service_data in services_data:
                service_type = service_data["service_type"]
                minutes = service_data["minutes"]
                price_per_minute = self.SERVICE_RATES[service_type]
                service_total_price = price_per_minute * minutes

                api_logger.debug(
                    f"Adding {minutes} minutes of {service_type} at rate {price_per_minute}/min, total: {service_total_price}"
                )

                # Create additional service record
                additional_service = AppointmentAdditionalService.objects.create(
                    appointment=appointment,
                    service_type=service_type,
                    duration=minutes,
                    price_per_minute=price_per_minute,
                    total_price=service_total_price,
                    added_by=request.user,
                )

                # Update totals
                total_added_minutes += minutes
                total_added_cost += service_total_price

                # Add to response
                added_services.append(
                    AppointmentAdditionalServiceSerializer(additional_service).data
                )

            # Update appointment totals
            original_duration = appointment.total_duration
            original_price = appointment.total_price

            appointment.total_duration += total_added_minutes
            appointment.total_price += total_added_cost

            # Save the appointment
            appointment.save()

            api_logger.info(
                f"✅ Successfully added {total_added_minutes} minutes (${float(total_added_cost)}) to Appointment:{appointment_id}"
            )

            # Create activity record
            activity_description = f"{request.user.first_name} {request.user.last_name} added additional minutes to appointment for {appointment.customer.first_name} {appointment.customer.last_name}"

            # Create activity using the same pattern as existing code
            activity = Activity.objects.create(
                customer=appointment.customer,
                activity_type="appointment_minutes_added",
                description=activity_description,
            )

            # Prepare response
            response_data = {
                "success": True,
                "appointment_id": appointment.id,
                "original_duration": original_duration,
                "original_price": original_price,
                "added_minutes": total_added_minutes,
                "added_cost": float(total_added_cost),
                "new_total_duration": appointment.total_duration,
                "new_total_price": float(appointment.total_price),
                "added_services": added_services,
                "appointment": AppointmentSerializer(appointment).data,
            }

            response = Response(response_data, status=status.HTTP_200_OK)
            log_response_data(response, "📤 Add Minutes Success Response")
            return response

        except Appointment.DoesNotExist:
            api_logger.warning(
                f"❌ Attempt to add minutes to non-existent appointment ID:{appointment_id}"
            )
            response = Response(
                {"detail": "Appointment not found."}, status=status.HTTP_404_NOT_FOUND
            )
            log_response_data(response, "📤 Appointment Not Found Response")
            return response
        except Exception as e:
            log_error(
                e,
                f"Adding minutes to appointment ID:{serializer.validated_data.get('appointment_id', 'unknown')}",
                log_full_trace=True,
            )
            return Response(
                {"detail": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class DeleteAdditionalServiceView(generics.DestroyAPIView):
    """
    API view to delete an additional service from an appointment.
    Only receptionist and owner roles can use this endpoint.
    """

    permission_classes = [IsAuthenticated]

    def delete(self, request, service_id, *args, **kwargs):
        log_request_data(
            request, f"📥 Delete Additional Service Request (ID:{service_id})"
        )

        # Only receptionist and owner can delete additional services
        if request.user.role not in ["owner", "receptionist"]:
            api_logger.warning(
                f"❌ Unauthorized attempt to delete additional service by user:{request.user.id} with role:{request.user.role}"
            )
            response = Response(
                {"detail": "Only receptionist and owner can delete additional services."},
                status=status.HTTP_403_FORBIDDEN,
            )
            log_response_data(response, "📤 Unauthorized Response")
            return response

        try:
            # Get the additional service
            additional_service = AppointmentAdditionalService.objects.select_related(
                "appointment"
            ).get(id=service_id)
            appointment = additional_service.appointment

            api_logger.info(
                f"🔄 Deleting additional service ID:{service_id} ({additional_service.service_type}, {additional_service.duration} min) from Appointment:{appointment.id}"
            )

            # Store details before deletion for response
            service_details = {
                "id": additional_service.id,
                "service_type": additional_service.service_type,
                "duration": additional_service.duration,
                "total_price": float(additional_service.total_price),
            }

            # Update appointment totals
            appointment.total_duration -= additional_service.duration
            appointment.total_price -= additional_service.total_price

            # Create activity record
            activity_description = f"{request.user.first_name} {request.user.last_name} removed additional {additional_service.service_type} ({additional_service.duration} min, {additional_service.total_price} AED) from appointment for {appointment.customer.first_name} {appointment.customer.last_name}"

            Activity.objects.create(
                customer=appointment.customer,
                activity_type="appointment_minutes_removed",
                description=activity_description,
            )

            # Delete the appointment service associated with this additional service, if any
            from api.services.models import Service

            service = Service.objects.filter(name=additional_service.service_type).first()
            if service:
                deleted_count = appointment.appointment_services.filter(
                    service=service, duration=additional_service.duration
                ).delete()[0]
                api_logger.debug(
                    f"Deleted {deleted_count} associated appointment service records"
                )

            # Delete the additional service
            additional_service.delete()

            api_logger.info(f"🔄 Additional service deleted, updating appointment totals")

            # Save the appointment with updated totals
            # This will trigger any post_save signals,
            appointment.save()

            updated_appointment = AppointmentSerializer(appointment).data

            api_logger.info(
                f"✅ Successfully deleted additional service ID:{service_id} from Appointment:{appointment.id}"
            )

            # Prepare response
            response_data = {
                "success": True,
                "message": "Additional service deleted successfully",
                "deleted_service": service_details,
                "appointment": updated_appointment,
            }

            response = Response(response_data, status=status.HTTP_200_OK)
            log_response_data(response, "📤 Delete Service Success Response")
            return response

        except AppointmentAdditionalService.DoesNotExist:
            api_logger.warning(
                f"❌ Attempt to delete non-existent additional service ID:{service_id}"
            )
            response = Response(
                {"detail": "Additional service not found."},
                status=status.HTTP_404_NOT_FOUND,
            )
            log_response_data(response, "📤 Service Not Found Response")
            return response
        except Exception as e:
            log_error(
                e, f"Deleting additional service ID:{service_id}", log_full_trace=True
            )
            return Response(
                {"detail": str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )


@api_view(["PATCH"])
@permission_classes([IsAuthenticated])
def adjust_appointment_minutes(request, appointment_id):
    """
    Endpoint to adjust an appointment's duration by adding or removing minutes.
    This will:
    1. Update the appointment's total_duration
    2. Calculate and update the end time
    3. Adjust the associated package's remaining_time (if any)
    4. Log the activity

    Params:
    - minutes_change: Integer (positive to add, negative to remove minutes)
    """
    # Log incoming request
    log_request_data(request, f"📥 Adjust Minutes for Appointment:{appointment_id}")

    try:
        with transaction.atomic():
            # Find the appointment
            try:
                appointment = Appointment.objects.get(pk=appointment_id)
                api_logger.info(
                    f"🔍 Found Appointment:{appointment_id} for Customer:{appointment.customer.id}"
                )
            except Appointment.DoesNotExist:
                api_logger.warning(f"❌ Appointment:{appointment_id} not found")
                response = Response(
                    {"error": "Appointment not found"}, status=status.HTTP_404_NOT_FOUND
                )
                log_response_data(response, f"❌ Appointment Not Found")
                return response

            # Get the minutes change value
            minutes_change = request.data.get("minutes_change")
            if minutes_change is None:
                api_logger.warning(
                    f"❌ Missing required parameter 'minutes_change' for Appointment:{appointment_id}"
                )
                response = Response(
                    {"error": "minutes_change is required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
                log_response_data(response, f"❌ Invalid Request")
                return response

            try:
                minutes_change = int(minutes_change)
                api_logger.info(
                    f"🔄 Processing minutes change: {minutes_change} for Appointment:{appointment_id}"
                )
            except ValueError:
                api_logger.warning(
                    f"❌ Invalid minutes_change value: {minutes_change} for Appointment:{appointment_id}"
                )
                response = Response(
                    {"error": "minutes_change must be an integer"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
                log_response_data(response, f"❌ Invalid Request")
                return response

            # Store original values for logging
            original_duration = appointment.total_duration
            new_duration = original_duration + minutes_change

            if new_duration <= 0:
                api_logger.warning(
                    f"❌ Invalid duration after change: {new_duration} for Appointment:{appointment_id}"
                )
                response = Response(
                    {"error": "Appointment duration cannot be zero or negative"},
                    status=status.HTTP_400_BAD_REQUEST,
                )
                log_response_data(response, f"❌ Invalid Duration")
                return response

            # Update appointment duration
            appointment.total_duration = new_duration
            api_logger.info(
                f"🔄 Updating appointment duration: {original_duration} → {new_duration} for Appointment:{appointment_id}"
            )

            # Calculate new end time
            start_datetime = datetime.combine(appointment.date, appointment.time)
            if timezone.is_naive(start_datetime):
                start_datetime = timezone.make_aware(start_datetime)

            # No need to store end time in the model, just calculate for reference
            new_end_time = (start_datetime + timedelta(minutes=new_duration)).time()
            api_logger.info(
                f"🔄 New end time calculated: {new_end_time} for Appointment:{appointment_id}"
            )

            # Adjust package minutes if applicable
            package_updated = False

            # Check if appointment has a UserPackage
            if appointment.user_package:
                package = appointment.user_package
                previous_remaining = package.remaining_time
                previous_deducted = package.time_deducted

                # If removing minutes, add them back to the package
                if minutes_change < 0:
                    package.remaining_time -= (
                        minutes_change  # negative change becomes positive
                    )
                    package.time_deducted += (
                        minutes_change  # negative change reduces time_deducted
                    )
                # If adding minutes, subtract them from the package
                else:
                    package.remaining_time -= minutes_change
                    package.time_deducted += minutes_change

                # Ensure we don't have negative values
                package.time_deducted = max(0, package.time_deducted)
                package.active = package.remaining_time > 0
                package.save()
                package_updated = True

                api_logger.info(
                    f"✅ Updated UserPackage:{package.id} - Remaining time: {previous_remaining} → {package.remaining_time}, Deducted: {previous_deducted} → {package.time_deducted}"
                )

            # Check if appointment has a SharedPackage
            elif appointment.shared_package:
                package = appointment.shared_package
                previous_remaining = package.remaining_time

                # If removing minutes, add them back to the package
                if minutes_change < 0:
                    package.remaining_time -= (
                        minutes_change  # negative change becomes positive
                    )
                # If adding minutes, subtract them from the package
                else:
                    package.remaining_time -= minutes_change

                package.active = package.remaining_time > 0
                package.save()
                package_updated = True

                api_logger.info(
                    f"✅ Updated SharedPackage:{package.id} - Remaining time: {previous_remaining} → {package.remaining_time}"
                )

            # Save the appointment
            appointment.save()
            api_logger.info(
                f"✅ Saved Appointment:{appointment_id} with new duration: {new_duration}"
            )

            # Log the activity
            activity_type = (
                "appointment_minutes_added"
                if minutes_change > 0
                else "appointment_minutes_removed"
            )
            activity_description = f"{'Added' if minutes_change > 0 else 'Removed'} {abs(minutes_change)} minutes to appointment {appointment_id}. Duration: {original_duration} → {new_duration} min"

            Activity.objects.create(
                staff=request.user,
                customer=appointment.customer,
                activity_type=activity_type,
                description=activity_description,
            )
            api_logger.info(f"✅ Created activity log: {activity_description}")

            # Prepare response
            response_data = {
                "message": f"Successfully {'added' if minutes_change > 0 else 'removed'} {abs(minutes_change)} minutes",
                "appointment_id": appointment.id,
                "new_duration": new_duration,
                "new_end_time": new_end_time.strftime("%H:%M"),
                "package_updated": package_updated,
            }

            response = Response(response_data, status=status.HTTP_200_OK)
            log_response_data(response, f"✅ Appointment Minutes Adjusted")
            return response

    except Exception as e:
        # Log the error with full context
        log_error(
            e, f"Adjusting minutes for Appointment:{appointment_id}", log_full_trace=True
        )
        response = Response(
            {
                "error": f"Failed to adjust appointment minutes: {str(e)}",
            },
            status=status.HTTP_500_INTERNAL_SERVER_ERROR,
        )
        log_response_data(response, "❌ Error Adjusting Minutes")
        return response
