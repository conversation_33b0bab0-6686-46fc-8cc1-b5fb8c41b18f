# helpers/comparison_report.py
from decimal import Decimal
from datetime import datetime
from django.db.models import Sum, Count
from rest_framework import status
from rest_framework.response import Response

from api.retail.models import DailySalesReport


def get_comparison_report(
    comparison_type, location1, location2, start_date1, end_date1, start_date2, end_date2
):
    """Compare sales performance between two periods or locations."""

    # Validate parameters based on comparison type
    if comparison_type == "period":
        if (
            not location1
            or not start_date1
            or not end_date1
            or not start_date2
            or not end_date2
        ):
            return Response(
                {
                    "detail": "For period comparison, location1, start_date1, end_date1, start_date2, and end_date2 are required"
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            start_date1 = datetime.strptime(start_date1, "%Y-%m-%d").date()
            end_date1 = datetime.strptime(end_date1, "%Y-%m-%d").date()
            start_date2 = datetime.strptime(start_date2, "%Y-%m-%d").date()
            end_date2 = datetime.strptime(end_date2, "%Y-%m-%d").date()
        except ValueError:
            return Response(
                {"detail": "Invalid date format. Use YYYY-MM-DD"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Get data for first period
        period1_data = DailySalesReport.objects.filter(
            date__range=[start_date1, end_date1], location=location1
        ).aggregate(
            gross_sales=Sum("gross_sales_amount"),
            service_sales=Sum("service_sales_amount"),
            product_sales=Sum("product_sales_amount"),
            package_sales=Sum("package_sales_amount"),
            cash_sales=Sum("cash_sales_amount"),
            card_sales=Sum("card_sales_amount"),
            online_sales=Sum("online_link_amount"),
            net_sales=Sum("net_sales_amount"),
            days=Count("id"),
        )

        # Get data for second period
        period2_data = DailySalesReport.objects.filter(
            date__range=[start_date2, end_date2], location=location1
        ).aggregate(
            gross_sales=Sum("gross_sales_amount"),
            service_sales=Sum("service_sales_amount"),
            product_sales=Sum("product_sales_amount"),
            package_sales=Sum("package_sales_amount"),
            net_sales=Sum("net_sales_amount"),
            days=Count("id"),
        )

        # Calculate comparison metrics
        comparison = {}
        for key in period1_data:
            if period1_data[key] is None:
                period1_data[key] = Decimal("0.00") if key != "days" else 0
            if period2_data[key] is None:
                period2_data[key] = Decimal("0.00") if key != "days" else 0

            if key != "days":
                # Calculate percentage change
                if period1_data[key] > 0:
                    change_pct = (
                        (period2_data[key] - period1_data[key]) / period1_data[key]
                    ) * 100
                else:
                    change_pct = 0 if period2_data[key] == 0 else 100

                comparison[key] = {
                    "period1": period1_data[key],
                    "period2": period2_data[key],
                    "change": period2_data[key] - period1_data[key],
                    "change_pct": change_pct,
                }

        return {
            "comparison_type": "period",
            "location": location1,
            "location_display": dict(DailySalesReport.LOCATION_CHOICES).get(
                location1, location1
            ),
            "period1": {
                "start_date": start_date1.isoformat(),
                "end_date": end_date1.isoformat(),
                "days": period1_data["days"],
            },
            "period2": {
                "start_date": start_date2.isoformat(),
                "end_date": end_date2.isoformat(),
                "days": period2_data["days"],
            },
            "comparison": comparison,
        }

    elif comparison_type == "location":
        if not location1 or not location2 or not start_date1 or not end_date1:
            return Response(
                {
                    "detail": "For location comparison, location1, location2, start_date1, and end_date1 are required"
                },
                status=status.HTTP_400_BAD_REQUEST,
            )

        try:
            start_date1 = datetime.strptime(start_date1, "%Y-%m-%d").date()
            end_date1 = datetime.strptime(end_date1, "%Y-%m-%d").date()
        except ValueError:
            return Response(
                {"detail": "Invalid date format. Use YYYY-MM-DD"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        # Get data for first location
        location1_data = DailySalesReport.objects.filter(
            date__range=[start_date1, end_date1], location=location1
        ).aggregate(
            gross_sales=Sum("gross_sales_amount"),
            service_sales=Sum("service_sales_amount"),
            product_sales=Sum("product_sales_amount"),
            package_sales=Sum("package_sales_amount"),
            cash_sales=Sum("cash_sales_amount"),
            card_sales=Sum("card_sales_amount"),
            online_sales=Sum("online_link_amount"),
            net_sales=Sum("net_sales_amount"),
            days=Count("id"),
        )

        # Get data for second location
        location2_data = DailySalesReport.objects.filter(
            date__range=[start_date1, end_date1], location=location2
        ).aggregate(
            gross_sales=Sum("gross_sales_amount"),
            service_sales=Sum("service_sales_amount"),
            product_sales=Sum("product_sales_amount"),
            package_sales=Sum("package_sales_amount"),
            cash_sales=Sum("cash_sales_amount"),
            card_sales=Sum("card_sales_amount"),
            online_sales=Sum("online_link_amount"),
            net_sales=Sum("net_sales_amount"),
            days=Count("id"),
        )

        # Calculate comparison metrics
        comparison = {}
        for key in location1_data:
            if location1_data[key] is None:
                location1_data[key] = Decimal("0.00") if key != "days" else 0
            if location2_data[key] is None:
                location2_data[key] = Decimal("0.00") if key != "days" else 0

            if key != "days":
                # Calculate percentage difference
                if location1_data[key] > 0:
                    diff_pct = (
                        (location2_data[key] - location1_data[key]) / location1_data[key]
                    ) * 100
                else:
                    diff_pct = 0 if location2_data[key] == 0 else 100

                comparison[key] = {
                    "location1": location1_data[key],
                    "location2": location2_data[key],
                    "difference": location2_data[key] - location1_data[key],
                    "diff_pct": diff_pct,
                }

        return {
            "comparison_type": "location",
            "date_range": {
                "start_date": start_date1.isoformat(),
                "end_date": end_date1.isoformat(),
            },
            "location1": {
                "code": location1,
                "name": dict(DailySalesReport.LOCATION_CHOICES).get(location1, location1),
                "days": location1_data["days"],
            },
            "location2": {
                "code": location2,
                "name": dict(DailySalesReport.LOCATION_CHOICES).get(location2, location2),
                "days": location2_data["days"],
            },
            "comparison": comparison,
        }

    else:
        return Response(
            {"detail": "Invalid comparison type. Must be 'period' or 'location'"},
            status=status.HTTP_400_BAD_REQUEST,
        )
