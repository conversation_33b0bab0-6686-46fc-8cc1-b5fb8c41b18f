from datetime import datetime, timed<PERSON>ta
import calendar
from django.db.models import Q, Sum
from django.utils.dateparse import parse_date
from rest_framework.exceptions import ValidationError
from rest_framework import status
from rest_framework import generics
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from rest_framework.pagination import PageNumberPagination
from rest_framework.views import APIView
from django.shortcuts import get_object_or_404
from django.contrib.auth import get_user_model
from rest_framework.filters import SearchFilter
from django.http import Http404, HttpResponse
from django_filters.rest_framework import DjangoFilterBackend
from api.appointments.models import (
    Activity,
    Appointment,
    AppointmentService,
    UserPackage,
    SharedPackage,
    SharedPackageUser,
    UnlimitedPackage,
    Discount,
    RewardBalance,
)
from api.services.models import Service, ServiceDuration, PackageOption
from api.appointments.serializers import (
    AppointmentSerializer,
    UserPackageSerializer,
    SharedPackageSerializer,
    UnlimitedPackageSerializer,
    AddMinutesToAppointmentSerializer,
    AppointmentAdditionalServiceSerializer,
    AppointmentAdditionalService,
    SharedPackageUserSerializer,
    PackageUsageSerializer,
    DiscountSerializer,
    AdminRewardSerializer,
    RewardBalanceSerializer,
)

from api.core.permissions import IsOwnerOrReceptionist
from api.staff.models import TherapistProfile, WorkingHour
from api.authentication.serializers import UserProfileSerializer
import traceback
from decimal import Decimal
from rest_framework.exceptions import NotFound, APIException, PermissionDenied
from api.staff.serializers import (
    TherapistProfileOptimizedSerializer,
)
from django.utils import timezone
import csv

User = get_user_model()

from .utils import send_appointment_confirmation_email

from .utils import PackageViewMixin

# Constants
MIN_ACTIVE_MINUTES = 0
BREAK_TIME = 10  # minutes break between appointments
NEW_APPOINTMENT_BREAK = 10  # minutes

from utils.logging import api_logger, log_request_data, log_response_data, log_error


class ActiveSharedPackage(generics.RetrieveAPIView):
    serializer_class = SharedPackageSerializer
    permission_classes = [IsAuthenticated]

    def get(self, request):
        log_prefix = "📦 Active Shared Package"
        log_request_data(request, log_prefix)

        try:
            user = request.user
            api_logger.info(
                f"🔍 {log_prefix} - Checking for active shared package for User:{user.id}"
            )

            # Query for an active UserPackage for the user.
            active_package = SharedPackage.objects.filter(
                users__user=user, active=True, remaining_time__gt=MIN_ACTIVE_MINUTES
            ).first()

            if active_package:
                api_logger.info(
                    f"✅ {log_prefix} - Found active package ID:{active_package.id} for User:{user.id}"
                )
                serializer = self.get_serializer(active_package)
                response = Response(serializer.data, status=200)
                log_response_data(response, f"{log_prefix} - Success")
                return response
            else:
                api_logger.warning(
                    f"❌ {log_prefix} - No active package found for User:{user.id}"
                )
                response = Response({"detail": "No active package found"}, status=404)
                log_response_data(response, f"{log_prefix} - Not Found")
                return response

        except Exception as e:
            log_error(
                e, f"{log_prefix} - Error retrieving active package", log_full_trace=True
            )
            response = Response(
                {"detail": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
            log_response_data(response, f"❌ {log_prefix}")
            return response


class AppointmentSharedPackageView(generics.ListCreateAPIView):
    """
    View for listing and creating appointments that use a shared package.
    Receptionists (or owners) can either book an appointment using an
    existing shared package (by providing shared_package_id) or create a
    new shared package.
    """

    serializer_class = AppointmentSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        user = self.request.user
        log_prefix = "📋 Shared Package Appointments"
        api_logger.info(f"{log_prefix} - User:{user.id} Role:{user.role} requesting list")

        qs = Appointment.objects.filter(shared_package__isnull=False).order_by(
            "-created_at"
        )
        if user.role in ["owner", "receptionist"]:
            api_logger.info(
                f"{log_prefix} - Returning all shared package appointments for staff"
            )
            return qs

        # For customers, show only their own shared package appointments.
        api_logger.info(f"{log_prefix} - Filtering appointments for customer:{user.id}")
        return qs.filter(customer=user)

    def create(self, request, *args, **kwargs):
        log_prefix = "📝 Shared Package Appointment Create"
        log_request_data(request, log_prefix)

        try:
            response = super().create(request, *args, **kwargs)
            log_response_data(response, f"✅ {log_prefix} - Success")
            return response
        except Exception as e:
            log_error(e, f"{log_prefix} - Failed", log_full_trace=True)
            if isinstance(e, ValidationError):
                response = Response(e.detail, status=status.HTTP_400_BAD_REQUEST)
            else:
                response = Response(
                    {"detail": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )
            log_response_data(response, f"❌ {log_prefix}")
            return response

    def perform_create(self, serializer):
        data = self.request.data
        log_prefix = "📝 Shared Package Appointment"

        try:
            # Ensure we are processing a shared package booking
            service_type = data.get("serviceType")
            if service_type != "shared_package":
                api_logger.warning(
                    f"❌ {log_prefix} - Invalid service type: {service_type}"
                )
                raise ValidationError(
                    {"detail": "Invalid serviceType for shared package booking."}
                )

            # Determine the customer
            if self.request.user.role in ["owner", "receptionist"]:
                customer_data = data.get("customer")
                if not customer_data:
                    api_logger.warning(f"❌ {log_prefix} - Missing customer data")
                    raise ValidationError({"detail": "Customer data is required."})

                email = customer_data.get("email", "").strip().lower()
                phone_number = customer_data.get("phone_number", "").strip()
                customer = None

                api_logger.info(
                    f"🔍 {log_prefix} - Looking up customer by email:{email} or phone:{phone_number}"
                )
                if email:
                    customer = User.objects.filter(email=email).first()
                if not customer and phone_number:
                    customer = User.objects.filter(phone_number=phone_number).first()

                if not customer:
                    api_logger.info(
                        f"🆕 {log_prefix} - Creating new customer with email:{email}"
                    )
                    # Create new customer if one does not exist.
                    customer = User.objects.create(
                        email=email,
                        first_name=customer_data.get("first_name", ""),
                        last_name=customer_data.get("last_name", ""),
                        phone_number=phone_number,
                        role="customer",
                    )
                    customer.set_unusable_password()
                    customer.save()
            else:
                customer = self.request.user
                api_logger.info(
                    f"{log_prefix} - Using authenticated user as customer:{customer.id}"
                )

            # Get total_duration
            try:
                total_duration = int(data.get("duration"))
                api_logger.info(
                    f"{log_prefix} - Appointment duration:{total_duration} minutes"
                )
            except (TypeError, ValueError):
                api_logger.warning(
                    f"❌ {log_prefix} - Invalid duration format: {data.get('duration')}"
                )
                raise ValidationError({"detail": "Invalid duration provided."})

            # Shared Package Logic
            shared_package_id = data.get("shared_package_id")
            api_logger.info(
                f"{log_prefix} - Processing with shared_package_id:{shared_package_id}"
            )
            total_price = Decimal("0")

            if shared_package_id:
                # Use existing package
                try:
                    shared_package = SharedPackage.objects.get(
                        id=shared_package_id, active=True
                    )
                    api_logger.info(
                        f"{log_prefix} - Found existing package:{shared_package_id} with {shared_package.remaining_time} minutes remaining"
                    )
                except SharedPackage.DoesNotExist:
                    api_logger.warning(
                        f"❌ {log_prefix} - Invalid package ID:{shared_package_id}"
                    )
                    raise ValidationError(
                        {"detail": "Invalid or inactive shared package."}
                    )

                if shared_package.remaining_time < total_duration:
                    api_logger.warning(
                        f"❌ {log_prefix} - Not enough time in package. Needed:{total_duration}, Available:{shared_package.remaining_time}"
                    )
                    raise ValidationError(
                        {"detail": "Not enough remaining time in the shared package."}
                    )

                # Deduct time
                prev_remaining = shared_package.remaining_time
                shared_package.remaining_time -= total_duration
                if shared_package.remaining_time <= 0:
                    shared_package.active = False
                    api_logger.info(
                        f"{log_prefix} - Package:{shared_package_id} depleted and deactivated"
                    )
                shared_package.save()
                api_logger.info(
                    f"✅ {log_prefix} - Updated package remaining time: {prev_remaining} → {shared_package.remaining_time}"
                )
            else:
                # Create new package
                api_logger.info(f"{log_prefix} - Creating new shared package")
                services = data.get("services", [])
                if not services or len(services) != 1:
                    api_logger.warning(
                        f"❌ {log_prefix} - Invalid services data: {services}"
                    )
                    raise ValidationError(
                        {
                            "detail": "Only one shared package service option can be booked."
                        }
                    )

                package_data = services[0]
                package_option_id = package_data.get("id")
                if not package_option_id:
                    api_logger.warning(f"❌ {log_prefix} - Missing package option ID")
                    raise ValidationError({"detail": "Package option ID is required."})

                try:
                    package_option = PackageOption.objects.get(id=package_option_id)
                    api_logger.info(
                        f"{log_prefix} - Found package option:{package_option_id}"
                    )
                except PackageOption.DoesNotExist:
                    api_logger.warning(
                        f"❌ {log_prefix} - Invalid package option ID:{package_option_id}"
                    )
                    raise ValidationError({"detail": "Invalid package option."})

                try:
                    package_total_time = int(package_data.get("time"))
                    total_price = Decimal(package_data.get("price"))
                    api_logger.info(
                        f"{log_prefix} - Package details: time:{package_total_time}, price:{total_price}"
                    )
                except (TypeError, ValueError):
                    api_logger.warning(
                        f"❌ {log_prefix} - Invalid package time or price: {package_data}"
                    )
                    raise ValidationError({"detail": "Invalid package time provided."})

                remaining_time = package_total_time - total_duration
                if remaining_time < 0:
                    api_logger.warning(
                        f"❌ {log_prefix} - Duration {total_duration} exceeds package time {package_total_time}"
                    )
                    raise ValidationError(
                        {"detail": "Booked duration exceeds package total time."}
                    )

                shared_package = SharedPackage.objects.create(
                    package_option=package_option,
                    total_time=package_total_time,
                    remaining_time=remaining_time,
                    active=True,
                )
                api_logger.info(
                    f"✅ {log_prefix} - Created new package ID:{shared_package.id}"
                )

                # Add customer to users
                SharedPackageUser.objects.create(
                    shared_package=shared_package, user=customer
                )
                api_logger.info(
                    f"{log_prefix} - Added customer:{customer.id} to package:{shared_package.id}"
                )

            # Get therapist
            therapist_id = data.get("therapist_id")
            try:
                therapist = TherapistProfile.objects.get(id=therapist_id)
                api_logger.info(f"{log_prefix} - Found therapist:{therapist_id}")
            except TherapistProfile.DoesNotExist:
                api_logger.warning(
                    f"❌ {log_prefix} - Invalid therapist ID:{therapist_id}"
                )
                raise ValidationError({"detail": "Invalid therapist."})

            # Create appointment
            location = data.get("location")
            date = data.get("date")
            time = data.get("time")

            appointment = Appointment.objects.create(
                date=date,
                time=time,
                customer=customer,
                total_duration=total_duration,
                total_price=total_price,
                therapist=therapist,
                location=location,
                shared_package=shared_package,
            )
            api_logger.info(
                f"✅ {log_prefix} - Created appointment ID:{appointment.id} for {date} {time}"
            )

            # Record activity
            Activity.objects.create(
                customer=self.request.user,
                activity_type="booking",
                description=f"{self.request.user.first_name} booked a shared package appointment for {customer.email}",
            )
            api_logger.info(f"{log_prefix} - Recorded activity for booking")

            # Send confirmation email
            try:
                serialized_appointment = AppointmentSerializer(appointment).data
                # send_appointment_confirmation_email(serialized_appointment)
                api_logger.info(
                    f"✅ {log_prefix} - Sent confirmation email to {customer.email}"
                )
            except Exception as email_error:
                log_error(
                    email_error,
                    f"{log_prefix} - Failed to send confirmation email",
                    log_full_trace=False,
                )

            return appointment

        except Exception as e:
            if not isinstance(e, ValidationError):
                log_error(
                    e,
                    f"{log_prefix} - Unexpected error in perform_create",
                    log_full_trace=True,
                )
            raise


class CustomerActiveSharedPackageView(generics.RetrieveAPIView):
    """
    Retrieves the active shared package for a specific customer.
    """

    serializer_class = SharedPackageSerializer
    permission_classes = [IsAuthenticated, IsOwnerOrReceptionist]

    def get(self, request, customer_id, *args, **kwargs):
        log_prefix = f"📦 Customer Active Shared Package [Customer:{customer_id}]"
        log_request_data(request, log_prefix)

        try:
            api_logger.info(f"🔍 {log_prefix} - Looking up customer")
            customer = get_object_or_404(User, id=customer_id, role="customer")

            api_logger.info(f"{log_prefix} - Searching for active shared package")
            active_shared_package = SharedPackage.objects.filter(
                users__user=customer, active=True, remaining_time__gt=MIN_ACTIVE_MINUTES
            ).first()

            if active_shared_package:
                api_logger.info(
                    f"✅ {log_prefix} - Found active package ID:{active_shared_package.id} with {active_shared_package.remaining_time} minutes remaining"
                )
                serializer = self.get_serializer(active_shared_package)
                response = Response(serializer.data, status=200)
                log_response_data(response, f"{log_prefix} - Success")
                return response

            api_logger.warning(f"❌ {log_prefix} - No active shared package found")
            response = Response({"detail": "No active shared package found."}, status=404)
            log_response_data(response, f"{log_prefix} - Not Found")
            return response

        except Exception as e:
            log_error(
                e,
                f"{log_prefix} - Failed to retrieve active package",
                log_full_trace=True,
            )
            response = Response(
                {"detail": f"Internal Server Error: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
            log_response_data(response, f"❌ {log_prefix}")
            return response


class SharedPackageDetailView(generics.RetrieveAPIView, PackageViewMixin):
    """Retrieve details for a specific shared package"""

    serializer_class = SharedPackageSerializer
    permission_classes = [IsAuthenticated]

    def get(self, request, *args, **kwargs):
        package_id = self.kwargs.get("pk")
        log_prefix = f"🔍 Shared Package Detail [ID:{package_id}]"
        log_request_data(request, log_prefix)

        try:
            response = super().get(request, *args, **kwargs)
            log_response_data(response, f"✅ {log_prefix}")
            return response
        except Exception as e:
            log_error(
                e, f"{log_prefix} - Failed to retrieve package", log_full_trace=True
            )
            if isinstance(e, PermissionDenied):
                response = Response({"detail": str(e)}, status=status.HTTP_403_FORBIDDEN)
            elif isinstance(e, Http404):
                response = Response(
                    {"detail": "Package not found"}, status=status.HTTP_404_NOT_FOUND
                )
            else:
                response = Response(
                    {"detail": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
                )
            log_response_data(response, f"❌ {log_prefix}")
            return response

    def get_object(self):
        package_id = self.kwargs.get("pk")
        api_logger.info(f"🔍 Shared Package Detail - Finding package:{package_id}")

        try:
            package = get_object_or_404(SharedPackage, id=package_id)

            # Check access permissions
            user = self.request.user
            api_logger.info(
                f"🔐 Shared Package Detail - Checking access for User:{user.id} Role:{user.role}"
            )

            if not self.check_package_access(package, self.request, "shared_package"):
                api_logger.warning(
                    f"🚫 Shared Package Detail - Access denied for User:{user.id} to Package:{package_id}"
                )
                raise PermissionDenied("You don't have permission to view this package.")

            api_logger.info(
                f"✅ Shared Package Detail - Access granted for Package:{package_id}"
            )
            return package
        except Exception as e:
            if not (isinstance(e, Http404) or isinstance(e, PermissionDenied)):
                log_error(
                    e,
                    f"Shared Package Detail - Error in get_object for package:{package_id}",
                    log_full_trace=True,
                )
            raise


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def shared_package_usage(request, pk):
    """Get usage history for a shared package"""
    log_prefix = f"📊 Shared Package Usage [ID:{pk}]"
    log_request_data(request, log_prefix)

    try:
        api_logger.info(f"{log_prefix} - Finding package")
        package = get_object_or_404(SharedPackage, id=pk)

        # Create an instance of the mixin to use its methods
        mixin = PackageViewMixin()
        user = request.user

        api_logger.info(f"{log_prefix} - Checking access for User:{user.id}")
        # Check access permissions
        if not mixin.check_package_access(package, request, "shared_package"):
            api_logger.warning(f"{log_prefix} - Access denied for User:{user.id}")
            response = Response(
                {"detail": "You don't have permission to view this package."},
                status=status.HTTP_403_FORBIDDEN,
            )
            log_response_data(response, f"{log_prefix} - Forbidden")
            return response

        api_logger.info(f"{log_prefix} - Retrieving usage history")
        usage_data = mixin.get_package_usage(package, "shared_package")

        response = Response(usage_data)
        log_response_data(response, f"{log_prefix} - Success")
        return response

    except Exception as e:
        log_error(e, f"{log_prefix} - Failed to retrieve usage", log_full_trace=True)
        response = Response(
            {"detail": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
        log_response_data(response, f"{log_prefix} - Error")
        return response


class SharedPackageUsersView(generics.ListCreateAPIView, PackageViewMixin):
    """List and add users to a shared package"""

    serializer_class = SharedPackageUserSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        package_id = self.kwargs.get("pk")
        log_prefix = f"👥 Shared Package Users [ID:{package_id}]"
        api_logger.info(f"{log_prefix} - Retrieving users list")

        try:
            package = get_object_or_404(SharedPackage, id=package_id)
            user = self.request.user

            # Check access permissions
            api_logger.info(f"{log_prefix} - Checking access for User:{user.id}")
            if not self.check_package_access(package, self.request, "shared_package"):
                api_logger.warning(f"{log_prefix} - Access denied for User:{user.id}")
                return SharedPackageUser.objects.none()

            api_logger.info(f"{log_prefix} - Access granted, returning users list")
            return SharedPackageUser.objects.filter(shared_package=package)

        except Exception as e:
            log_error(e, f"{log_prefix} - Error in get_queryset", log_full_trace=True)
            return SharedPackageUser.objects.none()

    def list(self, request, *args, **kwargs):
        package_id = self.kwargs.get("pk")
        log_prefix = f"👥 Shared Package Users [ID:{package_id}]"
        log_request_data(request, f"{log_prefix} - List")

        try:
            queryset = self.get_queryset()
            users = [package_user.user for package_user in queryset]
            api_logger.info(f"{log_prefix} - Found {len(users)} users")

            # Add the added_at field to each user
            user_data = []
            for i, user in enumerate(users):
                user_dict = {
                    "id": user.id,
                    "first_name": user.first_name,
                    "last_name": user.last_name,
                    "email": user.email,
                    "phone_number": user.phone_number,
                    "added_at": (
                        queryset[i].added_at.isoformat() if queryset[i].added_at else None
                    ),
                }
                user_data.append(user_dict)

            response = Response(user_data)
            log_response_data(response, f"{log_prefix} - Success")
            return response

        except Exception as e:
            log_error(e, f"{log_prefix} - Failed to list users", log_full_trace=True)
            response = Response(
                {"detail": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
            log_response_data(response, f"{log_prefix} - Error")
            return response

    def create(self, request, *args, **kwargs):
        package_id = self.kwargs.get("pk")
        log_prefix = f"👥 Shared Package Users [ID:{package_id}]"
        log_request_data(request, f"{log_prefix} - Add User")

        try:
            package = get_object_or_404(SharedPackage, id=package_id)
            user_requester = request.user

            # Only receptionist and owner can add users
            if user_requester.role not in ["owner", "receptionist"]:
                api_logger.warning(
                    f"{log_prefix} - Unauthorized role: {user_requester.role}"
                )
                response = Response(
                    {"detail": "Only owner and receptionist can add users to a package."},
                    status=status.HTTP_403_FORBIDDEN,
                )
                log_response_data(response, f"{log_prefix} - Forbidden")
                return response

            user_id = request.data.get("user_id")
            if not user_id:
                api_logger.warning(f"{log_prefix} - Missing user ID")
                response = Response(
                    {"detail": "User ID is required."}, status=status.HTTP_400_BAD_REQUEST
                )
                log_response_data(response, f"{log_prefix} - Bad Request")
                return response

            # Check if user exists
            api_logger.info(f"{log_prefix} - Looking up User:{user_id}")
            try:
                user = User.objects.get(id=user_id)
            except User.DoesNotExist:
                api_logger.warning(f"{log_prefix} - User:{user_id} not found")
                response = Response(
                    {"detail": "User not found."}, status=status.HTTP_404_NOT_FOUND
                )
                log_response_data(response, f"{log_prefix} - Not Found")
                return response

            # Check if user is already in the package
            if SharedPackageUser.objects.filter(
                shared_package=package, user=user
            ).exists():
                api_logger.warning(f"{log_prefix} - User:{user_id} already in package")
                response = Response(
                    {"detail": "User is already in this package."},
                    status=status.HTTP_400_BAD_REQUEST,
                )
                log_response_data(response, f"{log_prefix} - Bad Request")
                return response

            # Create the association
            api_logger.info(
                f"{log_prefix} - Adding User:{user_id} to Package:{package_id}"
            )
            package_user = SharedPackageUser.objects.create(
                shared_package=package, user=user
            )
            api_logger.info(f"{log_prefix} - User:{user_id} successfully added")

            # Return user data with added_at timestamp
            user_data = {
                "id": user.id,
                "first_name": user.first_name,
                "last_name": user.last_name,
                "email": user.email,
                "phone_number": user.phone_number,
                "added_at": (
                    package_user.added_at.isoformat() if package_user.added_at else None
                ),
            }

            response = Response(user_data, status=status.HTTP_201_CREATED)
            log_response_data(response, f"{log_prefix} - Created")
            return response

        except Exception as e:
            log_error(e, f"{log_prefix} - Failed to add user", log_full_trace=True)
            response = Response(
                {"detail": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
            log_response_data(response, f"{log_prefix} - Error")
            return response


class SharedPackageUserRemoveView(generics.DestroyAPIView, PackageViewMixin):
    """Remove a user from a shared package"""

    permission_classes = [IsAuthenticated]

    def get_object(self):
        package_id = self.kwargs.get("pk")
        user_id = self.kwargs.get("user_id")

        api_logger.info(f"🔍 Retrieving shared package {package_id} and user {user_id}")
        package = get_object_or_404(SharedPackage, id=package_id)

        # Only receptionist and owner can remove users
        if self.request.user.role not in ["owner", "receptionist"]:
            api_logger.warning(
                f"❌ Permission denied for user {self.request.user.id} with role {self.request.user.role}"
            )
            raise PermissionDenied(
                "Only owner and receptionist can remove users from a package."
            )

        package_user = get_object_or_404(
            SharedPackageUser, shared_package=package, user_id=user_id
        )
        api_logger.info(
            f"✅ Found package user relation for package {package_id} and user {user_id}"
        )
        return package_user

    def destroy(self, request, *args, **kwargs):
        log_request_data(request, "📥 SharedPackageUser Remove Request")

        try:
            instance = self.get_object()
            package_id = instance.shared_package.id
            user_id = instance.user_id

            api_logger.info(
                f"🔄 Removing user {user_id} from shared package {package_id}"
            )
            self.perform_destroy(instance)

            api_logger.info(
                f"✅ Successfully removed user {user_id} from shared package {package_id}"
            )
            response = Response(status=status.HTTP_204_NO_CONTENT)
            log_response_data(response, "📤 SharedPackageUser Remove Response")
            return response

        except Exception as e:
            log_error(e, f"Removing user from shared package", log_full_trace=True)
            raise
