from datetime import datetime, timed<PERSON>ta
import calendar
from django.db.models import Q, Sum
from django.utils.dateparse import parse_date
from rest_framework.exceptions import ValidationError
from rest_framework import status
from rest_framework import generics
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from rest_framework.pagination import PageNumberPagination
from rest_framework.views import APIView
from django.shortcuts import get_object_or_404
from django.contrib.auth import get_user_model
from rest_framework.filters import SearchFilter
from django.http import HttpResponse
from django_filters.rest_framework import DjangoFilterBackend
from api.appointments.models import (
    Activity,
    Appointment,
    AppointmentService,
    UserPackage,
    SharedPackage,
    SharedPackageUser,
    UnlimitedPackage,
    Discount,
    RewardBalance,
)
from api.services.models import Service, ServiceDuration, PackageOption
from api.appointments.serializers import (
    AppointmentSerializer,
    UserPackageSerializer,
    SharedPackageSerializer,
    UnlimitedPackageSerializer,
    AddMinutesToAppointmentSerializer,
    AppointmentAdditionalServiceSerializer,
    AppointmentAdditionalService,
    SharedPackageUserSerializer,
    PackageUsageSerializer,
    DiscountSerializer,
    AdminRewardSerializer,
    RewardBalanceSerializer,
)

from api.core.permissions import IsOwnerOrReceptionist
from api.staff.models import TherapistProfile, WorkingHour
from api.authentication.serializers import UserProfileSerializer
import traceback
from decimal import Decimal
from rest_framework.exceptions import NotFound, APIException, PermissionDenied
from api.staff.serializers import (
    TherapistProfileOptimizedSerializer,
)
from django.utils import timezone
import csv

User = get_user_model()

# Constants
MIN_ACTIVE_MINUTES = 0


from utils.logging import (
    api_logger,
    logger,
    log_request_data,
    log_response_data,
    log_error,
)


class CustomUserPagination(PageNumberPagination):
    page_size = 5  # Adjust as needed
    page_size_query_param = "page_size"
    max_page_size = 5


class CustomerListView(generics.ListAPIView):
    """
    List customers with pagination, search functionality, and date range filtering.
    Also supports CSV export.
    """

    serializer_class = UserProfileSerializer
    permission_classes = [IsAuthenticated, IsOwnerOrReceptionist]
    pagination_class = CustomUserPagination
    filter_backends = [DjangoFilterBackend, SearchFilter]
    search_fields = ["email", "phone_number", "first_name", "last_name"]

    def get_queryset(self):
        """
        Override get_queryset to filter by date range and handle errors.
        """
        try:
            api_logger.info(f"🔍 Retrieving customer queryset with filters")
            queryset = User.objects.filter(role="customer")

            # Apply date range filtering on created_at
            start_date = self.request.query_params.get("start_date")
            end_date = self.request.query_params.get("end_date")

            if start_date:
                api_logger.debug(f"Filtering customers created from {start_date}")
                queryset = queryset.filter(created_at__gte=start_date)
            if end_date:
                # Add one day to include the end date fully
                end_date_obj = datetime.strptime(end_date, "%Y-%m-%d") + timedelta(days=1)
                end_date_str = end_date_obj.strftime("%Y-%m-%d")
                api_logger.debug(f"Filtering customers created before {end_date_str}")
                queryset = queryset.filter(created_at__lt=end_date_str)

            count = queryset.count()
            api_logger.info(f"✅ Customer queryset retrieved: {count} customers")
            return queryset
        except Exception as e:
            log_error(e, "Retrieving customer queryset", log_full_trace=True)
            raise APIException(f"Internal Server Error: {str(e)}")

    def get(self, request, *args, **kwargs):
        """
        Override get method to handle CSV export
        """
        log_request_data(request, "Customer List Request")

        export_format = request.query_params.get("export")
        if export_format == "csv":
            api_logger.info(f"🔄 Processing CSV export request for customers")
            response = self._export_to_csv(request)
            log_response_data(response, "Customer CSV Export Response")
            return response

        response = super().get(request, *args, **kwargs)
        log_response_data(response, "Customer List Response")
        return response

    def _export_to_csv(self, request):
        """
        Export customers to CSV
        """
        try:
            api_logger.info("🔄 Starting customer data CSV export")
            response = HttpResponse(content_type="text/csv")
            filename = f"customers_{timezone.now().strftime('%Y%m%d')}.csv"
            response["Content-Disposition"] = f'attachment; filename="{filename}"'

            writer = csv.writer(response)

            # Write headers
            writer.writerow(
                [
                    "ID",
                    "First Name",
                    "Last Name",
                    "Email",
                    "Phone Number",
                    "Gender",
                    "Created At",
                    "Updated At",
                    "Medical Issues",
                    "Goal",
                    "Reward Balance",
                ]
            )

            # Get queryset with filters but without pagination
            queryset = self.filter_queryset(self.get_queryset())
            api_logger.info(f"🔄 Exporting {queryset.count()} customers to CSV")

            # Write data rows
            rows_written = 0
            for user in queryset:
                reward_balance = (
                    user.reward_balance.total_points
                    if hasattr(user, "reward_balance")
                    else 0
                )
                writer.writerow(
                    [
                        user.id,
                        user.first_name,
                        user.last_name,
                        user.email,
                        user.phone_number,
                        user.gender,
                        user.created_at.strftime("%Y-%m-%d") if user.created_at else "",
                        user.updated_at.strftime("%Y-%m-%d") if user.updated_at else "",
                        user.medical_issues or "",
                        user.goal or "",
                        reward_balance,
                    ]
                )
                rows_written += 1

            api_logger.info(
                f"✅ Successfully exported {rows_written} customer records to CSV"
            )
            return response
        except Exception as e:
            log_error(e, "Exporting customers to CSV", log_full_trace=True)
            raise APIException(f"CSV Export Error: {str(e)}")


from utils.logging import (
    api_logger,
    log_request_data,
    log_response_data,
    log_error,
    sanitize_data,
)


class CustomerView(generics.GenericAPIView):
    """
    Handles adding a new customer (POST) and updating an existing customer (PUT/PATCH).
    Only users with role 'owner' or 'receptionist' (per IsOwnerOrReceptionist) can access these endpoints.
    """

    serializer_class = UserProfileSerializer
    permission_classes = [IsAuthenticated, IsOwnerOrReceptionist]

    def get_object(self):
        """
        Retrieves the customer instance based on the provided customer_id URL kwarg.
        Only customers (role='customer') are allowed.
        """
        customer_id = self.kwargs.get("customer_id")
        if not customer_id:
            api_logger.warning("❌ Customer ID not provided for update")
            raise NotFound("Customer ID not provided for update.")

        try:
            api_logger.debug(f"🔍 Looking up customer with ID: {customer_id}")
            customer = User.objects.get(id=customer_id, role="customer")
            api_logger.debug(f"✅ Found customer: {customer.id} ({customer.email})")
            return customer
        except User.DoesNotExist:
            api_logger.warning(f"❌ Customer not found with ID: {customer_id}")
            raise NotFound("Customer not found.")

    def post(self, request, *args, **kwargs):
        """
        Create a new customer.
        URL: /customer_add/
        """
        log_request_data(request, "📥 Create Customer Request")

        try:
            api_logger.info("🔄 Processing new customer creation")
            serializer = self.get_serializer(data=request.data)
            serializer.is_valid(raise_exception=True)

            # Ensure the created user is a customer regardless of the incoming data.
            customer = serializer.save(role="customer")

            # Log success with sanitized data
            safe_data = sanitize_data(serializer.data, ["password"])
            api_logger.info(
                f"✅ Customer created: ID={customer.id}, Email={customer.email}"
            )
            api_logger.debug(f"Customer details: {safe_data}")

            response = Response(serializer.data, status=status.HTTP_201_CREATED)
            log_response_data(response, "📤 Create Customer Response")
            return response

        except Exception as e:
            log_error(e, "Creating customer", log_full_trace=True)
            raise

    def put(self, request, *args, **kwargs):
        """
        Full update of an existing customer.
        URL: /customer/<int:customer_id>/
        """
        log_request_data(request, "📥 Update Customer (PUT) Request")

        try:
            customer = self.get_object()
            api_logger.info(f"🔄 Processing full update for customer: {customer.id}")

            serializer = self.get_serializer(customer, data=request.data)
            serializer.is_valid(raise_exception=True)
            serializer.save()

            # Log success with sanitized data
            safe_data = sanitize_data(serializer.data, ["password"])
            api_logger.info(
                f"✅ Customer updated via PUT: ID={customer.id}, Email={customer.email}"
            )
            api_logger.debug(f"Updated customer details: {safe_data}")

            response = Response(serializer.data, status=status.HTTP_200_OK)
            log_response_data(response, "📤 Update Customer (PUT) Response")
            return response

        except Exception as e:
            log_error(
                e,
                f"Updating customer (PUT) for ID: {kwargs.get('customer_id')}",
                log_full_trace=True,
            )
            raise

    def patch(self, request, *args, **kwargs):
        """
        Partial update of an existing customer.
        URL: /customer/<int:customer_id>/
        """
        log_request_data(request, "📥 Update Customer (PATCH) Request")

        try:
            customer = self.get_object()
            api_logger.info(f"🔄 Processing partial update for customer: {customer.id}")

            serializer = self.get_serializer(customer, data=request.data, partial=True)
            serializer.is_valid(raise_exception=True)
            serializer.save()

            # Log success with sanitized data
            safe_data = sanitize_data(serializer.data, ["password"])
            api_logger.info(
                f"✅ Customer updated via PATCH: ID={customer.id}, Email={customer.email}"
            )
            api_logger.debug(f"Updated fields: {list(request.data.keys())}")

            response = Response(serializer.data, status=status.HTTP_200_OK)
            log_response_data(response, "📤 Update Customer (PATCH) Response")
            return response

        except Exception as e:
            log_error(
                e,
                f"Updating customer (PATCH) for ID: {kwargs.get('customer_id')}",
                log_full_trace=True,
            )
            raise

    def delete(self, request, *args, **kwargs):
        """
        Delete an existing customer.
        URL: /customer/<int:customer_id>/
        """
        log_request_data(request, "📥 Delete Customer Request")

        try:
            customer = self.get_object()
            customer_id = customer.id
            customer_email = customer.email

            api_logger.info(f"🔄 Processing deletion of customer: {customer_id}")
            customer.delete()

            api_logger.info(
                f"✅ Customer deleted: ID={customer_id}, Email={customer_email}"
            )

            response = Response(status=status.HTTP_204_NO_CONTENT)
            log_response_data(response, "📤 Delete Customer Response")
            return response

        except Exception as e:
            log_error(
                e,
                f"Deleting customer for ID: {kwargs.get('customer_id')}",
                log_full_trace=True,
            )
            raise


class CustomerProfileView(APIView):
    """
    API view for retrieving a customer's profile with all their data.
    Only owner and receptionist roles can access this endpoint.
    """

    permission_classes = [IsAuthenticated, IsOwnerOrReceptionist]

    def get(self, request, customer_id):
        """
        Get a customer's profile with associated data:
        - User profile details
        - Active packages (user, shared, unlimited)
        - Recent appointments (last 10)
        - Package history

        Args:
            customer_id: ID of the customer to retrieve
        """
        log_request_data(request, f"📥 Customer Profile Request: ID={customer_id}")

        try:
            api_logger.info(f"🔍 Retrieving profile for customer ID: {customer_id}")

            # Get customer details
            customer = get_object_or_404(User, id=customer_id, role="customer")
            api_logger.debug(f"Customer found: {customer.email}")
            customer_serializer = UserProfileSerializer(customer)

            # Get active packages
            api_logger.debug(f"Fetching active packages for customer: {customer_id}")
            active_user_package = UserPackage.objects.filter(
                user=customer, active=True, remaining_time__gt=0
            )

            active_shared_packages = SharedPackage.objects.filter(
                users__user=customer, active=True, remaining_time__gt=0
            )

            active_unlimited_package = UnlimitedPackage.objects.filter(
                user=customer, active=True
            )

            # Get recent appointments
            api_logger.debug(f"Fetching recent appointments for customer: {customer_id}")
            recent_appointments = Appointment.objects.filter(customer=customer).order_by(
                "-date", "-time"
            )[:10]

            # Get all packages (for history)
            api_logger.debug(f"Fetching package history for customer: {customer_id}")
            all_user_packages = UserPackage.objects.filter(user=customer).order_by(
                "-created_at"
            )
            all_shared_packages = SharedPackage.objects.filter(
                users__user=customer
            ).order_by("-created_at")
            all_unlimited_packages = UnlimitedPackage.objects.filter(
                user=customer
            ).order_by("-created_at")

            # Get usage statistics
            api_logger.debug(f"Calculating usage statistics for customer: {customer_id}")
            total_appointments = Appointment.objects.filter(customer=customer).count()
            total_spent = (
                Appointment.objects.filter(customer=customer).aggregate(
                    Sum("total_price")
                )["total_price__sum"]
                or 0
            )
            last_appointment = (
                Appointment.objects.filter(customer=customer)
                .order_by("-date", "-time")
                .first()
            )

            # Calculate no-show rate
            total_bookings = Appointment.objects.filter(customer=customer).count()
            no_shows = Appointment.objects.filter(
                customer=customer, status="cancelled"
            ).count()
            no_show_rate = (no_shows / total_bookings * 100) if total_bookings > 0 else 0
            api_logger.debug(
                f"Customer {customer_id} no-show rate: {round(no_show_rate, 2)}%"
            )

            # Get reward balance
            api_logger.debug(f"Fetching rewards for customer: {customer_id}")
            try:
                reward_balance_data = RewardBalanceSerializer(
                    customer.reward_balance
                ).data
            except RewardBalance.DoesNotExist:
                api_logger.debug(f"No reward balance found for customer: {customer_id}")
                reward_balance_data = {"total_points": 0}

            # Prepare response data
            response_data = {
                "customer": customer_serializer.data,
                "stats": {
                    "total_appointments": total_appointments,
                    "total_spent": float(total_spent),
                    "no_show_rate": round(no_show_rate, 2),
                    "last_appointment": (
                        {
                            "date": (
                                last_appointment.date.isoformat()
                                if last_appointment
                                else None
                            ),
                            "time": (
                                last_appointment.time.isoformat()
                                if last_appointment
                                else None
                            ),
                            "id": last_appointment.id if last_appointment else None,
                        }
                        if last_appointment
                        else None
                    ),
                },
                "active_packages": {
                    "user_packages": (
                        UserPackageSerializer(active_user_package, many=True).data
                        if active_user_package
                        else None
                    ),
                    "shared_packages": SharedPackageSerializer(
                        active_shared_packages, many=True
                    ).data,
                    "unlimited_packages": (
                        UnlimitedPackageSerializer(
                            active_unlimited_package, many=True
                        ).data
                        if active_unlimited_package
                        else None
                    ),
                },
                "recent_appointments": AppointmentSerializer(
                    recent_appointments, many=True
                ).data,
                "package_history": {
                    "user_packages": UserPackageSerializer(
                        all_user_packages, many=True
                    ).data,
                    "shared_packages": SharedPackageSerializer(
                        all_shared_packages, many=True
                    ).data,
                    "unlimited_packages": UnlimitedPackageSerializer(
                        all_unlimited_packages, many=True
                    ).data,
                },
                "rewards": AdminRewardSerializer(
                    customer.rewards.order_by("-created_at")[:10], many=True
                ).data,
                "reward_balance": reward_balance_data,
            }

            api_logger.info(
                f"✅ Successfully retrieved profile for customer: {customer_id}"
            )
            api_logger.debug(
                f"Profile stats: {total_appointments} appointments, ${float(total_spent)} spent"
            )

            response = Response(response_data, status=status.HTTP_200_OK)
            log_response_data(response, f"📤 Customer Profile Response: ID={customer_id}")
            return response

        except User.DoesNotExist:
            api_logger.warning(f"❌ Customer not found with ID: {customer_id}")
            return Response(
                {"detail": "Customer not found"}, status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            log_error(
                e,
                f"Retrieving profile for customer ID: {customer_id}",
                log_full_trace=True,
            )
            return Response(
                {"detail": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class CustomerAppointmentsView(APIView):
    """
    API view for retrieving a customer's appointments with date range filtering
    and CSV export functionality.
    Only owner and receptionist roles can access this endpoint.
    """

    permission_classes = [IsAuthenticated, IsOwnerOrReceptionist]

    def get(self, request, customer_id=None):
        """
        Get appointments within a date range with optional filtering.

        Query params:
        - start_date: Start date (YYYY-MM-DD)
        - end_date: End date (YYYY-MM-DD)
        - export: Set to 'csv' to export as CSV
        - status: Filter by appointment status

        If customer_id is provided, only that customer's appointments are returned.
        """
        log_request_data(
            request,
            f"📥 Appointments Request: {'Customer ID=' + str(customer_id) if customer_id else 'All Customers'}",
        )

        try:
            # Get query parameters
            start_date = request.query_params.get("start_date")
            end_date = request.query_params.get("end_date")
            export_format = request.query_params.get("export")
            status_filter = request.query_params.get("status")

            # Default to last 30 days if no dates provided
            if not start_date:
                start_date = (timezone.now() - timedelta(days=30)).date().isoformat()
                api_logger.debug(f"No start_date provided, defaulting to: {start_date}")
            if not end_date:
                end_date = timezone.now().date().isoformat()
                api_logger.debug(f"No end_date provided, defaulting to: {end_date}")

            # Build the query
            query = Q(date__gte=start_date) & Q(date__lte=end_date)
            api_logger.info(
                f"🔍 Searching appointments between {start_date} and {end_date}"
            )

            # Add customer filter if customer_id is provided
            customer_info = ""
            if customer_id:
                customer = get_object_or_404(User, id=customer_id, role="customer")
                query &= Q(customer=customer)
                customer_info = f" for customer {customer.id} ({customer.email})"
                api_logger.debug(f"Filtering appointments for customer ID: {customer_id}")

            # Add status filter if provided
            if status_filter:
                query &= Q(status=status_filter)
                api_logger.debug(f"Filtering appointments by status: {status_filter}")

            # Get appointments
            appointments = Appointment.objects.filter(query).order_by("-date", "-time")
            appointment_count = appointments.count()
            api_logger.info(
                f"🔄 Retrieved {appointment_count} appointments{customer_info}"
            )

            # Export to CSV if requested
            if export_format == "csv":
                api_logger.info(
                    f"🔄 Exporting {appointment_count} appointments to CSV{customer_info}"
                )
                response = self._export_to_csv(appointments, customer_id)
                log_response_data(response, "📤 Appointments CSV Export Response")
                return response

            # Regular JSON response
            appointments_serializer = AppointmentSerializer(appointments, many=True)

            # Count total appointments and sum of total price
            total_price = (
                appointments.aggregate(Sum("total_price"))["total_price__sum"] or 0
            )
            api_logger.debug(
                f"Total appointments: {appointment_count}, Total price: {float(total_price)}"
            )

            response_data = {
                "appointments": appointments_serializer.data,
                "meta": {
                    "total_count": appointment_count,
                    "total_price": float(total_price),
                    "start_date": start_date,
                    "end_date": end_date,
                },
            }

            api_logger.info(f"✅ Successfully retrieved appointments{customer_info}")
            response = Response(response_data, status=status.HTTP_200_OK)
            log_response_data(response, "📤 Appointments Response")
            return response

        except User.DoesNotExist:
            api_logger.warning(f"❌ Customer not found with ID: {customer_id}")
            return Response(
                {"detail": "Customer not found"}, status=status.HTTP_404_NOT_FOUND
            )
        except Exception as e:
            log_error(
                e,
                f"Retrieving appointments{' for customer ID: ' + str(customer_id) if customer_id else ''}",
                log_full_trace=True,
            )
            return Response(
                {"detail": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def _export_to_csv(self, appointments, customer_id=None):
        """
        Helper method to export appointments to CSV
        """
        try:
            response = HttpResponse(content_type="text/csv")

            # Set filename with date range and optionally customer name
            filename = "appointments"
            if customer_id:
                try:
                    customer = User.objects.get(id=customer_id)
                    customer_name = f"{customer.first_name}_{customer.last_name}"
                    filename += f"_{customer_name}"
                    api_logger.debug(f"Adding customer name to filename: {customer_name}")
                except User.DoesNotExist:
                    filename += f"_customer_{customer_id}"
                    api_logger.warning(
                        f"Customer not found for CSV filename: {customer_id}"
                    )

            filename += f"_{timezone.now().strftime('%Y%m%d')}.csv"
            api_logger.debug(f"CSV export filename: {filename}")
            response["Content-Disposition"] = f'attachment; filename="{filename}"'

            # Create CSV writer
            writer = csv.writer(response)

            # Write headers
            writer.writerow(
                [
                    "ID",
                    "Date",
                    "Time",
                    "Customer",
                    "Email",
                    "Phone",
                    "Therapist",
                    "Service",
                    "Duration",
                    "Price",
                    "Status",
                    "Location",
                    "Reward Points",
                ]
            )

            # Write data rows
            rows_written = 0
            for appointment in appointments:
                # Prepare services list as comma-joined string
                services = ", ".join(
                    [
                        f"{service.service.name} ({service.duration}min)"
                        for service in appointment.appointment_services.all()
                    ]
                )

                writer.writerow(
                    [
                        appointment.id,
                        appointment.date,
                        appointment.time,
                        f"{appointment.customer.first_name} {appointment.customer.last_name}",
                        appointment.customer.email,
                        appointment.customer.phone_number,
                        (
                            f"{appointment.therapist.user.first_name} {appointment.therapist.user.last_name}"
                            if appointment.therapist
                            else "N/A"
                        ),
                        services or "Package Service",
                        appointment.total_duration,
                        appointment.total_price,
                        appointment.status,
                        appointment.location,
                        (
                            appointment.reward.points
                            if hasattr(appointment, "reward") and appointment.reward
                            else 0
                        ),
                    ]
                )
                rows_written += 1

            api_logger.info(
                f"✅ Successfully exported {rows_written} appointment records to CSV"
            )
            return response

        except Exception as e:
            log_error(
                e,
                f"Exporting appointments to CSV{' for customer ID: ' + str(customer_id) if customer_id else ''}",
                log_full_trace=True,
            )
            raise


class CustomerActivePackageView(generics.RetrieveAPIView):
    """
    Retrieves the active package for a specific customer.
    """

    serializer_class = UserPackageSerializer
    permission_classes = [IsAuthenticated, IsOwnerOrReceptionist]

    def get(self, request, customer_id, *args, **kwargs):
        log_request_data(request, f"📥 Active Package Request for Customer:{customer_id}")

        try:
            api_logger.info(f"🔍 Looking up customer with ID:{customer_id}")
            customer = get_object_or_404(User, id=customer_id, role="customer")

            api_logger.info(f"🔄 Fetching active package for Customer:{customer_id}")
            active_package = UserPackage.objects.filter(
                user=customer, active=True, remaining_time__gt=MIN_ACTIVE_MINUTES
            ).first()

            if active_package:
                api_logger.info(
                    f"✅ Found active package for Customer:{customer_id}, PackageID:{active_package.id}"
                )
                serializer = self.get_serializer(active_package)
                response = Response(serializer.data, status=200)
                log_response_data(
                    response, f"📤 Active Package Response for Customer:{customer_id}"
                )
                return response

            api_logger.warning(f"❌ No active package found for Customer:{customer_id}")
            response = Response({"detail": "No active package found."}, status=404)
            log_response_data(
                response, f"📤 No Active Package Response for Customer:{customer_id}"
            )
            return response

        except Exception as e:
            log_error(
                e,
                f"Processing active package request for Customer:{customer_id}",
                log_full_trace=True,
            )
            raise APIException(f"Internal Server Error: {str(e)}")


class CustomerAllPackagesView(generics.ListAPIView):
    """
    Retrieves all packages (active and inactive) for a specific customer.
    """

    serializer_class = UserPackageSerializer
    permission_classes = [IsAuthenticated, IsOwnerOrReceptionist]

    def get_queryset(self):
        customer_id = self.kwargs.get("customer_id")
        # Filter by active status if specified in query params
        active_filter = self.request.query_params.get("active")

        api_logger.info(f"🔍 Retrieving packages for Customer:{customer_id}")
        customer = get_object_or_404(User, id=customer_id, role="customer")

        queryset = UserPackage.objects.filter(user=customer)

        # Apply active filter if specified
        if active_filter:
            is_active = active_filter.lower() == "true"
            queryset = queryset.filter(active=is_active)
            if is_active:
                queryset = queryset.filter(remaining_time__gt=MIN_ACTIVE_MINUTES)

        return queryset.order_by("-created_at")

    def list(self, request, *args, **kwargs):
        log_request_data(
            request,
            f"📥 All Packages Request for Customer:{self.kwargs.get('customer_id')}",
        )

        try:
            queryset = self.get_queryset()
            serializer = self.get_serializer(queryset, many=True)

            response_data = {"count": len(serializer.data), "results": serializer.data}

            response = Response(response_data, status=200)
            log_response_data(
                response,
                f"📤 All Packages Response for Customer:{self.kwargs.get('customer_id')}",
            )
            return response

        except Exception as e:
            log_error(
                e,
                f"Processing all packages request for Customer:{self.kwargs.get('customer_id')}",
                log_full_trace=True,
            )
            raise APIException(f"Internal Server Error: {str(e)}")


class CustomerAllSharedPackagesView(generics.ListAPIView):
    """
    Retrieves all shared packages (active and inactive) for a specific customer.
    """

    serializer_class = SharedPackageSerializer
    permission_classes = [IsAuthenticated, IsOwnerOrReceptionist]

    def get_queryset(self):
        customer_id = self.kwargs.get("customer_id")
        # Filter by active status if specified in query params
        active_filter = self.request.query_params.get("active")

        api_logger.info(f"🔍 Retrieving shared packages for Customer:{customer_id}")
        customer = get_object_or_404(User, id=customer_id, role="customer")

        queryset = SharedPackage.objects.filter(users__user=customer)

        # Apply active filter if specified
        if active_filter:
            is_active = active_filter.lower() == "true"
            queryset = queryset.filter(active=is_active)
            if is_active:
                queryset = queryset.filter(remaining_time__gt=MIN_ACTIVE_MINUTES)

        return queryset.order_by("-created_at")

    def list(self, request, *args, **kwargs):
        log_request_data(
            request,
            f"📥 All Shared Packages Request for Customer:{self.kwargs.get('customer_id')}",
        )

        try:
            queryset = self.get_queryset()
            serializer = self.get_serializer(queryset, many=True)

            response_data = {"count": len(serializer.data), "results": serializer.data}

            response = Response(response_data, status=200)
            log_response_data(
                response,
                f"📤 All Shared Packages Response for Customer:{self.kwargs.get('customer_id')}",
            )
            return response

        except Exception as e:
            log_error(
                e,
                f"Processing all shared packages request for Customer:{self.kwargs.get('customer_id')}",
                log_full_trace=True,
            )
            raise APIException(f"Internal Server Error: {str(e)}")


class CustomerAllUnlimitedPackagesView(generics.ListAPIView):
    """
    Retrieves all unlimited packages (active and inactive) for a specific customer.
    """

    serializer_class = UnlimitedPackageSerializer
    permission_classes = [IsAuthenticated, IsOwnerOrReceptionist]

    def get_queryset(self):
        customer_id = self.kwargs.get("customer_id")
        # Filter by active status if specified in query params
        active_filter = self.request.query_params.get("active")

        api_logger.info(f"🔍 Retrieving unlimited packages for Customer:{customer_id}")
        customer = get_object_or_404(User, id=customer_id, role="customer")

        queryset = UnlimitedPackage.objects.filter(user=customer)

        # Apply active filter if specified
        if active_filter:
            is_active = active_filter.lower() == "true"
            queryset = queryset.filter(active=is_active)
            if is_active:
                queryset = queryset.filter(remaining_time__gt=MIN_ACTIVE_MINUTES)

        return queryset.order_by("-created_at")

    def list(self, request, *args, **kwargs):
        log_request_data(
            request,
            f"📥 All Unlimited Packages Request for Customer:{self.kwargs.get('customer_id')}",
        )

        try:
            queryset = self.get_queryset()
            serializer = self.get_serializer(queryset, many=True)

            response_data = {"count": len(serializer.data), "results": serializer.data}

            response = Response(response_data, status=200)
            log_response_data(
                response,
                f"📤 All Unlimited Packages Response for Customer:{self.kwargs.get('customer_id')}",
            )
            return response

        except Exception as e:
            log_error(
                e,
                f"Processing all unlimited packages request for Customer:{self.kwargs.get('customer_id')}",
                log_full_trace=True,
            )
            raise APIException(f"Internal Server Error: {str(e)}")
