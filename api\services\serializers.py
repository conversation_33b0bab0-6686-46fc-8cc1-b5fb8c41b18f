from rest_framework import serializers
from .models import Service, ServiceDuration, ServicePackage, PackageOption
from django.contrib.auth import get_user_model
from api.staff.models import TherapistProfile

User = get_user_model()


class UserMinimalSerializer(serializers.ModelSerializer):
    """Simple serializer for user information."""

    class Meta:
        model = User
        fields = ["id", "first_name", "last_name"]


class TherapistMinimalSerializer(serializers.ModelSerializer):
    """Simple serializer for therapist information."""

    user = UserMinimalSerializer(read_only=True)

    class Meta:
        model = TherapistProfile
        fields = ["id", "user"]


class ServiceDurationSerializer(serializers.ModelSerializer):
    """
    Serializer for ServiceDuration model.
    """

    class Meta:
        model = ServiceDuration
        fields = ["id", "time", "price"]


class ServiceSerializer(serializers.ModelSerializer):
    """
    Serializer for Service model.
    Includes related durations.
    """

    durations = ServiceDurationSerializer(many=True, read_only=True)
    created_by = UserMinimalSerializer(read_only=True)
    therapist_specific = TherapistMinimalSerializer(read_only=True)

    class Meta:
        model = Service
        fields = [
            "id",
            "name",
            "description",
            "durations",
            "is_public",
            "created_by",
            "therapist_specific",
            "created_at",
            "updated_at",
        ]


class ServiceCreateUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating and updating Service records.
    """

    durations = ServiceDurationSerializer(many=True, required=False)

    class Meta:
        model = Service
        fields = [
            "id",
            "name",
            "description",
            "durations",
            "is_public",
            "therapist_specific",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]

    def create(self, validated_data):
        durations_data = validated_data.pop("durations", [])
        service = Service.objects.create(**validated_data)

        for duration_data in durations_data:
            ServiceDuration.objects.create(service=service, **duration_data)

        return service

    def update(self, instance, validated_data):
        durations_data = validated_data.pop("durations", None)

        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        if durations_data is not None:
            # Remove existing durations and create new ones
            instance.durations.all().delete()
            for duration_data in durations_data:
                ServiceDuration.objects.create(service=instance, **duration_data)

        return instance


class PackageOptionViewSerializer(serializers.ModelSerializer):
    """
    Serializer for PackageOption model.
    """

    class Meta:
        model = PackageOption
        fields = ["id", "time", "price"]


class ServicePackageViewSerializer(serializers.ModelSerializer):
    """
    Serializer for ServicePackage model.
    Includes related options and services.
    """

    services_included = ServiceSerializer(many=True, read_only=True)

    class Meta:
        model = ServicePackage
        fields = [
            "id",
            "name",
            "description",
            "services_included",
            "benefits",
            "created_at",
            "updated_at",
        ]


class PackageOptionSerializer(serializers.ModelSerializer):
    """
    Serializer for PackageOption model.
    """

    package = ServicePackageViewSerializer(read_only=True)

    class Meta:
        model = PackageOption
        fields = ["id", "time", "price", "package"]


class ServicePackageSerializer(serializers.ModelSerializer):
    """
    Serializer for ServicePackage model.
    Includes related options and services.
    """

    options = PackageOptionViewSerializer(many=True, read_only=True)
    services_included = ServiceSerializer(many=True, read_only=True)
    created_by = UserMinimalSerializer(read_only=True)
    therapist_specific = TherapistMinimalSerializer(read_only=True)

    class Meta:
        model = ServicePackage
        fields = [
            "id",
            "name",
            "description",
            "services_included",
            "benefits",
            "options",
            "is_public",
            "shared",
            "unlimited",
            "created_by",
            "therapist_specific",
            "created_at",
            "updated_at",
        ]


class ServicePackageCreateUpdateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating and updating ServicePackage records.
    """

    options = PackageOptionViewSerializer(many=True, required=False)
    services_included = serializers.PrimaryKeyRelatedField(
        queryset=Service.objects.all(), many=True, required=False
    )

    class Meta:
        model = ServicePackage
        fields = [
            "id",
            "name",
            "description",
            "services_included",
            "benefits",
            "options",
            "is_public",
            "shared",
            "unlimited",
            "therapist_specific",
            "created_at",
            "updated_at",
        ]
        read_only_fields = ["id", "created_at", "updated_at"]

    def create(self, validated_data):
        options_data = validated_data.pop("options", [])
        services_data = validated_data.pop("services_included", [])

        package = ServicePackage.objects.create(**validated_data)

        # Add services
        if services_data:
            package.services_included.set(services_data)

        # Create options
        for option_data in options_data:
            PackageOption.objects.create(package=package, **option_data)

        return package

    def update(self, instance, validated_data):
        options_data = validated_data.pop("options", None)
        services_data = validated_data.pop("services_included", None)

        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()

        # Update services if provided
        if services_data is not None:
            instance.services_included.set(services_data)

        # Update options if provided
        if options_data is not None:
            # Remove existing options and create new ones
            instance.options.all().delete()
            for option_data in options_data:
                PackageOption.objects.create(package=instance, **option_data)

        return instance
