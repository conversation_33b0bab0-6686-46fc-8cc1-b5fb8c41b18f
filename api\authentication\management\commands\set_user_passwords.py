from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model

User = get_user_model()

class Command(BaseCommand):
    help = 'Sets a default password for all users without a usable password'

    def add_arguments(self, parser):
        parser.add_argument(
            '--password',
            type=str,
            default='password123',
            help='Default password to set for users (default: password123)',
        )
        
        parser.add_argument(
            '--email',
            type=str,
            help='Set password only for a specific email',
        )

    def handle(self, *args, **options):
        default_password = options['password']
        specific_email = options.get('email')
        
        if specific_email:
            users = User.objects.filter(email=specific_email)
            self.stdout.write(f"Setting password for user with email: {specific_email}")
        else:
            users = User.objects.all()
            self.stdout.write(f"Setting password for all users")
        
        count = 0
        for user in users:
            if not user.password:
                user.set_password(default_password)
                user.save()
                count += 1
                self.stdout.write(f"Set password for user: {user.email}")
        
        self.stdout.write(self.style.SUCCESS(f"Successfully set passwords for {count} users"))