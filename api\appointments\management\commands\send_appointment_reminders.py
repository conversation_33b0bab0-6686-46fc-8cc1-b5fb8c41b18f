from django.core.management.base import BaseCommand
from django.utils import timezone
from django.template.loader import render_to_string
from django.utils.html import strip_tags
from django.core.mail import send_mail
from django.conf import settings
from datetime import timedelta, datetime, time
import logging
import copy

# Import your Appointment model - adjust the import path as needed
from api.appointments.models import Appointment
from api.appointments.serializers import AppointmentSerializer

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = "Sends reminder emails for appointments scheduled within the next 24 hours"

    def handle(self, *args, **options):
        self.stdout.write("Checking for upcoming appointments...")

        # Get current time
        now = timezone.now()

        # Calculate 24 hours from now
        reminder_window_end = now + timedelta(hours=24)

        # Find appointments that:
        # 1. Are scheduled within the next 24 hours
        # 2. Haven't had a reminder sent yet
        # 3. Are still in 'booked' status (not cancelled or completed)
        upcoming_appointments = self._get_upcoming_appointments(now, reminder_window_end)

        self.stdout.write(
            f"Found {len(upcoming_appointments)} appointments to send reminders for"
        )

        # Send reminders for each appointment
        reminders_sent = 0
        for appointment in upcoming_appointments:
            try:
                # Serialize the appointment data
                serialized_appointment = AppointmentSerializer(appointment).data

                # Send the reminder email
                self._send_reminder_email(serialized_appointment)

                # Mark that a reminder has been sent
                appointment.reminder_sent = True
                appointment.save(update_fields=["reminder_sent"])

                reminders_sent += 1
                self.stdout.write(
                    f"Sent reminder for appointment {appointment.id} to {appointment.customer.email}"
                )
            except Exception as e:
                logger.error(
                    f"Failed to send reminder for appointment {appointment.id}: {str(e)}",
                    exc_info=True,
                )
                self.stdout.write(
                    self.style.ERROR(
                        f"Failed to send reminder for appointment {appointment.id}: {str(e)}"
                    )
                )

        self.stdout.write(
            self.style.SUCCESS(
                f"Successfully sent {reminders_sent} appointment reminders"
            )
        )

    def _get_upcoming_appointments(self, now, reminder_window_end):
        """
        Get appointments in the next 24 hours that need reminders.
        """
        # Convert date and time fields into a datetime for comparison
        # This approach may need adjustment based on how you store dates and times
        upcoming_appointments = []

        # Get all appointments with status 'booked'
        appointments = Appointment.objects.filter(status="booked")

        for appointment in appointments:
            # Parse the appointment date and time
            try:
                # Assuming appointment.date is a date and appointment.time is a time string like "14:30"
                appt_date = appointment.date  # Should be a date object
                hour = appointment.time.hour
                minute = appointment.time.minute

                # Create a datetime object combining the date and time
                appt_datetime = timezone.make_aware(
                    datetime.combine(appt_date, time(hour, minute))
                )

                # Check if appointment is within the 24-hour window
                if now <= appt_datetime <= reminder_window_end:
                    # Check if reminder has already been sent
                    if (
                        not hasattr(appointment, "reminder_sent")
                        or not appointment.reminder_sent
                    ):
                        upcoming_appointments.append(appointment)
            except Exception as e:
                logger.error(
                    f"Error processing appointment {appointment.id}: {str(e)}",
                    exc_info=True,
                )
                continue

        return upcoming_appointments

    def _send_reminder_email(self, serialized_appointment):
        """
        Send reminder email for a specific appointment with properly formatted dates.

        Args:
            serialized_appointment: The serialized appointment data from AppointmentSerializer
        """

        # Format dates for better readability
        def format_serialized_appointment_dates(data):
            """Format dates in the serialized appointment data for email templates."""
            # Deep copy to avoid modifying the original
            formatted_data = copy.deepcopy(data)

            # Format the main appointment date
            if "date" in formatted_data:
                try:
                    date_obj = datetime.strptime(formatted_data["date"], "%Y-%m-%d")
                    formatted_data["date"] = date_obj.strftime(
                        "%B %d, %Y"
                    )  # March 25, 2025
                except (ValueError, TypeError):
                    pass

            # Format appointment time
            if "time" in formatted_data:
                try:
                    time_obj = datetime.strptime(formatted_data["time"], "%H:%M:%S")
                    formatted_data["time"] = time_obj.strftime("%I:%M %p")  # 2:30 PM
                except (ValueError, TypeError):
                    pass

            # Format package expiry dates
            # User Package
            if (
                "user_package_obj" in formatted_data
                and formatted_data["user_package_obj"]
            ):
                if "expiry_date" in formatted_data["user_package_obj"]:
                    try:
                        date_str = formatted_data["user_package_obj"]["expiry_date"]
                        date_obj = datetime.strptime(date_str, "%Y-%m-%d")
                        formatted_data["user_package_obj"]["expiry_date"] = (
                            date_obj.strftime("%B %d, %Y")
                        )
                    except (ValueError, TypeError):
                        pass

            # Shared Package
            if (
                "shared_package_obj" in formatted_data
                and formatted_data["shared_package_obj"]
            ):
                if "expiry_date" in formatted_data["shared_package_obj"]:
                    try:
                        date_str = formatted_data["shared_package_obj"]["expiry_date"]
                        date_obj = datetime.strptime(date_str, "%Y-%m-%d")
                        formatted_data["shared_package_obj"]["expiry_date"] = (
                            date_obj.strftime("%B %d, %Y")
                        )
                    except (ValueError, TypeError):
                        pass

            # Unlimited Package
            if (
                "unlimited_package_obj" in formatted_data
                and formatted_data["unlimited_package_obj"]
            ):
                if "expiry_date" in formatted_data["unlimited_package_obj"]:
                    try:
                        date_str = formatted_data["unlimited_package_obj"]["expiry_date"]
                        date_obj = datetime.strptime(date_str, "%Y-%m-%d")
                        formatted_data["unlimited_package_obj"]["expiry_date"] = (
                            date_obj.strftime("%B %d, %Y")
                        )
                    except (ValueError, TypeError):
                        pass

            return formatted_data

        # Format dates in the appointment data
        formatted_appointment = format_serialized_appointment_dates(
            serialized_appointment
        )

        # Get time for subject line (using the formatted time if available)
        time_for_subject = formatted_appointment.get("time", "")

        subject = f"Reminder: Your appointment at {time_for_subject}"

        # Prepare context for email template
        context = {"appointment": formatted_appointment}

        # Render HTML email template
        html_message = render_to_string("email/app_reminder.html", context)
        plain_message = strip_tags(html_message)

        # Get recipient email from serialized data
        recipient_email = formatted_appointment["customer_obj"]["email"]

        # Check if in development/staging environment to redirect emails
        if settings.DEBUG or getattr(settings, "ENVIRONMENT", "production") in [
            "local",
            "staging",
        ]:
            # Optionally redirect emails in non-production environments
            recipient_email = settings.DEFAULT_FROM_EMAIL

        # Send the email
        send_mail(
            subject=subject,
            message=plain_message,
            from_email=settings.DEFAULT_FROM_EMAIL,
            recipient_list=[recipient_email],
            html_message=html_message,
            fail_silently=False,
        )
