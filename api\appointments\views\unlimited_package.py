from datetime import datetime, timed<PERSON>ta
import calendar
from django.db.models import Q, Sum
from django.utils.dateparse import parse_date
from rest_framework.exceptions import ValidationError
from rest_framework import status
from rest_framework import generics
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from rest_framework.pagination import PageNumberPagination
from rest_framework.views import APIView
from django.shortcuts import get_object_or_404
from django.contrib.auth import get_user_model
from rest_framework.filters import SearchFilter
from django.http import HttpResponse
from django_filters.rest_framework import DjangoFilterBackend
from api.appointments.models import (
    Activity,
    Appointment,
    AppointmentService,
    UserPackage,
    SharedPackage,
    SharedPackageUser,
    UnlimitedPackage,
    Discount,
    RewardBalance,
)
from api.services.models import Service, ServiceDuration, PackageOption
from api.appointments.serializers import (
    AppointmentSerializer,
    UserPackageSerializer,
    SharedPackageSerializer,
    UnlimitedPackageSerializer,
    AddMinutesToAppointmentSerializer,
    AppointmentAdditionalServiceSerializer,
    AppointmentAdditionalService,
    SharedPackageUserSerializer,
    PackageUsageSerializer,
    DiscountSerializer,
    AdminRewardSerializer,
    RewardBalanceSerializer,
)

from api.core.permissions import IsOwnerOrReceptionist
from api.staff.models import TherapistProfile, WorkingHour
from api.authentication.serializers import UserProfileSerializer
import traceback
from decimal import Decimal
from rest_framework.exceptions import NotFound, APIException, PermissionDenied
from api.staff.serializers import (
    TherapistProfileOptimizedSerializer,
)
from django.utils import timezone
import csv

User = get_user_model()

from django.template.loader import render_to_string
from django.utils.html import strip_tags
from django.core.mail import send_mail
from django.conf import settings

from .utils import PackageViewMixin

# Constants
MIN_ACTIVE_MINUTES = 0
BREAK_TIME = 10  # minutes break between appointments
NEW_APPOINTMENT_BREAK = 10  # minutes

from utils.logging import api_logger, log_request_data, log_response_data, log_error


class ActiveUnlimitedPackage(generics.RetrieveAPIView):
    serializer_class = UnlimitedPackageSerializer
    permission_classes = [IsAuthenticated]

    def get(self, request):
        user = request.user
        # Query for an active UserPackage for the user.
        active_package = UnlimitedPackage.objects.filter(
            user=user, active=True, remaining_time__gte=MIN_ACTIVE_MINUTES
        ).first()

        if active_package:
            serializer = self.get_serializer(active_package)
            return Response(serializer.data, status=200)
        else:
            return Response({"detail": "No active package found"}, status=404)


class AppointmentUnlimitedPackageView(generics.ListCreateAPIView):
    serializer_class = AppointmentSerializer
    permission_classes = [IsAuthenticated]

    def get_queryset(self):
        user = self.request.user
        api_logger.info(
            f"🔍 Fetching unlimited package appointments for User:{user.id} Role:{user.role}"
        )

        qs = Appointment.objects.filter(unlimited_package__isnull=False).order_by(
            "-created_at"
        )

        if user.role in ["owner", "receptionist"]:
            api_logger.debug(
                f"Admin user {user.id} accessing all unlimited package appointments"
            )
            return qs

        api_logger.debug(
            f"Customer {user.id} accessing their unlimited package appointments"
        )
        return qs.filter(customer=user)

    def perform_create(self, serializer):
        user = self.request.user
        api_logger.info(f"🔄 Creating unlimited package appointment for User:{user.id}")
        log_request_data(self.request, "Unlimited Package Appointment Creation")

        try:
            data = self.request.data

            if data.get("serviceType") != "unlimited_package":
                api_logger.warning(
                    f"❌ Invalid serviceType for unlimited package booking: {data.get('serviceType')}"
                )
                raise ValidationError(
                    {"detail": "Invalid serviceType for unlimited package booking."}
                )

            # Determine the customer.
            if user.role in ["owner", "receptionist"]:
                api_logger.info(f"Admin creating appointment for customer")
                customer_data = data.get("customer")
                if not customer_data:
                    api_logger.warning("❌ Missing customer data for admin booking")
                    raise ValidationError({"detail": "Customer data is required."})

                email = customer_data.get("email", "").strip().lower()
                phone_number = customer_data.get("phone_number", "").strip()

                customer = None
                if email:
                    customer = User.objects.filter(email=email).first()
                    api_logger.debug(
                        f"Customer lookup by email: {email}, found: {customer is not None}"
                    )

                if not customer and phone_number:
                    customer = User.objects.filter(phone_number=phone_number).first()
                    api_logger.debug(
                        f"Customer lookup by phone: {phone_number}, found: {customer is not None}"
                    )

                if not customer:
                    api_logger.info(f"Creating new customer with email: {email}")
                    customer = User.objects.create(
                        email=email,
                        first_name=customer_data.get("first_name", ""),
                        last_name=customer_data.get("last_name", ""),
                        phone_number=phone_number,
                        role="customer",
                    )
                    customer.set_unusable_password()
                    customer.save()
            else:
                customer = user
                api_logger.debug(
                    f"Customer creating their own appointment, User:{customer.id}"
                )

            # Get the duration for this booking.
            try:
                total_duration = int(data.get("duration"))
                api_logger.debug(f"Appointment duration: {total_duration} minutes")
            except (TypeError, ValueError):
                api_logger.warning(
                    f"❌ Invalid duration provided: {data.get('duration')}"
                )
                raise ValidationError({"detail": "Invalid duration provided."})

            total_price = Decimal("0")
            # Retrieve or create an unlimited package for the customer.
            try:
                unlimited_package = UnlimitedPackage.objects.get(
                    user=customer, active=True
                )
                api_logger.info(
                    f"Using existing unlimited package ID:{unlimited_package.id} for User:{customer.id}"
                )
            except UnlimitedPackage.DoesNotExist:
                # Create a new unlimited package and assign a default package_option
                default_option = PackageOption.objects.filter(
                    package__unlimited=True
                ).first()
                total_price = default_option.price
                api_logger.info(
                    f"Creating new unlimited package for User:{customer.id}, Price:{total_price}"
                )
                unlimited_package = UnlimitedPackage.objects.create(
                    user=customer, package_option=default_option
                )

            # Check remaining time
            api_logger.debug(
                f"Package remaining time before deduction: {unlimited_package.remaining_time} minutes"
            )
            if unlimited_package.remaining_time < total_duration:
                api_logger.warning(
                    f"❌ Not enough time in package ID:{unlimited_package.id}, Remaining:{unlimited_package.remaining_time}, Needed:{total_duration}"
                )
                raise ValidationError(
                    {"detail": "Not enough remaining time in the unlimited package."}
                )

            # Update package time
            unlimited_package.remaining_time -= total_duration
            unlimited_package.time_deducted += total_duration
            if unlimited_package.remaining_time <= 0:
                api_logger.warning(
                    f"Package ID:{unlimited_package.id} depleted, marking as inactive"
                )
                unlimited_package.active = False
            unlimited_package.save()
            api_logger.debug(
                f"Package remaining time after deduction: {unlimited_package.remaining_time} minutes"
            )

            # Retrieve therapist.
            therapist_id = data.get("therapist_id")
            try:
                therapist = TherapistProfile.objects.get(id=therapist_id)
                api_logger.debug(f"Therapist ID:{therapist_id} found")
            except TherapistProfile.DoesNotExist:
                api_logger.warning(f"❌ Invalid therapist ID:{therapist_id}")
                raise ValidationError({"detail": "Invalid therapist."})

            # Appointment details
            date = data.get("date")
            time = data.get("time")
            location = data.get("location")
            api_logger.debug(
                f"Appointment details - Date:{date}, Time:{time}, Location:{location}"
            )

            # Create the appointment
            appointment = Appointment.objects.create(
                date=date,
                time=time,
                customer=customer,
                total_duration=total_duration,
                total_price=total_price,
                therapist=therapist,
                location=location,
                unlimited_package=unlimited_package,
            )
            api_logger.info(
                f"✅ Created appointment ID:{appointment.id} for User:{customer.id}"
            )

            # Record an activity
            Activity.objects.create(
                customer=user,
                activity_type="booking",
                description=f"{user.first_name} booked an unlimited package appointment",
            )
            api_logger.debug(f"Activity recorded for User:{user.id}")

            # Send confirmation email
            try:
                serialized_appointment = AppointmentSerializer(appointment).data
                # send_appointment_confirmation_email(serialized_appointment)
                api_logger.info(
                    f"📤 Sent appointment confirmation email for Appointment:{appointment.id}"
                )
            except Exception as email_error:
                log_error(
                    email_error,
                    f"Sending confirmation email for Appointment:{appointment.id}",
                    log_full_trace=True,
                )

            return appointment

        except Exception as e:
            log_error(e, f"Creating unlimited package appointment for User:{user.id}")
            raise


class CustomerActiveUnlimitedPackageView(generics.RetrieveAPIView):
    """
    Retrieves the active unlimited package for a specific customer.
    """

    serializer_class = UnlimitedPackageSerializer
    permission_classes = [IsAuthenticated, IsOwnerOrReceptionist]

    def get(self, request, customer_id, *args, **kwargs):
        user = request.user
        api_logger.info(
            f"🔍 Admin user:{user.id} checking active unlimited package for Customer:{customer_id}"
        )
        log_request_data(request, "Customer Active Package Request")

        try:
            customer = get_object_or_404(User, id=customer_id, role="customer")
            api_logger.debug(f"Customer:{customer_id} found")

            active_unlimited_package = UnlimitedPackage.objects.filter(
                user=customer, active=True, remaining_time__gt=MIN_ACTIVE_MINUTES
            ).first()

            if active_unlimited_package:
                api_logger.info(
                    f"✅ Found active package ID:{active_unlimited_package.id} for Customer:{customer_id}, Remaining time:{active_unlimited_package.remaining_time}"
                )
                serializer = self.get_serializer(active_unlimited_package)
                response = Response(serializer.data, status=200)
                log_response_data(response, "Customer Active Package Response")
                return response

            api_logger.warning(
                f"❌ No active unlimited package found for Customer:{customer_id}"
            )
            response = Response(
                {"detail": "No active unlimited package found."}, status=404
            )
            log_response_data(response, "Customer Active Package Response")
            return response

        except Exception as e:
            log_error(
                e, f"Retrieving active unlimited package for Customer:{customer_id}"
            )
            raise APIException(f"Internal Server Error: {str(e)}")


class UnlimitedPackageDetailView(generics.RetrieveAPIView, PackageViewMixin):
    """Retrieve details for a specific unlimited package"""

    serializer_class = UnlimitedPackageSerializer
    permission_classes = [IsAuthenticated]

    def get_object(self):
        user = self.request.user
        package_id = self.kwargs.get("pk")
        api_logger.info(
            f"🔍 Retrieving unlimited package ID:{package_id} for User:{user.id}"
        )

        try:
            package = get_object_or_404(UnlimitedPackage, id=package_id)
            api_logger.debug(
                f"Found unlimited package ID:{package_id}, Owner:{package.user.id}"
            )

            # Check access permissions
            if not self.check_package_access(package, self.request, "unlimited_package"):
                api_logger.warning(
                    f"❌ Permission denied for User:{user.id} accessing Package:{package_id}"
                )
                raise PermissionDenied("You don't have permission to view this package.")

            api_logger.info(
                f"✅ Access granted to Package:{package_id} for User:{user.id}"
            )
            return package
        except Exception as e:
            log_error(e, f"Retrieving unlimited package ID:{package_id}")
            raise

    def retrieve(self, request, *args, **kwargs):
        log_request_data(request, "Unlimited Package Detail Request")
        try:
            response = super().retrieve(request, *args, **kwargs)
            log_response_data(response, "Unlimited Package Detail Response")
            return response
        except Exception as e:
            log_error(e, f"Retrieving unlimited package details")
            raise


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def unlimited_package_usage(request, pk):
    """Get usage history for an unlimited package"""
    user = request.user
    api_logger.info(f"🔍 Retrieving usage history for Package:{pk} by User:{user.id}")
    log_request_data(request, "Package Usage Request")

    try:
        package = get_object_or_404(UnlimitedPackage, id=pk)
        api_logger.debug(f"Found unlimited package ID:{pk}, Owner:{package.user.id}")

        # Create an instance of the mixin to use its methods
        mixin = PackageViewMixin()

        # Check access permissions
        if not mixin.check_package_access(package, request, "unlimited_package"):
            api_logger.warning(
                f"❌ Permission denied for User:{user.id} accessing Package:{pk}"
            )
            response = Response(
                {"detail": "You don't have permission to view this package."},
                status=status.HTTP_403_FORBIDDEN,
            )
            log_response_data(response, "Package Usage Response")
            return response

        api_logger.info(f"✅ Access granted to Package:{pk} for User:{user.id}")
        usage_data = mixin.get_package_usage(package, "unlimited_package")
        api_logger.debug(
            f"Retrieved usage data with {len(usage_data.get('appointments', []))} appointments"
        )

        response = Response(usage_data)
        log_response_data(response, "Package Usage Response")
        return response
    except Exception as e:
        log_error(e, f"Retrieving usage for Package:{pk}")
        raise
