from django.core.management.base import BaseCommand
from django.utils import timezone
from api.appointments.models import Sale
import csv
import os
from datetime import datetime

class Command(BaseCommand):
    help = 'Export sales data to CSV based on filters'

    def add_arguments(self, parser):
        parser.add_argument('--start-date', type=str, help='Start date (YYYY-MM-DD)')
        parser.add_argument('--end-date', type=str, help='End date (YYYY-MM-DD)')
        parser.add_argument('--payment-method', type=str, help='Filter by payment method (cash, card, link)')
        parser.add_argument('--location', type=str, help='Filter by location (A, B)')
        parser.add_argument('--sale-type', type=str, help='Filter by sale type (package, service)')
        parser.add_argument('--output-dir', type=str, default='exports', help='Directory to save CSV files')
        parser.add_argument('--april-2025', action='store_true', help='Generate all reports for April 2025')

    def handle(self, *args, **options):
        # Create output directory if it doesn't exist
        output_dir = options.get('output_dir')
        os.makedirs(output_dir, exist_ok=True)
        self.stdout.write(f"Exporting to directory: {output_dir}")

        # Handle the April 2025 use case
        if options.get('april_2025'):
            self.stdout.write("Generating all reports for April 2025...")
            self.generate_april_2025_reports(output_dir)
            return

        # Get filter parameters
        start_date = options.get('start_date')
        end_date = options.get('end_date')
        payment_method = options.get('payment_method')
        location = options.get('location')
        sale_type = options.get('sale_type')

        # Validate date range if provided
        if (start_date and not end_date) or (end_date and not start_date):
            self.stdout.write(self.style.ERROR('Both start-date and end-date must be provided together'))
            return
        
        # Apply filters to queryset - use select_related to optimize queries
        queryset = Sale.objects.select_related(
            'user', 'appointment', 'user_package', 'shared_package',
            'unlimited_package', 'package_option', 'discount'
        ).order_by('-created_at')
        
        filter_description = []
        
        if start_date and end_date:
            queryset = queryset.filter(created_at__date__range=[start_date, end_date])
            filter_description.append(f"date range {start_date} to {end_date}")
            date_label = f"{start_date}_to_{end_date}"
        else:
            date_label = "all_time"

        # Apply additional filters if provided
        if payment_method:
            queryset = queryset.filter(payment_method=payment_method)
            filter_description.append(f"payment method {payment_method}")
            filename = f"{payment_method}_sales_{date_label}.csv"
        elif location:
            queryset = queryset.filter(location=location)
            filter_description.append(f"location {location}")
            filename = f"location_{location}_sales_{date_label}.csv"
        elif sale_type:
            queryset = queryset.filter(sale_type=sale_type)
            filter_description.append(f"sale type {sale_type}")
            filename = f"{sale_type}_sales_{date_label}.csv"
        else:
            filename = f"all_sales_{date_label}.csv"

        # Export to CSV
        count = queryset.count()
        if count == 0:
            self.stdout.write(self.style.WARNING(f'No sales found matching the filters: {", ".join(filter_description)}'))
            return
            
        self.stdout.write(f"Found {count} sales matching the filters: {', '.join(filter_description)}")
        filepath = os.path.join(output_dir, filename)
        self.export_to_csv(queryset, filepath)
        self.stdout.write(self.style.SUCCESS(f'Successfully exported {count} sales to {filepath}'))

    def generate_april_2025_reports(self, output_dir):
        """Generate all required reports for April 2025"""
        start_date = '2025-04-01'
        end_date = '2025-04-30'
        
        self.stdout.write("Creating CSV exports for April 2025...")
        
        # Generate CSV for each payment method
        for method in ['cash', 'card', 'link']:
            queryset = Sale.objects.select_related(
                'user', 'appointment', 'user_package', 'shared_package',
                'unlimited_package', 'package_option', 'discount'
            ).filter(
                created_at__date__range=[start_date, end_date],
                payment_method=method
            ).order_by('-created_at')
            
            count = queryset.count()
            if count == 0:
                self.stdout.write(self.style.WARNING(f'No {method} sales found for April 2025'))
                continue
                
            filename = f"all_{method}_sales_april_2025.csv"
            filepath = os.path.join(output_dir, filename)
            self.export_to_csv(queryset, filepath)
            self.stdout.write(self.style.SUCCESS(f'Exported {count} {method} sales to {filepath}'))
            
        # Generate CSV for each location
        for location in ['A', 'B']:
            loc_name = "Al Warqa Mall" if location == "A" else "Al mizhar branch"
            queryset = Sale.objects.select_related(
                'user', 'appointment', 'user_package', 'shared_package',
                'unlimited_package', 'package_option', 'discount'
            ).filter(
                created_at__date__range=[start_date, end_date],
                location=location
            ).order_by('-created_at')
            
            count = queryset.count()
            if count == 0:
                self.stdout.write(self.style.WARNING(f'No sales found for {loc_name} in April 2025'))
                continue
                
            filename = f"all_location_{location}_sales_april_2025.csv"
            filepath = os.path.join(output_dir, filename)
            self.export_to_csv(queryset, filepath)
            self.stdout.write(self.style.SUCCESS(f'Exported {count} {loc_name} sales to {filepath}'))
            
        # Generate CSV for each sale type
        for sale_type in ['package', 'service']:
            type_name = "Package" if sale_type == "package" else "Service"
            queryset = Sale.objects.select_related(
                'user', 'appointment', 'user_package', 'shared_package',
                'unlimited_package', 'package_option', 'discount'
            ).filter(
                created_at__date__range=[start_date, end_date],
                sale_type=sale_type
            ).order_by('-created_at')
            
            count = queryset.count()
            if count == 0:
                self.stdout.write(self.style.WARNING(f'No {type_name} sales found for April 2025'))
                continue
                
            filename = f"all_{sale_type}_sales_april_2025.csv"
            filepath = os.path.join(output_dir, filename)
            self.export_to_csv(queryset, filepath)
            self.stdout.write(self.style.SUCCESS(f'Exported {count} {type_name} sales to {filepath}'))
            
        # Export all sales for April 2025
        all_queryset = Sale.objects.select_related(
            'user', 'appointment', 'user_package', 'shared_package',
            'unlimited_package', 'package_option', 'discount'
        ).filter(
            created_at__date__range=[start_date, end_date]
        ).order_by('-created_at')
        
        count = all_queryset.count()
        if count == 0:
            self.stdout.write(self.style.WARNING('No sales found for April 2025'))
        else:
            all_filename = f"all_sales_april_2025.csv"
            all_filepath = os.path.join(output_dir, all_filename)
            self.export_to_csv(all_queryset, all_filepath)
            self.stdout.write(self.style.SUCCESS(f'Exported {count} total sales to {all_filepath}'))
        
        self.stdout.write(self.style.SUCCESS('All April 2025 reports generated successfully!'))

    def export_to_csv(self, queryset, filepath):
        """Export queryset to CSV file with comprehensive details"""
        with open(filepath, 'w', newline='') as csvfile:
            writer = csv.writer(csvfile)
            
            # Write comprehensive header row
            writer.writerow([
                'ID',
                'Date',
                'Time',
                'Customer ID',
                'Customer Name',
                'Customer Email',
                'Sale Type',
                'Payment Method',
                'Location',
                'Total Price',
                'Discount Percentage',
                'Discount Name',
                'Package Option Name',
                'Package Option Price',
                'Package Option Time',
                'Invoice Number',
                'Appointment ID',
                'User Package ID',
                'Shared Package ID',
                'Unlimited Package ID'
            ])
            
            # Write data rows
            for sale in queryset:
                # Get customer information
                customer_id = sale.user_id if sale.user_id else "N/A"
                customer_name = f"{sale.user.first_name} {sale.user.last_name}" if sale.user else "N/A"
                customer_email = sale.user.email if sale.user else "N/A"
                
                # Get package information
                package_option_name = str(sale.package_option) if sale.package_option else "N/A"
                package_option_price = sale.package_option.price if sale.package_option else "N/A"
                package_option_time = sale.package_option.time if sale.package_option else "N/A"
                
                # Get discount information
                discount_name = sale.discount.name if sale.discount else "N/A"
                
                # Format location to be more readable
                location_display = "Al Warqa Mall" if sale.location == "A" else "Al mizhar branch"
                
                # Format sale type to be more readable
                sale_type_display = "Package Sale" if sale.sale_type == "package" else "Service Sale"
                
                writer.writerow([
                    sale.id,
                    sale.created_at.date(),
                    sale.created_at.time(),
                    customer_id,
                    customer_name,
                    customer_email,
                    sale_type_display,
                    sale.payment_method,
                    location_display,
                    sale.total_price,
                    sale.discount_percentage,
                    discount_name,
                    package_option_name,
                    package_option_price,
                    package_option_time,
                    sale.invoice_number or "N/A",
                    sale.appointment_id or "N/A",
                    sale.user_package_id or "N/A",
                    sale.shared_package_id or "N/A",
                    sale.unlimited_package_id or "N/A"
                ])