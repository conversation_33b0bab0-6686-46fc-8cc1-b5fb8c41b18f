"""
Tests for Commission Rule API endpoints.
Covers test cases TC-R-001 through TC-R-013 from TestCases.md
"""

import pytest
from decimal import Decimal
from django.urls import reverse
from django.contrib.auth import get_user_model
from rest_framework import status
from rest_framework.test import APIClient

from api.staff.models import TherapistProfile
from api.commissions.models import CommissionProfile, CommissionRule
from api.services.models import Service, ServicePackage

User = get_user_model()


@pytest.fixture
def api_client():
    return APIClient()


@pytest.fixture
def owner_user(db):
    """Create a user with owner role"""
    user = User.objects.create_user(
        email="<EMAIL>",
        phone_number="+971501234567",
        first_name="Owner",
        last_name="User",
        role="owner",
        is_staff=True,
    )
    user.set_password("testpass123")
    user.save()
    return user


@pytest.fixture
def receptionist_user(db):
    """Create a user with receptionist role"""
    user = User.objects.create_user(
        email="<EMAIL>",
        phone_number="+971501234568",
        first_name="Receptionist",
        last_name="User",
        role="receptionist",
        is_staff=True,
    )
    user.set_password("testpass123")
    user.save()
    return user


@pytest.fixture
def therapist_user(db):
    """Create a user with therapist role"""
    user = User.objects.create_user(
        email="<EMAIL>",
        phone_number="+971501234569",
        first_name="Therapist",
        last_name="User",
        role="therapist",
    )
    user.set_password("testpass123")
    user.save()
    return user


@pytest.fixture
def customer_user(db):
    """Create a user with customer role"""
    user = User.objects.create_user(
        email="<EMAIL>",
        phone_number="+971501234570",
        first_name="Customer",
        last_name="User",
        role="customer",
    )
    user.set_password("testpass123")
    user.save()
    return user


@pytest.fixture
def therapist_profile(therapist_user):
    """Create a therapist profile for testing"""
    return TherapistProfile.objects.create(
        user=therapist_user,
        qualifications="Test qualifications",
        start_year=2020,
        location="A"
    )


@pytest.fixture
def commission_profile(therapist_profile):
    """Create a commission profile for testing"""
    return CommissionProfile.objects.create(
        therapist=therapist_profile,
        name="Test Commission Profile",
        base_percentage=Decimal("15.00"),
        sessions_threshold=10,
        is_active=True,
        is_default=True
    )


@pytest.fixture
def service(db):
    """Create a service for testing"""
    return Service.objects.create(
        name="Test Service",
        description="Test service description",
        duration=60,
        price=Decimal("100.00"),
        is_active=True
    )


@pytest.fixture
def package(db):
    """Create a service package for testing"""
    return ServicePackage.objects.create(
        name="Test Package",
        description="Test package description",
        price=Decimal("500.00"),
        sessions_count=5,
        is_active=True
    )


@pytest.fixture
def commission_rule_data(commission_profile):
    """Valid commission rule data for testing"""
    return {
        "profile": commission_profile.id,
        "name": "Test Commission Rule",
        "rule_type": "global",
        "percentage": "20.00",
        "min_sessions": 15,
        "priority": 1,
        "is_active": True
    }


class TestCommissionRuleCreation:
    """Test cases for commission rule creation - TC-R-001"""

    @pytest.mark.django_db
    def test_owner_can_create_commission_rule(self, api_client, owner_user, commission_rule_data):
        """
        TC-R-001: Verify only owner can create commission rules
        Test that owner user can successfully create commission rules
        """
        api_client.force_authenticate(user=owner_user)
        url = reverse('commissionrule-list')

        response = api_client.post(url, commission_rule_data, format='json')

        assert response.status_code == status.HTTP_201_CREATED
        assert CommissionRule.objects.count() == 1

        created_rule = CommissionRule.objects.first()
        assert created_rule.name == commission_rule_data["name"]
        assert created_rule.percentage == Decimal(commission_rule_data["percentage"])
        assert created_rule.profile.id == commission_rule_data["profile"]

    @pytest.mark.django_db
    def test_receptionist_cannot_create_commission_rule(self, api_client, receptionist_user, commission_rule_data):
        """
        TC-R-001: Verify only owner can create commission rules
        Test that receptionist user cannot create commission rules
        """
        api_client.force_authenticate(user=receptionist_user)
        url = reverse('commissionrule-list')

        response = api_client.post(url, commission_rule_data, format='json')

        assert response.status_code == status.HTTP_403_FORBIDDEN
        assert "You do not have permission to create commission rules" in str(response.data)
        assert CommissionRule.objects.count() == 0

    @pytest.mark.django_db
    def test_therapist_cannot_create_commission_rule(self, api_client, therapist_user, commission_rule_data):
        """
        TC-R-001: Verify only owner can create commission rules
        Test that therapist user cannot create commission rules
        """
        api_client.force_authenticate(user=therapist_user)
        url = reverse('commissionrule-list')

        response = api_client.post(url, commission_rule_data, format='json')

        assert response.status_code == status.HTTP_403_FORBIDDEN
        assert "You do not have permission to create commission rules" in str(response.data)
        assert CommissionRule.objects.count() == 0

    @pytest.mark.django_db
    def test_customer_cannot_create_commission_rule(self, api_client, customer_user, commission_rule_data):
        """
        TC-R-001: Verify only owner can create commission rules
        Test that customer user cannot create commission rules
        """
        api_client.force_authenticate(user=customer_user)
        url = reverse('commissionrule-list')

        response = api_client.post(url, commission_rule_data, format='json')

        assert response.status_code == status.HTTP_403_FORBIDDEN
        assert "You do not have permission to create commission rules" in str(response.data)
        assert CommissionRule.objects.count() == 0

    @pytest.mark.django_db
    def test_unauthenticated_user_cannot_create_commission_rule(self, api_client, commission_rule_data):
        """
        TC-R-001: Verify only owner can create commission rules
        Test that unauthenticated user cannot create commission rules
        """
        url = reverse('commissionrule-list')

        response = api_client.post(url, commission_rule_data, format='json')

        assert response.status_code == status.HTTP_401_UNAUTHORIZED
        assert CommissionRule.objects.count() == 0


class TestCommissionRuleUpdate:
    """Test cases for commission rule updates - TC-R-002"""

    @pytest.fixture
    def commission_rule(self, commission_profile):
        """Create a commission rule for testing updates"""
        return CommissionRule.objects.create(
            profile=commission_profile,
            name="Original Rule",
            rule_type="global",
            percentage=Decimal("10.00"),
            min_sessions=15,
            priority=1,
            is_active=True
        )

    @pytest.fixture
    def update_data(self):
        """Data for updating commission rule"""
        return {
            "name": "Updated Rule Name",
            "percentage": "25.00",
            "min_sessions": 20,
            "priority": 2,
            "is_active": False
        }

    @pytest.mark.django_db
    def test_owner_can_update_commission_rule(self, api_client, owner_user, commission_rule, update_data):
        """
        TC-R-002: Verify only owner can update commission rules
        Test that owner user can successfully update commission rules
        """
        api_client.force_authenticate(user=owner_user)
        url = reverse('commissionrule-detail', kwargs={'pk': commission_rule.pk})

        response = api_client.patch(url, update_data, format='json')

        assert response.status_code == status.HTTP_200_OK

        # Refresh from database
        commission_rule.refresh_from_db()
        assert commission_rule.name == update_data["name"]
        assert commission_rule.percentage == Decimal(update_data["percentage"])
        assert commission_rule.min_sessions == update_data["min_sessions"]
        assert commission_rule.priority == update_data["priority"]
        assert commission_rule.is_active == update_data["is_active"]

    @pytest.mark.django_db
    def test_receptionist_cannot_update_commission_rule(self, api_client, receptionist_user, commission_rule, update_data):
        """
        TC-R-002: Verify only owner can update commission rules
        Test that receptionist user cannot update commission rules (matching Commission Profile pattern)
        """
        api_client.force_authenticate(user=receptionist_user)
        url = reverse('commissionrule-detail', kwargs={'pk': commission_rule.pk})

        original_name = commission_rule.name
        response = api_client.patch(url, update_data, format='json')

        assert response.status_code == status.HTTP_404_NOT_FOUND  # Matching Commission Profile pattern

        # Verify no changes were made
        commission_rule.refresh_from_db()
        assert commission_rule.name == original_name

    @pytest.mark.django_db
    def test_therapist_cannot_update_commission_rule(self, api_client, therapist_user, commission_rule, update_data):
        """
        TC-R-002: Verify only owner can update commission rules
        Test that therapist user cannot update commission rules
        """
        api_client.force_authenticate(user=therapist_user)
        url = reverse('commissionrule-detail', kwargs={'pk': commission_rule.pk})

        original_name = commission_rule.name
        response = api_client.patch(url, update_data, format='json')

        assert response.status_code == status.HTTP_403_FORBIDDEN

        # Verify no changes were made
        commission_rule.refresh_from_db()
        assert commission_rule.name == original_name

    @pytest.mark.django_db
    def test_customer_cannot_update_commission_rule(self, api_client, customer_user, commission_rule, update_data):
        """
        TC-R-002: Verify only owner can update commission rules
        Test that customer user cannot update commission rules
        """
        api_client.force_authenticate(user=customer_user)
        url = reverse('commissionrule-detail', kwargs={'pk': commission_rule.pk})

        original_name = commission_rule.name
        response = api_client.patch(url, update_data, format='json')

        assert response.status_code == status.HTTP_404_NOT_FOUND

        # Verify no changes were made
        commission_rule.refresh_from_db()
        assert commission_rule.name == original_name

    @pytest.mark.django_db
    def test_unauthenticated_user_cannot_update_commission_rule(self, api_client, commission_rule, update_data):
        """
        TC-R-002: Verify only owner can update commission rules
        Test that unauthenticated user cannot update commission rules
        """
        url = reverse('commissionrule-detail', kwargs={'pk': commission_rule.pk})

        original_name = commission_rule.name
        response = api_client.patch(url, update_data, format='json')

        assert response.status_code == status.HTTP_401_UNAUTHORIZED

        # Verify no changes were made
        commission_rule.refresh_from_db()
        assert commission_rule.name == original_name

    @pytest.mark.django_db
    def test_owner_can_full_update_commission_rule(self, api_client, owner_user, commission_rule, commission_profile):
        """
        TC-R-002: Verify only owner can update commission rules
        Test that owner user can perform full updates (PUT) on commission rules
        """
        api_client.force_authenticate(user=owner_user)
        url = reverse('commissionrule-detail', kwargs={'pk': commission_rule.pk})

        full_update_data = {
            "profile": commission_profile.id,
            "name": "Completely New Rule",
            "rule_type": "global",  # Changed to global to avoid service validation
            "percentage": "30.00",
            "min_sessions": 25,
            "priority": 3,
            "is_active": True
        }

        response = api_client.put(url, full_update_data, format='json')

        assert response.status_code == status.HTTP_200_OK

        # Refresh from database
        commission_rule.refresh_from_db()
        assert commission_rule.name == full_update_data["name"]
        assert commission_rule.rule_type == full_update_data["rule_type"]
        assert commission_rule.percentage == Decimal(full_update_data["percentage"])
        assert commission_rule.min_sessions == full_update_data["min_sessions"]
        assert commission_rule.priority == full_update_data["priority"]
        assert commission_rule.is_active == full_update_data["is_active"]


class TestCommissionRuleDelete:
    """Test cases for commission rule deletion - TC-R-003"""

    @pytest.fixture
    def commission_rule(self, commission_profile):
        """Create a commission rule for testing deletion"""
        return CommissionRule.objects.create(
            profile=commission_profile,
            name="Rule to Delete",
            rule_type="global",
            percentage=Decimal("15.00"),
            min_sessions=15,
            priority=1,
            is_active=True
        )

    @pytest.mark.django_db
    def test_owner_can_delete_commission_rule(self, api_client, owner_user, commission_rule):
        """
        TC-R-003: Verify only owner can delete commission rules
        Test that owner user can successfully delete commission rules
        """
        api_client.force_authenticate(user=owner_user)
        url = reverse('commissionrule-detail', kwargs={'pk': commission_rule.pk})

        # Verify rule exists before deletion
        assert CommissionRule.objects.filter(pk=commission_rule.pk).exists()

        response = api_client.delete(url)

        assert response.status_code == status.HTTP_204_NO_CONTENT

        # Verify rule was deleted
        assert not CommissionRule.objects.filter(pk=commission_rule.pk).exists()

    @pytest.mark.django_db
    def test_receptionist_cannot_delete_commission_rule(self, api_client, receptionist_user, commission_rule):
        """
        TC-R-003: Verify only owner can delete commission rules
        Test that receptionist user cannot delete commission rules (matching Commission Profile pattern)
        """
        api_client.force_authenticate(user=receptionist_user)
        url = reverse('commissionrule-detail', kwargs={'pk': commission_rule.pk})

        response = api_client.delete(url)

        assert response.status_code == status.HTTP_404_NOT_FOUND  # Matching Commission Profile pattern

        # Verify rule still exists
        assert CommissionRule.objects.filter(pk=commission_rule.pk).exists()

    @pytest.mark.django_db
    def test_therapist_cannot_delete_commission_rule(self, api_client, therapist_user, commission_rule):
        """
        TC-R-003: Verify only owner can delete commission rules
        Test that therapist user cannot delete commission rules
        """
        api_client.force_authenticate(user=therapist_user)
        url = reverse('commissionrule-detail', kwargs={'pk': commission_rule.pk})

        response = api_client.delete(url)

        assert response.status_code == status.HTTP_403_FORBIDDEN

        # Verify rule still exists
        assert CommissionRule.objects.filter(pk=commission_rule.pk).exists()

    @pytest.mark.django_db
    def test_customer_cannot_delete_commission_rule(self, api_client, customer_user, commission_rule):
        """
        TC-R-003: Verify only owner can delete commission rules
        Test that customer user cannot delete commission rules
        """
        api_client.force_authenticate(user=customer_user)
        url = reverse('commissionrule-detail', kwargs={'pk': commission_rule.pk})

        response = api_client.delete(url)

        assert response.status_code == status.HTTP_404_NOT_FOUND

        # Verify rule still exists
        assert CommissionRule.objects.filter(pk=commission_rule.pk).exists()

    @pytest.mark.django_db
    def test_unauthenticated_user_cannot_delete_commission_rule(self, api_client, commission_rule):
        """
        TC-R-003: Verify only owner can delete commission rules
        Test that unauthenticated user cannot delete commission rules
        """
        url = reverse('commissionrule-detail', kwargs={'pk': commission_rule.pk})

        response = api_client.delete(url)

        assert response.status_code == status.HTTP_401_UNAUTHORIZED

        # Verify rule still exists
        assert CommissionRule.objects.filter(pk=commission_rule.pk).exists()


class TestCommissionRuleList:
    """Test cases for commission rule listing - TC-R-004"""

    @pytest.fixture
    def multiple_commission_rules(self, commission_profile):
        """Create multiple commission rules for testing list access"""
        rules = []
        for i in range(3):
            rule = CommissionRule.objects.create(
                profile=commission_profile,
                name=f"Test Rule {i+1}",
                rule_type="global",
                percentage=Decimal(f"{10 + i*5}.00"),
                min_sessions=15 + i*5,
                priority=i+1,
                is_active=True
            )
            rules.append(rule)
        return rules

    @pytest.mark.django_db
    def test_owner_can_list_all_commission_rules(self, api_client, owner_user, multiple_commission_rules):
        """
        TC-R-004: Verify only owner can list all commission rules
        Test that owner user can successfully list all commission rules
        """
        api_client.force_authenticate(user=owner_user)
        url = reverse('commissionrule-list')

        response = api_client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert len(response.data) == 3  # Should see all 3 rules

        # Verify rule names are in the response
        rule_names = [rule['name'] for rule in response.data]
        assert "Test Rule 1" in rule_names
        assert "Test Rule 2" in rule_names
        assert "Test Rule 3" in rule_names

    @pytest.mark.django_db
    def test_receptionist_cannot_list_commission_rules(self, api_client, receptionist_user, multiple_commission_rules):
        """
        TC-R-004: Verify only owner can list all commission rules
        Test that receptionist user cannot list commission rules (matching Commission Profile pattern)
        """
        api_client.force_authenticate(user=receptionist_user)
        url = reverse('commissionrule-list')

        response = api_client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert len(response.data) == 0  # Should see no rules (matching Commission Profile pattern)

    @pytest.mark.django_db
    def test_therapist_can_only_see_own_commission_rules(self, api_client, therapist_user, therapist_profile, multiple_commission_rules):
        """
        TC-R-004: Verify only owner can list all commission rules
        Test that therapist user can only see rules for their own profiles
        """
        api_client.force_authenticate(user=therapist_user)
        url = reverse('commissionrule-list')

        response = api_client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert len(response.data) == 3  # Should see their own rules

        # Verify all rules belong to the therapist's profile
        for rule_data in response.data:
            rule = CommissionRule.objects.get(id=rule_data['id'])
            assert rule.profile.therapist == therapist_profile

    @pytest.mark.django_db
    def test_customer_cannot_list_commission_rules(self, api_client, customer_user, multiple_commission_rules):
        """
        TC-R-004: Verify only owner can list all commission rules
        Test that customer user cannot list commission rules
        """
        api_client.force_authenticate(user=customer_user)
        url = reverse('commissionrule-list')

        response = api_client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert len(response.data) == 0  # Should see no rules

    @pytest.mark.django_db
    def test_unauthenticated_user_cannot_list_commission_rules(self, api_client, multiple_commission_rules):
        """
        TC-R-004: Verify only owner can list all commission rules
        Test that unauthenticated user cannot list commission rules
        """
        url = reverse('commissionrule-list')

        response = api_client.get(url)

        assert response.status_code == status.HTTP_401_UNAUTHORIZED

    @pytest.mark.django_db
    def test_owner_can_retrieve_specific_commission_rule(self, api_client, owner_user, multiple_commission_rules):
        """
        TC-R-004: Verify only owner can list all commission rules
        Test that owner user can retrieve specific commission rules
        """
        api_client.force_authenticate(user=owner_user)
        rule = multiple_commission_rules[0]
        url = reverse('commissionrule-detail', kwargs={'pk': rule.pk})

        response = api_client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert response.data['name'] == rule.name
        assert response.data['id'] == rule.id

    @pytest.mark.django_db
    def test_receptionist_cannot_retrieve_specific_commission_rule(self, api_client, receptionist_user, multiple_commission_rules):
        """
        TC-R-004: Verify only owner can list all commission rules
        Test that receptionist user cannot retrieve specific commission rules (matching Commission Profile pattern)
        """
        api_client.force_authenticate(user=receptionist_user)
        rule = multiple_commission_rules[0]
        url = reverse('commissionrule-detail', kwargs={'pk': rule.pk})

        response = api_client.get(url)

        assert response.status_code == status.HTTP_404_NOT_FOUND  # Should not be able to access (matching Commission Profile pattern)

    @pytest.mark.django_db
    def test_owner_sees_only_active_rules_by_default(self, api_client, owner_user, commission_profile):
        """
        TC-R-004: Test that owner sees only active rules by default (matching Commission Profile pattern)
        """
        # Create active and inactive rules
        active_rule = CommissionRule.objects.create(
            profile=commission_profile,
            name="Active Rule",
            rule_type="global",
            percentage=Decimal("20.00"),
            min_sessions=15,
            priority=1,
            is_active=True
        )

        inactive_rule = CommissionRule.objects.create(
            profile=commission_profile,
            name="Inactive Rule",
            rule_type="global",
            percentage=Decimal("15.00"),
            min_sessions=15,
            priority=1,
            is_active=False
        )

        api_client.force_authenticate(user=owner_user)
        url = reverse('commissionrule-list')

        response = api_client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert len(response.data) == 1  # Should see only active rule
        assert response.data[0]['name'] == "Active Rule"

    @pytest.mark.django_db
    def test_owner_can_see_all_rules_with_include_inactive_parameter(self, api_client, owner_user, commission_profile):
        """
        TC-R-004: Test that owner can see all rules with include_inactive parameter (matching Commission Profile pattern)
        """
        # Create active and inactive rules
        active_rule = CommissionRule.objects.create(
            profile=commission_profile,
            name="Active Rule",
            rule_type="global",
            percentage=Decimal("20.00"),
            min_sessions=15,
            priority=1,
            is_active=True
        )

        inactive_rule = CommissionRule.objects.create(
            profile=commission_profile,
            name="Inactive Rule",
            rule_type="global",
            percentage=Decimal("15.00"),
            min_sessions=15,
            priority=1,
            is_active=False
        )

        api_client.force_authenticate(user=owner_user)
        url = reverse('commissionrule-list')

        response = api_client.get(url, {'include_inactive': 'true'})

        assert response.status_code == status.HTTP_200_OK
        assert len(response.data) == 2  # Should see both active and inactive rules
        rule_names = [rule['name'] for rule in response.data]
        assert "Active Rule" in rule_names
        assert "Inactive Rule" in rule_names

    @pytest.mark.django_db
    def test_therapist_sees_only_own_active_rules_by_default(self, api_client, therapist_user, therapist_profile, commission_profile):
        """
        TC-R-004: Test that therapist sees only own active rules by default (matching Commission Profile pattern)
        """
        # Create active and inactive rules for the therapist
        active_rule = CommissionRule.objects.create(
            profile=commission_profile,
            name="Active Rule",
            rule_type="global",
            percentage=Decimal("20.00"),
            min_sessions=15,
            priority=1,
            is_active=True
        )

        inactive_rule = CommissionRule.objects.create(
            profile=commission_profile,
            name="Inactive Rule",
            rule_type="global",
            percentage=Decimal("15.00"),
            min_sessions=15,
            priority=1,
            is_active=False
        )

        api_client.force_authenticate(user=therapist_user)
        url = reverse('commissionrule-list')

        response = api_client.get(url)

        assert response.status_code == status.HTTP_200_OK
        assert len(response.data) == 1  # Should see only active rule
        assert response.data[0]['name'] == "Active Rule"

    @pytest.mark.django_db
    def test_therapist_can_see_own_inactive_rules_with_include_inactive_parameter(self, api_client, therapist_user, therapist_profile, commission_profile):
        """
        TC-R-004: Test that therapist can see own inactive rules with include_inactive parameter (matching Commission Profile pattern)
        """
        # Create active and inactive rules for the therapist
        active_rule = CommissionRule.objects.create(
            profile=commission_profile,
            name="Active Rule",
            rule_type="global",
            percentage=Decimal("20.00"),
            min_sessions=15,
            priority=1,
            is_active=True
        )

        inactive_rule = CommissionRule.objects.create(
            profile=commission_profile,
            name="Inactive Rule",
            rule_type="global",
            percentage=Decimal("15.00"),
            min_sessions=15,
            priority=1,
            is_active=False
        )

        api_client.force_authenticate(user=therapist_user)
        url = reverse('commissionrule-list')

        response = api_client.get(url, {'include_inactive': 'true'})

        assert response.status_code == status.HTTP_200_OK
        assert len(response.data) == 2  # Should see both active and inactive rules
        rule_names = [rule['name'] for rule in response.data]
        assert "Active Rule" in rule_names
        assert "Inactive Rule" in rule_names

    @pytest.mark.django_db
    def test_rule_becomes_invisible_after_deactivation(self, api_client, owner_user, commission_profile):
        """
        TC-R-004: Test that rule becomes invisible after deactivation (matching Commission Profile pattern)
        """
        # Create active rule
        rule = CommissionRule.objects.create(
            profile=commission_profile,
            name="Test Rule",
            rule_type="global",
            percentage=Decimal("20.00"),
            min_sessions=15,
            priority=1,
            is_active=True
        )

        api_client.force_authenticate(user=owner_user)
        list_url = reverse('commissionrule-list')

        # Initially should see the rule
        response = api_client.get(list_url)
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data) == 1

        # Deactivate the rule
        detail_url = reverse('commissionrule-detail', kwargs={'pk': rule.pk})
        api_client.patch(detail_url, {'is_active': False}, format='json')

        # Now should not see the rule by default
        response = api_client.get(list_url)
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data) == 0

    @pytest.mark.django_db
    def test_rule_becomes_visible_after_activation(self, api_client, owner_user, commission_profile):
        """
        TC-R-004: Test that rule becomes visible after activation (matching Commission Profile pattern)
        """
        # Create inactive rule
        rule = CommissionRule.objects.create(
            profile=commission_profile,
            name="Test Rule",
            rule_type="global",
            percentage=Decimal("20.00"),
            min_sessions=15,
            priority=1,
            is_active=False
        )

        api_client.force_authenticate(user=owner_user)
        list_url = reverse('commissionrule-list')

        # Initially should not see the rule
        response = api_client.get(list_url)
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data) == 0

        # Activate the rule
        detail_url = reverse('commissionrule-detail', kwargs={'pk': rule.pk})
        api_client.patch(detail_url, {'is_active': True}, format='json')

        # Now should see the rule
        response = api_client.get(list_url)
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data) == 1
        assert response.data[0]['name'] == "Test Rule"
