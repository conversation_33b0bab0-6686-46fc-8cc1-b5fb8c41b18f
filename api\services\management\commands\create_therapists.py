from django.core.management.base import BaseCommand
from django.db import transaction
from api.staff.models import TherapistProfile, Service, ServicePackage
from django.contrib.auth import get_user_model

User = get_user_model()


class Command(BaseCommand):
    help = "Assign therapists and receptionists to the system with locations"

    def handle(self, *args, **kwargs):
        # Fetch services and packages
        services = {service.name: service for service in Service.objects.all()}
        packages = {package.name: package for package in ServicePackage.objects.all()}

        for therapist in TherapistProfile.objects.all():
            therapist.user.delete()  # This will also delete the related TherapistProfile due to CASCADE
        User.objects.filter(role="therapist").delete()
        receptionist_data = [
            # {
            #     "name": "Fay<PERSON>z Zahra",
            #     "role": "Receptionist",
            #     "services": [],
            #     "packages": [],
            #     "location": "A",
            #     "hours": [
            #         {"day": 0, "start_time": "12:00", "end_time": "22:00"},
            #         {"day": 1, "start_time": "12:00", "end_time": "22:00"},
            #         {"day": 2, "start_time": "12:00", "end_time": "22:00"},
            #         {"day": 3, "start_time": "12:00", "end_time": "22:00"},
            #         {"day": 5, "start_time": "12:00", "end_time": "22:00"},
            #         {"day": 6, "start_time": "12:00", "end_time": "22:00"},
            #     ],
            # },
            # {
            #     "name": "Hajra Atarya",
            #     "role": "Receptionist",
            #     "services": [],
            #     "packages": [],
            #     "location": "B",
            #     "hours": [
            #         {"day": 0, "start_time": "13:00", "end_time": "21:00"},
            #         {"day": 1, "start_time": "13:00", "end_time": "21:00"},
            #         {"day": 2, "start_time": "13:00", "end_time": "21:00"},
            #         {"day": 3, "start_time": "13:00", "end_time": "21:00"},
            #         {"day": 4, "start_time": "14:00", "end_time": "22:00"},
            #         {"day": 6, "start_time": "13:00", "end_time": "21:00"},
            #     ],
            # },
        ]

        therapist_data = [
            {
                "name": "Omar Alnajjar",
                "role": "Therapist",
                "services": ["Stretching", "Physio"],
                "packages": [],
                "location": "A",
                "hours": [
                    {"day": 0, "start_time": "10:00", "end_time": "22:00"},
                    {"day": 1, "start_time": "10:00", "end_time": "22:00"},
                    {"day": 2, "start_time": "10:00", "end_time": "22:00"},
                    {"day": 3, "start_time": "10:00", "end_time": "22:00"},
                    {"day": 4, "start_time": "10:00", "end_time": "22:00"},
                    {"day": 5, "start_time": "10:00", "end_time": "22:00"},
                    {"day": 6, "start_time": "10:00", "end_time": "22:00"},
                ],
            },
            {
                "name": "Farnaz Alidadi",
                "role": "Therapist",
                "services": ["Stretching", "Physio"],
                "packages": [],
                "location": "A",
                "hours": [
                    {"day": 0, "start_time": "10:00", "end_time": "18:00"},
                    {"day": 1, "start_time": "10:00", "end_time": "18:00"},
                    {"day": 2, "start_time": "10:00", "end_time": "18:00"},
                    {"day": 3, "start_time": "10:00", "end_time": "18:00"},
                    {"day": 5, "start_time": "10:00", "end_time": "18:00"},
                    {"day": 6, "start_time": "10:00", "end_time": "18:00"},
                ],
            },
            {
                "name": "Usman Zaman",
                "role": "Therapist",
                "services": ["Stretching", "Massage", "Physio"],
                "packages": [
                    "Light Recovery Package",
                    "Medium Recovery Package",
                    "Extreme Recovery Package",
                ],
                "location": "A",
                "hours": [
                    {"day": 0, "start_time": "14:00", "end_time": "22:00"},
                    {"day": 1, "start_time": "14:00", "end_time": "22:00"},
                    {"day": 3, "start_time": "14:00", "end_time": "22:00"},
                    {"day": 4, "start_time": "14:00", "end_time": "22:00"},
                    {"day": 5, "start_time": "14:00", "end_time": "22:00"},
                    {"day": 6, "start_time": "14:00", "end_time": "22:00"},
                ],
            },
            {
                "name": "Maryam Bautista",
                "role": "Therapist",
                "services": ["Massage"],
                "packages": ["Light Recovery Package"],
                "location": "A",
                "hours": [
                    {"day": 0, "start_time": "12:00", "end_time": "20:00"},
                    {"day": 2, "start_time": "12:00", "end_time": "22:00"},
                    {"day": 3, "start_time": "12:00", "end_time": "22:00"},
                    {"day": 4, "start_time": "12:00", "end_time": "22:00"},
                    {"day": 5, "start_time": "12:00", "end_time": "20:00"},
                    {"day": 6, "start_time": "12:00", "end_time": "20:00"},
                ],
            },
            {
                "name": "Fatimah Abdullah",
                "role": "Therapist",
                "services": ["Stretching", "Physio"],
                "packages": [],
                "location": "B",
                "hours": [
                    {"day": 1, "start_time": "13:00", "end_time": "21:00"},
                    {"day": 2, "start_time": "13:00", "end_time": "21:00"},
                    {"day": 3, "start_time": "13:00", "end_time": "21:00"},
                    {"day": 4, "start_time": "14:00", "end_time": "22:00"},
                    {"day": 5, "start_time": "13:00", "end_time": "21:00"},
                    {"day": 6, "start_time": "13:00", "end_time": "21:00"},
                ],
            },
            {
                "name": "Khatera Fatihi",
                "role": "Therapist",
                "services": ["Stretching", "Physio"],
                "packages": [],
                "location": "B",
                "hours": [
                    {"day": 0, "start_time": "13:00", "end_time": "21:00"},
                    {"day": 1, "start_time": "13:00", "end_time": "21:00"},
                    {"day": 2, "start_time": "13:00", "end_time": "21:00"},
                    {"day": 3, "start_time": "13:00", "end_time": "21:00"},
                    {"day": 4, "start_time": "14:00", "end_time": "22:00"},
                    {"day": 5, "start_time": "13:00", "end_time": "21:00"},
                ],
            },
            {
                "name": "Katherine Joy Salgarino",
                "role": "Therapist",
                "services": ["Massage"],
                "packages": ["Light Recovery Package"],
                "location": "B",
                "hours": [
                    {"day": 0, "start_time": "13:00", "end_time": "21:00"},
                    {"day": 2, "start_time": "13:00", "end_time": "21:00"},
                    {"day": 3, "start_time": "13:00", "end_time": "21:00"},
                    {"day": 4, "start_time": "12:00", "end_time": "22:00"},
                    {"day": 5, "start_time": "13:00", "end_time": "21:00"},
                    {"day": 6, "start_time": "13:00", "end_time": "21:00"},
                ],
            },
        ]

        try:
            with transaction.atomic():
                # Step 1: Create receptionists
                for receptionist in receptionist_data:
                    first_name, last_name = receptionist["name"].split(" ", 1)
                    user, _ = User.objects.get_or_create(
                        email=f"{first_name.lower()}@stretchup.ae",
                        defaults={
                            "first_name": first_name,
                            "last_name": last_name,
                            "role": "receptionist",
                            "phone_number": f"+971555{first_name[:3].lower()}",
                        },
                    )
                    self.stdout.write(
                        self.style.SUCCESS(
                            f"Created receptionist: {user.get_full_name()} at location {receptionist['location']}"
                        )
                    )


                # Step 2: Create therapists and assign services/packages
                for therapist in therapist_data:
                    first_name, last_name = therapist["name"].split(" ", 1)
                    user, _ = User.objects.get_or_create(
                        email=f"{first_name.lower()}@stretchup.ae",
                        defaults={
                            "first_name": first_name,
                            "last_name": last_name,
                            "role": "therapist",
                            "phone_number": f"+971555{first_name[:3].lower()}",
                        },
                    )
                    profile, _ = TherapistProfile.objects.get_or_create(user=user)
                    profile.location = therapist["location"]

                    # Assign services
                    if therapist["services"]:
                        profile.services.set(
                            [services[service] for service in therapist["services"]]
                        )

                    # Assign packages
                    if therapist["packages"]:
                        profile.eligible_packages.set(
                            [packages[package] for package in therapist["packages"]]
                        )

                    profile.save()

                    # TC-P-012: Create default commission profile for new therapist
                    from api.commissions.models import CommissionProfile
                    default_profile, created = CommissionProfile.objects.get_or_create(
                        therapist=profile,
                        is_default=True,
                        defaults={
                            'name': f"Default Profile - {user.get_full_name()}",
                            'base_percentage': 15.00,
                            'sessions_threshold': 15,
                            'is_active': True,
                        }
                    )

                    if created:
                        self.stdout.write(
                            self.style.SUCCESS(
                                f"Created default commission profile for: {user.get_full_name()}"
                            )
                        )

                    self.stdout.write(
                        self.style.SUCCESS(
                            f"Created therapist: {user.get_full_name()} at location {profile.location}"
                        )
                    )
                    if "hours" in therapist.keys():
                        for hour_data in therapist["hours"]:
                            profile.working_hours.get_or_create(
                                day=hour_data["day"],
                                defaults={
                                    "start_time": hour_data["start_time"],
                                    "end_time": hour_data["end_time"],
                                },
                            )
        except Exception as e:
            self.stderr.write(self.style.ERROR(f"Error occurred: {e}"))
            raise

        self.stdout.write(self.style.SUCCESS("Staff assignment completed successfully!"))
