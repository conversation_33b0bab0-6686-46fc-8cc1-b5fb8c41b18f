version: "3.8"
services:
  db:
    image: postgres:latest
    volumes:
      - postgres_data:/var/lib/postgresql/data
    environment:
      POSTGRES_HOST_AUTH_METHOD: trust # Allows connections without a password (use only for local development)
      POSTGRES_DB: postgres
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    networks:
      - api_network
    env_file:
      - .env

  # db_test:
  #   image: postgres:latest
  #   environment:
  #     POSTGRES_DB: postgres_test
  #     POSTGRES_USER: postgres
  #     POSTGRES_PASSWORD: password
  #   ports:
  #     - "5432"

  # redis:
  #   image: redis:alpine
  #   ports:
  #     - "6379:6379"
  #   networks:
  #     - api_network

  # celery:
  #   build: .
  #   command: celery -A config worker -l info
  #   volumes:
  #     - .:/code
  #   depends_on:
  #     - web
  #     - redis
  #   networks:
  #     - api_network

  # celerybeat:
  #   build: .
  #   command: celery -A config beat -l info --scheduler django_celery_beat.schedulers:DatabaseScheduler
  #   volumes:
  #     - .:/code
  #   depends_on:
  #     - web
  #     - redis

  # networks:
  #   - api_network

  web:
    build: .
    command: sh -c "pip install -r requirements.txt && python manage.py migrate && python manage.py runserver 0.0.0.0:8000"
    volumes:
      - .:/app
    ports:
      - "8000:8000"
    depends_on:
      - db
    environment:
      - DEBUG=True
    networks:
      - api_network

  test:
    build: .
    command: bash
    volumes:
      - .:/app
    depends_on:
      - db
    environment:
      - DEBUG=True
    networks:
      - api_network
    stdin_open: true
    tty: true

volumes:
  postgres_data:

networks:
  api_network:
    driver: bridge
