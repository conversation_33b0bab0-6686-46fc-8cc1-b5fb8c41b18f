# Commission Recalculation Script

This script allows you to recalculate commissions for sales within a specified date range.

## Usage

### Recommended Usage (Auto Mode)

```bash
# 🤖 AUTO MODE - Handles everything intelligently (RECOMMENDED)
python manage.py recalculate_commissions --start-date 2024-01-01 --end-date 2024-01-31 --auto

# Auto mode with dry run to preview changes
python manage.py recalculate_commissions --start-date 2024-01-01 --end-date 2024-01-31 --auto --dry-run --verbose
```

### Advanced Usage

```bash
# Calculate missing commissions only (for sales without any commissions)
python manage.py recalculate_commissions --start-date 2024-01-01 --end-date 2024-01-31 --missing-only

# Force recalculate all commissions (when rules have been updated)
python manage.py recalculate_commissions --start-date 2024-01-01 --end-date 2024-01-31 --force-recalculate

# Standard recalculation (legacy mode)
python manage.py recalculate_commissions --start-date 2024-01-01 --end-date 2024-01-31
```

### Docker Usage

```bash
# Run inside Docker container
docker exec -it stretchup-api-web-1 python manage.py recalculate_commissions --start-date 2024-01-01 --end-date 2024-01-31 --auto

# Or using docker-compose
docker-compose exec web python manage.py recalculate_commissions --start-date 2024-01-01 --end-date 2024-01-31 --auto
```

### 🚀 **ONE COMMAND SOLUTION (Recommended)**

Use the helper script for the easiest experience:

```bash
# Simple wrapper script (recommended)
./recalculate_commissions.sh 2025-05-01 2025-05-31

# Preview changes first
./recalculate_commissions.sh 2025-05-01 2025-05-31 --dry-run

# Only missing commissions
./recalculate_commissions.sh 2025-05-01 2025-05-31 --missing-only

# Force recalculate all
./recalculate_commissions.sh 2025-05-01 2025-05-31 --force

# Help and examples
./recalculate_commissions.sh --help
```

## What the Script Does

1. **Validates Input**: Checks date format and ensures start date is before end date
2. **Finds Sales**: Queries all appointment/package sales and product sales in the date range
3. **Deletes Existing Commissions**: Removes all commission records for the found sales
4. **Recalculates Commissions**: Uses the existing `CommissionCalculator` service to recalculate commissions
5. **Updates Statistics**: Refreshes therapist monthly and yearly statistics
6. **Cleanup**: Removes empty monthly stat records

## Parameters

- `--start-date`: Start date in YYYY-MM-DD format (required)
- `--end-date`: End date in YYYY-MM-DD format (required)
- `--auto`: 🤖 **Auto mode** - Intelligently handles both missing and outdated commissions (recommended)
- `--missing-only`: Only calculate commissions for sales that have no existing commissions
- `--force-recalculate`: Force recalculation even for sales that already have commissions
- `--dry-run`: Preview mode - shows what would be done without making changes
- `--verbose`: Show detailed progress information

### Mode Selection

Choose **ONE** of these modes:
- `--auto` (recommended): Automatically detects and handles both missing commissions and outdated commissions
- `--missing-only`: Only processes sales without any commissions
- `--force-recalculate`: Recalculates all commissions regardless of existing state
- No mode flag: Standard recalculation (legacy behavior)

## Safety Features

- **Dry Run Mode**: Test the script without making any changes
- **Transaction Safety**: All operations are wrapped in a database transaction
- **Confirmation Prompt**: Asks for confirmation before proceeding with live changes
- **Detailed Logging**: Provides comprehensive feedback on what's being processed

## Example Output

```
Commission Recalculation Script
Date Range: 2024-01-01 to 2024-01-31
Mode: LIVE
==================================================
Found 150 appointment/package sales
Found 25 product sales
Total sales to process: 175

This will recalculate commissions for 175 sales. Continue? (y/N): y

1. Deleting existing commission records...
   Found 89 existing commission records to delete
   Deleted 89 commission records

2. Recalculating commissions for appointment/package sales...
   Processed 150 appointment/package sales
   Created 89 commission records

3. Handling product sales...
   Found 25 product sales
   Note: Product sales commission logic not implemented yet

4. Updating therapist statistics...
   Updated statistics for 12 therapists
   Removed 3 empty monthly stat records

--- COMMISSION RECALCULATION COMPLETED ---
```

## Important Notes

- **Backup First**: Always backup your database before running this script on production data
- **Test with Dry Run**: Use `--dry-run` first to preview changes
- **Date Range**: The script processes sales based on their `created_at` date
- **Product Sales**: Currently only handles appointment/package sales; product sales commission logic is not implemented
- **Performance**: For large date ranges, the script may take some time to complete

## Troubleshooting

- **No sales found**: Check that the date range contains actual sales
- **Permission errors**: Ensure you have proper database permissions
- **Memory issues**: For very large date ranges, consider processing smaller chunks
