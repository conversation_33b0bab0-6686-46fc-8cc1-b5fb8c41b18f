import csv
import os
from datetime import datetime

from django.conf import settings
from django.core.management.base import BaseCommand
from django.db import transaction
from django.db.models import Q

from django.contrib.auth import get_user_model

User = get_user_model()


class Command(BaseCommand):
    help = (
        "Import user contacts from a cleaned CSV file (located in the project root) "
        "into the User model. If email is missing, a default email is generated using "
        "the normalized_phone. Names are converted to proper case (e.g., 'SULTAN' becomes 'Sultan')."
    )

    def add_arguments(self, parser):
        parser.add_argument(
            "--csv",
            type=str,
            default="contacts_cleaned.csv",
            help="Filename of the cleaned CSV file in the project root (default: contacts_cleaned.csv)",
        )

    def handle(self, *args, **options):
        csv_filename = options["csv"]
        csv_path = os.path.join(settings.BASE_DIR, csv_filename)

        if not os.path.exists(csv_path):
            self.stdout.write(self.style.ERROR(f"CSV file not found at: {csv_path}"))
            return

        created_count = 0
        updated_count = 0
        skipped_count = 0

        with open(csv_path, mode="r", encoding="utf-8") as csvfile:
            reader = csv.DictReader(csvfile)
            for row in reader:
                # Use normalized_phone (if present) for phone_number; fall back to Phone if not.
                normalized_phone = str(row.get("normalized_phone", "")).strip()
                phone = normalized_phone or str(row.get("Phone", "")).strip()

                # Read first and last name and clean them.
                first_name = str(row.get("first_name", "")).strip()
                last_name = str(row.get("last_name", "")).strip()

                # Convert names from ALL-UPPERCASE to a proper format (e.g., 'SULTAN' -> 'Sultan')
                # We use lower() then capitalize() for each.
                if first_name:
                    first_name = first_name.lower().capitalize()
                else:
                    first_name = "NoName"  # default if missing

                if last_name:
                    last_name = last_name.lower().capitalize()
                else:
                    last_name = "NoSurname"  # default if missing

                # Process email: if missing, generate one from the phone.
                email = str(row.get("Email", "")).strip()
                if not email:
                    if phone:
                        email = f"{phone}@example.com"
                    else:
                        self.stdout.write(
                            self.style.WARNING(
                                f"Skipping row because both email and phone are missing: {row}"
                            )
                        )
                        skipped_count += 1
                        continue

                # Lowercase the email to enforce consistency.
                email = email.lower()

                # Optionally, parse the "Created At" field if available.
                created_at_str = str(row.get("Created At", "")).strip()
                created_at = None
                if created_at_str:
                    try:
                        created_at = datetime.fromisoformat(created_at_str)
                    except ValueError:
                        self.stdout.write(
                            self.style.WARNING(f"Invalid date format in row: {row}")
                        )

                # Check for an existing user using email or phone.
                try:
                    user = User.objects.get(Q(email=email) | Q(phone_number=phone))
                    updated = False
                    if user.email != email:
                        user.email = email
                        updated = True
                    if user.phone_number != phone:
                        user.phone_number = phone
                        updated = True
                    if user.first_name != first_name:
                        user.first_name = first_name
                        updated = True
                    if user.last_name != last_name:
                        user.last_name = last_name
                        updated = True

                    if updated:
                        user.save()
                        updated_count += 1
                        self.stdout.write(self.style.SUCCESS(f"Updated user: {email}"))
                    else:
                        self.stdout.write(
                            f"User already exists and is up-to-date: {email}"
                        )
                except User.DoesNotExist:
                    try:
                        with transaction.atomic():
                            user = User.objects.create(
                                email=email,
                                phone_number=phone,
                                first_name=first_name,
                                last_name=last_name,
                                is_active=True,
                                role="customer",
                                # If needed, you can use created_at in a different field or log it.
                            )
                        created_count += 1
                        self.stdout.write(self.style.SUCCESS(f"Created user: {email}"))
                    except Exception as e:
                        self.stdout.write(
                            self.style.ERROR(f"Error creating user for row {row}: {e}")
                        )
                        skipped_count += 1

        self.stdout.write(
            self.style.SUCCESS(
                f"Import complete. Created: {created_count}, Updated: {updated_count}, Skipped: {skipped_count}"
            )
        )
