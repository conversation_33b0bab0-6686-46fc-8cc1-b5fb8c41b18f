# Generated by Django 4.2.19 on 2025-04-12 23:47

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('staff', '0003_therapistprofile_location'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('services', '0002_servicepackage_shared_servicepackage_unlimited'),
    ]

    operations = [
        migrations.AddField(
            model_name='service',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_services', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='service',
            name='is_public',
            field=models.BooleanField(default=False, help_text='If True, the service is visible to unauthenticated users'),
        ),
        migrations.AddField(
            model_name='service',
            name='therapist_specific',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='special_services', to='staff.therapistprofile'),
        ),
        migrations.AddField(
            model_name='servicepackage',
            name='created_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_packages', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='servicepackage',
            name='is_public',
            field=models.BooleanField(default=False, help_text='If True, the package is visible to unauthenticated users'),
        ),
        migrations.AddField(
            model_name='servicepackage',
            name='therapist_specific',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='special_packages', to='staff.therapistprofile'),
        ),
    ]
