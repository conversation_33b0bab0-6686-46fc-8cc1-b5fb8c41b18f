from rest_framework import viewsets
from rest_framework.views import APIView
from rest_framework.generics import (
    ListCreateAPIView,
    RetrieveUpdateDestroyAPIView,
    CreateAPIView,
)
from rest_framework.permissions import IsAuthenticated
from rest_framework_simplejwt.authentication import JWTAuthentication
from rest_framework.permissions import IsAdminUser
from rest_framework.response import Response
from rest_framework import status
from api.core.permissions import IsStaffUser

ERROR_MSG = "Method not allowed"


class BaseViewSet(viewsets.ModelViewSet):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]


class StaffViewSet(viewsets.ModelViewSet):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated, IsStaffUser]


class BaseApiView(APIView):
    authentication_classes = [JWTAuthentication]
    permission_classes = [IsAuthenticated]


class StaffApiView(BaseApiView):
    permission_classes = [IsAuthenticated, IsStaffUser]


class StaffCreateApiView(StaffApiView, CreateAPIView):
    queryset = []
    serializer_class = None


class StaffListCreateApiView(StaffApiView, ListCreateAPIView):
    queryset = []
    serializer_class = None


class StaffRetrieveUpdateDestroyApiView(StaffApiView, RetrieveUpdateDestroyAPIView):
    queryset = []
    serializer_class = None

    def put(self, request, *args, **kwargs):
        return Response({"error": ERROR_MSG}, status=status.HTTP_405_METHOD_NOT_ALLOWED)


class NonStaffApiView(BaseApiView):
    pass


class NonStaffCreateApiView(NonStaffApiView, CreateAPIView):
    queryset = []
    serializer_class = None


class NonStaffListCreateApiView(NonStaffApiView, ListCreateAPIView):
    queryset = []
    serializer_class = None


class NonStaffRetrieveUpdateDestroyApiView(NonStaffApiView, RetrieveUpdateDestroyAPIView):
    queryset = []
    serializer_class = None

    def put(self, request, *args, **kwargs):
        return Response({"error": ERROR_MSG}, status=status.HTTP_405_METHOD_NOT_ALLOWED)
