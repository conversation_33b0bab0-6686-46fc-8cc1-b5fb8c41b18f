from django.core.management.base import BaseCommand
from api.commissions.models import CommissionRule, CommissionProfile
from api.services.models import ServicePackage


class Command(BaseCommand):
    help = 'Display all current commission rules and profiles'

    def handle(self, *args, **options):
        self.stdout.write('📋 CURRENT COMMISSION RULES AND PROFILES')
        self.stdout.write('=' * 60)

        # Show commission profiles first
        self.stdout.write('\n🏢 COMMISSION PROFILES:')
        profiles = CommissionProfile.objects.filter(is_active=True)
        
        for profile in profiles:
            self.stdout.write(f'\n📋 Profile: {profile.name}')
            self.stdout.write(f'   👨‍⚕️ Therapist: {profile.therapist.user.get_full_name()}')
            self.stdout.write(f'   📊 Base Percentage: {profile.base_percentage}%')
            self.stdout.write(f'   🎯 Sessions Threshold: {profile.sessions_threshold}')
            self.stdout.write(f'   ✅ Default: {profile.is_default}')

        # Show commission rules
        self.stdout.write('\n\n🔧 COMMISSION RULES:')
        rules = CommissionRule.objects.filter(is_active=True).order_by('profile__name', '-priority')

        if not rules.exists():
            self.stdout.write('   ❌ No active commission rules found')
            return

        current_profile = None
        for rule in rules:
            # Group by profile
            if rule.profile != current_profile:
                current_profile = rule.profile
                profile_name = rule.profile.name if rule.profile else "🌐 GLOBAL RULES"
                self.stdout.write(f'\n📋 {profile_name}:')

            self.stdout.write(f'\n   🔧 Rule: {rule.name}')
            self.stdout.write(f'      🎯 Type: {rule.rule_type}')
            self.stdout.write(f'      🏆 Priority: {rule.priority}')
            self.stdout.write(f'      📊 Min Sessions: {rule.min_sessions}')
            
            # Commission details
            if rule.percentage and rule.percentage > 0:
                self.stdout.write(f'      💰 Commission: {rule.percentage}% (percentage)')
            elif rule.fixed_amount and rule.fixed_amount > 0:
                self.stdout.write(f'      💰 Commission: {rule.fixed_amount} AED (fixed)')
            else:
                self.stdout.write(f'      💰 Commission: ❌ Not set')
            
            # Scope
            if rule.service:
                self.stdout.write(f'      🔧 Service: {rule.service.name}')
            elif rule.package:
                self.stdout.write(f'      📦 Package: {rule.package.name}')
            else:
                self.stdout.write(f'      🌐 Scope: Global rule')

        # Show expert packages for reference
        self.stdout.write('\n\n📦 EXPERT PACKAGES (for reference):')
        expert_packages = ServicePackage.objects.filter(name__icontains='expert', is_active=True)
        
        if expert_packages.exists():
            for package in expert_packages:
                self.stdout.write(f'\n📦 Package: {package.name}')
                options = package.package_options.all()
                for option in options:
                    price_per_min = float(option.price) / option.time if option.time > 0 else 0
                    self.stdout.write(f'   ⏱️ Option: {option.time} min - {option.price} AED ({price_per_min:.4f} AED/min)')
        else:
            self.stdout.write('   ❌ No expert packages found')

        self.stdout.write('\n✅ Commission rules display completed!')
