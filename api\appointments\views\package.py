from datetime import datetime, timed<PERSON>ta
import calendar
from django.db.models import Q, Sum
from django.utils.dateparse import parse_date
from rest_framework.exceptions import ValidationError
from rest_framework import status
from rest_framework import generics
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from rest_framework.pagination import PageNumberPagination
from rest_framework.views import APIView
from django.shortcuts import get_object_or_404
from django.contrib.auth import get_user_model
from rest_framework.filters import SearchFilter
from django.http import Http404, HttpResponse
from django_filters.rest_framework import DjangoFilterBackend
from api.appointments.models import (
    Activity,
    Appointment,
    AppointmentService,
    UserPackage,
    SharedPackage,
    SharedPackageUser,
    UnlimitedPackage,
    Discount,
    RewardBalance,
)
from api.services.models import Service, ServiceDuration, PackageOption
from api.appointments.serializers import (
    AppointmentSerializer,
    UserPackageSerializer,
    SharedPackageSerializer,
    UnlimitedPackageSerializer,
    AddMinutesToAppointmentSerializer,
    AppointmentAdditionalServiceSerializer,
    AppointmentAdditionalService,
    SharedPackageUserSerializer,
    PackageUsageSerializer,
    DiscountSerializer,
    AdminRewardSerializer,
    RewardBalanceSerializer,
)

from api.core.permissions import IsOwnerOrReceptionist
from api.staff.models import TherapistProfile, WorkingHour
from api.authentication.serializers import UserProfileSerializer
import traceback
from decimal import Decimal
from rest_framework.exceptions import NotFound, APIException, PermissionDenied
from api.staff.serializers import (
    TherapistProfileOptimizedSerializer,
)
from django.utils import timezone
import csv

User = get_user_model()

from django.template.loader import render_to_string
from django.utils.html import strip_tags
from django.core.mail import send_mail
from django.conf import settings

from .utils import PackageViewMixin

# Constants
MIN_ACTIVE_MINUTES = 0
BREAK_TIME = 10  # minutes break between appointments
NEW_APPOINTMENT_BREAK = 10  # minutes


from utils.logging import api_logger, log_request_data, log_response_data, log_error


class ActivePackage(generics.RetrieveAPIView):
    serializer_class = UserPackageSerializer
    permission_classes = [IsAuthenticated]

    def get(self, request):
        log_request_data(request, "Active Package Request")
        user = request.user
        api_logger.info(f"📥 Fetching active package for User:{user.id}")

        try:
            # Query for an active UserPackage for the user.
            active_package = UserPackage.objects.filter(
                user=user, active=True, remaining_time__gte=MIN_ACTIVE_MINUTES
            ).first()

            if active_package:
                api_logger.info(
                    f"✅ Found active package ID:{active_package.id} for User:{user.id}"
                )
                serializer = self.get_serializer(active_package)
                response = Response(serializer.data, status=200)
                log_response_data(response, "Active Package Response")
                return response
            else:
                api_logger.warning(f"❌ No active package found for User:{user.id}")
                response = Response({"detail": "No active package found"}, status=404)
                log_response_data(response, "Active Package Not Found Response")
                return response
        except Exception as e:
            log_error(e, f"Error fetching active package for User:{user.id}")
            raise


class UserPackageDetailView(generics.RetrieveAPIView, PackageViewMixin):
    """Retrieve details for a specific user package"""

    serializer_class = UserPackageSerializer
    permission_classes = [IsAuthenticated]

    def get(self, request, *args, **kwargs):
        log_request_data(request, "Package Detail Request")
        api_logger.info(f"📥 Retrieving package details for ID:{self.kwargs.get('pk')}")

        try:
            response = super().get(request, *args, **kwargs)
            api_logger.info(
                f"✅ Successfully retrieved package ID:{self.kwargs.get('pk')}"
            )
            log_response_data(response, "Package Detail Response")
            return response
        except PermissionDenied as pd:
            log_error(
                pd,
                f"Permission denied for package ID:{self.kwargs.get('pk')}",
                log_full_trace=False,
            )
            raise
        except Exception as e:
            log_error(e, f"Error retrieving package ID:{self.kwargs.get('pk')}")
            raise

    def get_object(self):
        package_id = self.kwargs.get("pk")
        api_logger.debug(f"🔍 Looking up package with ID:{package_id}")

        try:
            package = get_object_or_404(UserPackage, id=package_id)

            # Check access permissions
            if not self.check_package_access(package, self.request, "user_package"):
                api_logger.warning(
                    f"❌ Access denied for User:{self.request.user.id} to Package:{package_id}"
                )
                raise PermissionDenied("You don't have permission to view this package.")

            return package
        except Http404:
            api_logger.warning(f"❌ Package not found with ID:{package_id}")
            raise
        except Exception as e:
            log_error(e, f"Error in get_object for Package:{package_id}")
            raise


@api_view(["GET"])
@permission_classes([IsAuthenticated])
def user_package_usage(request, pk):
    """Get usage history for a user package"""
    log_request_data(request, "Package Usage Request")
    api_logger.info(f"📥 Fetching usage for Package:{pk} by User:{request.user.id}")

    try:
        package = get_object_or_404(UserPackage, id=pk)
        api_logger.debug(f"🔍 Found package with ID:{pk}")

        # Create an instance of the mixin to use its methods
        mixin = PackageViewMixin()

        # Check access permissions
        if not mixin.check_package_access(package, request, "user_package"):
            api_logger.warning(
                f"❌ Access denied for User:{request.user.id} to Package:{pk}"
            )
            response = Response(
                {"detail": "You don't have permission to view this package."},
                status=status.HTTP_403_FORBIDDEN,
            )
            log_response_data(response, "Package Usage Permission Denied")
            return response

        api_logger.info(f"🔄 Processing usage data for Package:{pk}")
        usage_data = mixin.get_package_usage(package, "user_package")
        api_logger.info(f"✅ Successfully retrieved usage data for Package:{pk}")

        response = Response(usage_data)
        log_response_data(response, "Package Usage Response")
        return response
    except Http404:
        api_logger.warning(f"❌ Package not found with ID:{pk}")
        raise
    except Exception as e:
        log_error(e, f"Error retrieving usage data for Package:{pk}")
        raise


class PackageUpdateExpiryView(APIView):
    """
    API view for updating the expiry date of any package type.
    Only owner and receptionist roles can update expiry dates.
    """

    permission_classes = [IsAuthenticated, IsOwnerOrReceptionist]

    def patch(self, request, package_type, pk):
        """
        Update the expiry date of a package.

        Args:
            package_type: Type of package ('user_package', 'shared_package', or 'unlimited_package')
            pk: ID of the package to update
        """
        log_request_data(request, "Update Package Expiry Request")
        api_logger.info(
            f"📥 Updating expiry date for {package_type}:{pk} by User:{request.user.id}"
        )

        try:
            # Validate the incoming expiry date
            expiry_date = request.data.get("expiry_date")
            if not expiry_date:
                api_logger.warning(f"❌ Missing expiry date for {package_type}:{pk}")
                response = Response(
                    {"detail": "Expiry date is required."},
                    status=status.HTTP_400_BAD_REQUEST,
                )
                log_response_data(response, "Missing Expiry Date Error")
                return response

            # Try to parse the date
            try:
                # If string format, parse it
                if isinstance(expiry_date, str):
                    expiry_datetime = datetime.fromisoformat(
                        expiry_date.replace("Z", "+00:00")
                    )
                    api_logger.debug(f"🔍 Parsed expiry date: {expiry_datetime}")
                else:
                    api_logger.warning(
                        f"❌ Invalid expiry date format for {package_type}:{pk}"
                    )
                    response = Response(
                        {"detail": "Invalid expiry date format."},
                        status=status.HTTP_400_BAD_REQUEST,
                    )
                    log_response_data(response, "Invalid Expiry Format Error")
                    return response
            except (ValueError, TypeError):
                api_logger.warning(
                    f"❌ Failed to parse expiry date for {package_type}:{pk}"
                )
                response = Response(
                    {
                        "detail": "Invalid expiry date format. Use ISO format (YYYY-MM-DDTHH:MM:SS)."
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )
                log_response_data(response, "Date Parse Error")
                return response

            # Get the appropriate package based on type
            api_logger.debug(f"🔍 Looking up {package_type} with ID:{pk}")
            if package_type == "user_package":
                package = get_object_or_404(UserPackage, id=pk)
            elif package_type == "shared_package":
                package = get_object_or_404(SharedPackage, id=pk)
            elif package_type == "unlimited_package":
                package = get_object_or_404(UnlimitedPackage, id=pk)
            else:
                api_logger.warning(f"❌ Invalid package type: {package_type}")
                response = Response(
                    {
                        "detail": "Invalid package type. Use 'user_package', 'shared_package', or 'unlimited_package'."
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )
                log_response_data(response, "Invalid Package Type Error")
                return response

            # Check if the new expiry date is in the future (optional validation)
            # now = timezone.now()
            # if expiry_datetime.replace(tzinfo=now.tzinfo) < now:
            #     api_logger.warning(
            #         f"❌ Past expiry date specified for {package_type}:{pk}"
            #     )
            #     response = Response(
            #         {"detail": "Expiry date must be in the future."},
            #         status=status.HTTP_400_BAD_REQUEST,
            #     )
            #     log_response_data(response, "Past Expiry Date Error")
            #     return response

            # Update the expiry date
            old_expiry = package.expiry_date
            api_logger.info(
                f"🔄 Changing expiry date from {old_expiry} to {expiry_datetime} for {package_type}:{pk}"
            )
            package.expiry_date = expiry_datetime

            # If the package has a remaining_time > 0 but was inactive due to expiry date,
            # we can optionally re-activate it
            if (
                package.remaining_time > 0
                and not package.active
                and old_expiry
                and old_expiry < now
            ):
                api_logger.info(
                    f"✅ Re-activating expired but valid package {package_type}:{pk}"
                )
                package.active = True

            # Save the package
            package.save()
            api_logger.info(
                f"✅ Successfully updated expiry date for {package_type}:{pk}"
            )

            # Create a response with appropriate details
            response_data = {
                "id": package.id,
                "package_type": package_type,
                "expiry_date": package.expiry_date,
                "active": package.active,
                "remaining_time": package.remaining_time,
                "total_time": package.total_time,
                "message": "Package expiry date updated successfully.",
            }

            response = Response(response_data, status=status.HTTP_200_OK)
            log_response_data(response, "Package Expiry Update Success")
            return response

        except Http404:
            api_logger.warning(f"❌ Package not found: {package_type}:{pk}")
            raise
        except Exception as e:
            log_error(e, f"Error updating expiry date for {package_type}:{pk}")
            response = Response(
                {"detail": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
            log_response_data(response, "Package Expiry Update Error")
            return response


class PackageUpdateRemainingTimeView(APIView):
    """
    API view for updating the remaining time of any package type.
    Only owner and receptionist roles can update remaining time.
    """

    permission_classes = [IsAuthenticated, IsOwnerOrReceptionist]

    def patch(self, request, package_type, pk):
        """
        Update the remaining time of a package.

        Args:
            package_type: Type of package ('user_package', 'shared_package', or 'unlimited_package')
            pk: ID of the package to update
        """
        log_request_data(request, "Update Package Remaining Time Request")
        api_logger.info(
            f"📥 Updating remaining time for {package_type}:{pk} by User:{request.user.id}"
        )

        try:
            # Validate the incoming remaining time
            remaining_time = request.data.get("remaining_time")
            if remaining_time is None:
                api_logger.warning(f"❌ Missing remaining time for {package_type}:{pk}")
                response = Response(
                    {"detail": "Remaining time is required."},
                    status=status.HTTP_400_BAD_REQUEST,
                )
                log_response_data(response, "Missing Remaining Time Error")
                return response

            # Try to parse the remaining time
            try:
                remaining_time = int(remaining_time)
                if remaining_time < 0:
                    api_logger.warning(
                        f"❌ Negative remaining time ({remaining_time}) for {package_type}:{pk}"
                    )
                    response = Response(
                        {"detail": "Remaining time cannot be negative."},
                        status=status.HTTP_400_BAD_REQUEST,
                    )
                    log_response_data(response, "Negative Time Error")
                    return response
            except (ValueError, TypeError):
                api_logger.warning(
                    f"❌ Invalid remaining time format for {package_type}:{pk}"
                )
                response = Response(
                    {
                        "detail": "Invalid remaining time format. Use a positive integer value."
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )
                log_response_data(response, "Invalid Time Format Error")
                return response

            # Get the appropriate package based on type
            api_logger.debug(f"🔍 Looking up {package_type} with ID:{pk}")
            if package_type == "user_package":
                package = get_object_or_404(UserPackage, id=pk)
            elif package_type == "shared_package":
                package = get_object_or_404(SharedPackage, id=pk)
            elif package_type == "unlimited_package":
                api_logger.warning(
                    f"❌ Attempted to update remaining time on unlimited package:{pk}"
                )
                response = Response(
                    {"detail": "Unlimited packages don't have remaining time."},
                    status=status.HTTP_400_BAD_REQUEST,
                )
                log_response_data(response, "Unlimited Package Error")
                return response
            else:
                api_logger.warning(f"❌ Invalid package type: {package_type}")
                response = Response(
                    {
                        "detail": "Invalid package type. Use 'user_package', 'shared_package', or 'unlimited_package'."
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )
                log_response_data(response, "Invalid Package Type Error")
                return response

            # Update the remaining time
            old_remaining_time = package.remaining_time
            api_logger.info(
                f"🔄 Changing remaining time from {old_remaining_time} to {remaining_time} for {package_type}:{pk}"
            )
            package.remaining_time = remaining_time

            # If the package was inactive due to zero remaining time, reactivate it
            if old_remaining_time <= 0 and remaining_time > 0 and not package.active:
                # Only reactivate if not expired
                now = timezone.now()
                if not package.expiry_date or package.expiry_date > now:
                    api_logger.info(
                        f"✅ Re-activating package with new remaining time: {package_type}:{pk}"
                    )
                    package.active = True

            # Save the package
            package.save()
            api_logger.info(
                f"✅ Successfully updated remaining time for {package_type}:{pk}"
            )

            # Create a response with appropriate details
            response_data = {
                "id": package.id,
                "package_type": package_type,
                "remaining_time": package.remaining_time,
                "total_time": package.total_time,
                "active": package.active,
                "expiry_date": package.expiry_date,
                "message": "Package remaining time updated successfully.",
            }

            response = Response(response_data, status=status.HTTP_200_OK)
            log_response_data(response, "Package Time Update Success")
            return response

        except Http404:
            api_logger.warning(f"❌ Package not found: {package_type}:{pk}")
            raise
        except Exception as e:
            log_error(e, f"Error updating remaining time for {package_type}:{pk}")
            response = Response(
                {"detail": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
            log_response_data(response, "Package Time Update Error")
            return response


class PackageDeleteView(APIView):
    """
    API view for deleting a package of any type.
    Only owner and receptionist roles can delete packages.
    WARNING: This will also delete all associated appointments and reward points.
    """

    permission_classes = [IsAuthenticated, IsOwnerOrReceptionist]

    def delete(self, request, package_type, pk):
        """
        Delete a package and all its associated data.

        Args:
            package_type: Type of package ('user_package', 'shared_package', or 'unlimited_package')
            pk: ID of the package to delete
        """
        log_prefix = f"🗑️ Package Delete [{package_type}:{pk}]"
        log_request_data(request, log_prefix)

        try:
            # Get the appropriate package based on type
            if package_type == "user_package":
                package = get_object_or_404(UserPackage, id=pk)
                # Get related appointments
                related_appointments = Appointment.objects.filter(user_package=package)
            elif package_type == "shared_package":
                package = get_object_or_404(SharedPackage, id=pk)
                # Get related appointments
                related_appointments = Appointment.objects.filter(shared_package=package)
            elif package_type == "unlimited_package":
                package = get_object_or_404(UnlimitedPackage, id=pk)
                # Get related appointments
                related_appointments = Appointment.objects.filter(
                    unlimited_package=package
                )
            else:
                error_msg = f"❌ Invalid package type: {package_type}"
                api_logger.warning(error_msg)
                response = Response(
                    {
                        "detail": "Invalid package type. Use 'user_package', 'shared_package', or 'unlimited_package'."
                    },
                    status=status.HTTP_400_BAD_REQUEST,
                )
                log_response_data(response, log_prefix)
                return response

            # Collect information about what will be deleted for the log
            appointment_count = related_appointments.count()
            package_info = {
                "id": package.id,
                "package_type": package_type,
                "total_time": package.total_time,
                "remaining_time": package.remaining_time,
                "appointments_deleted": appointment_count,
                "user_id": getattr(package, "user_id", None),
                "created_at": getattr(package, "created_at", None),
            }

            api_logger.info(
                f"🔄 {log_prefix} - Processing deletion of {package_type}:{pk} with {appointment_count} appointments"
            )

            # First delete related appointments (this will cascade delete reward points)
            related_appointments.delete()
            api_logger.info(
                f"✅ {log_prefix} - Deleted {appointment_count} related appointments"
            )

            # Then delete the package itself
            package.delete()
            api_logger.info(f"✅ {log_prefix} - Package successfully deleted")

            # Create response data
            response_data = {
                "success": True,
                "message": f"Package and {appointment_count} related appointments successfully deleted.",
                "deleted_package": package_info,
            }

            response = Response(response_data, status=status.HTTP_200_OK)
            log_response_data(response, log_prefix)
            return response

        except Exception as e:
            log_error(e, f"{log_prefix} - Failed to delete package", log_full_trace=True)
            response = Response(
                {"detail": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
            log_response_data(response, f"❌ {log_prefix}")
            return response
