# Generated by Django 4.2.19 on 2025-05-21 05:26

from decimal import Decimal
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('staff', '0004_therapistprofile_is_active'),
        ('commissions', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='commissionearning',
            name='is_eligible',
            field=models.BooleanField(default=True, help_text='Whether this commission is eligible based on business rules'),
        ),
        migrations.CreateModel(
            name='TherapistMonthStats',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('year', models.PositiveIntegerField()),
                ('month', models.PositiveIntegerField()),
                ('total_sessions', models.PositiveIntegerField(default=0)),
                ('total_earnings', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10)),
                ('is_paid', models.<PERSON><PERSON>anField(default=False)),
                ('payment_date', models.DateField(blank=True, null=True)),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('therapist', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='month_stats', to='staff.therapistprofile')),
            ],
            options={
                'verbose_name_plural': 'Therapist Month Stats',
                'ordering': ['-year', '-month'],
                'unique_together': {('therapist', 'year', 'month')},
            },
        ),
        migrations.AddField(
            model_name='commissionearning',
            name='month_stat',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='commissions', to='commissions.therapistmonthstats'),
        ),
        migrations.CreateModel(
            name='TherapistYearStats',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('year', models.PositiveIntegerField()),
                ('total_sessions', models.PositiveIntegerField(default=0)),
                ('total_earnings', models.DecimalField(decimal_places=2, default=Decimal('0.00'), max_digits=10)),
                ('last_updated', models.DateTimeField(auto_now=True)),
                ('therapist', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='year_stats', to='staff.therapistprofile')),
            ],
            options={
                'verbose_name_plural': 'Therapist Year Stats',
                'ordering': ['-year'],
                'unique_together': {('therapist', 'year')},
            },
        ),
    ]
