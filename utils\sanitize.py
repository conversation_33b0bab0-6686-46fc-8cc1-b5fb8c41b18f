import nh3
import re


def remove_encoded_tags(text):
    # This regex removes encoded HTML entities like &lt; and &gt;
    clean = re.compile(r"&lt;|&gt;")
    return re.sub(clean, "", text)


def remove_trailing_s(text):
    # Strip trailing whitespace
    return text.rstrip()


def sanitize_input(input_data):
    if isinstance(input_data, str):
        sanitized = nh3.clean(input_data)
        sanitized = remove_encoded_tags(sanitized)
        return remove_trailing_s(sanitized)

    elif isinstance(input_data, dict):
        return {k: sanitize_input(v) for k, v in input_data.items()}
    elif isinstance(input_data, list):
        return [sanitize_input(item) for item in input_data]
    return input_data
