from django.db import models
from django.conf import settings
from api.services.models import Service, ServicePackage, PackageOption
from api.staff.models import TherapistProfile
from dateutil.relativedelta import relativedelta
from datetime import timedelta
from django.utils import timezone
import traceback
from decimal import Decimal

from django.utils import timezone
from dateutil.relativedelta import relativedelta
from django.core.files.base import ContentFile

from weasyprint import HTML
from django.template.loader import render_to_string
import datetime
import base64
import os
import re


class Discount(models.Model):
    name = models.CharField(max_length=100)
    code = models.CharField(max_length=50, unique=True)
    discount_percentage = models.DecimalField(max_digits=10, decimal_places=5)
    description = models.TextField(blank=True)
    valid_from = models.DateTimeField(auto_now_add=True)
    valid_until = models.DateTimeField()

    def __str__(self):
        return f"{self.name} ({self.code})"


class UnlimitedPackage(models.Model):
    """
    Represents an unlimited package for a single user.
    This package allows the user to book as many services as they want within a 1-month period.
    On creation, the package is set with total_time and remaining_time equal to 99999.
    Its expiry_date is set to one month from the moment of creation (i.e. after the first appointment).
    """

    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="unlimited_package",
    )
    package_option = models.ForeignKey(
        PackageOption,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="unlimited_packages",
    )
    total_time = models.PositiveIntegerField(
        default=99999,
        help_text="Total minutes available (always 99999 for unlimited packages)",
    )
    remaining_time = models.PositiveIntegerField(
        default=99999, help_text="Remaining minutes available (always starts at 99999)"
    )
    time_deducted = models.PositiveIntegerField(
        default=0, help_text="Total minutes that have been used (for reporting purposes)"
    )
    active = models.BooleanField(
        default=True,
        help_text="Indicates if the package is active (until expiry or manually deactivated)",
    )
    expiry_date = models.DateTimeField(
        null=True,
        blank=True,
        help_text="The date when the package will expire (1 month from creation)",
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def calculate_expiry_date(self):
        """Sets expiry date to one month from now."""
        return timezone.now() + relativedelta(months=1)

    def save(self, *args, **kwargs):
        # Set expiry_date only on creation.
        if not self.pk:
            self.expiry_date = self.calculate_expiry_date()
        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.user.email} - Unlimited Package (Expires: {self.expiry_date})"


class SharedPackage(models.Model):
    """
    Represents a shared package that multiple users can use.
    A receptionist creates a shared package and assigns users.
    """

    package_option = models.ForeignKey(
        PackageOption,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="shared_packages",
    )
    total_time = models.PositiveIntegerField(
        help_text="Total minutes available for all users in this shared package"
    )
    remaining_time = models.PositiveIntegerField(
        help_text="Remaining minutes available for use"
    )
    expiry_date = models.DateTimeField(
        null=True,
        blank=True,
        help_text="The date when the package will expire.",
    )
    active = models.BooleanField(
        default=True,
        help_text="Indicates if the package is active (i.e. remaining_time > 0)",
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def calculate_expiry_date(self):
        """Calculate expiry date based on package time."""
        purchase_date = timezone.now()
        if self.total_time == 500:
            return purchase_date + relativedelta(months=6)
        elif self.total_time == 1000:
            return purchase_date + relativedelta(months=10)
        return None

    def save(self, *args, **kwargs):
        """
        Set expiry_date only when creating a new object.
        """
        if not self.pk:  # New object
            self.expiry_date = self.calculate_expiry_date()
        super().save(*args, **kwargs)

    def __str__(self):
        return f"Shared Package ({self.total_time} min) - {self.remaining_time} min left"


class SharedPackageUser(models.Model):
    """
    Links users to a Shared Package.
    Users can be added or removed from a shared package.
    """

    shared_package = models.ForeignKey(
        SharedPackage, on_delete=models.CASCADE, related_name="users"
    )
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name="shared_packages"
    )
    added_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ("shared_package", "user")

    def __str__(self):
        return f"{self.user.email} - Shared Package {self.shared_package.id}"


class UserPackage(models.Model):
    """
    Represents a package that a user has purchased.
    A user may have at most one active package.
    When a new appointment is booked using a package, the package's
    remaining time is deducted. When remaining_time reaches zero,
    the package is considered inactive.
    """

    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="user_packages",  # no unique constraint here
    )
    package_option = models.ForeignKey(
        PackageOption,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="user_packages",
    )
    total_time = models.PositiveIntegerField(
        help_text="Total minutes purchased with this package"
    )
    remaining_time = models.PositiveIntegerField(
        help_text="Remaining minutes available in the package"
    )
    time_deducted = models.PositiveIntegerField(
        default=0, help_text="Total minutes that have been used"
    )
    active = models.BooleanField(
        default=True,
        help_text="Indicates if the package is active (i.e. remaining_time > 0)",
    )
    expiry_date = models.DateTimeField(
        null=True,
        blank=True,
        help_text="The date when the package will expire.",
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def calculate_expiry_date(self):
        """Calculates expiry date based on total_time."""
        purchase_date = timezone.now()

        if self.total_time == 100:
            return purchase_date + timedelta(weeks=5)
        elif self.total_time == 300:
            return purchase_date + relativedelta(months=4)
        elif self.total_time == 500:
            return purchase_date + relativedelta(months=6)
        return None  # If total_time is not predefined, expiry_date is left as None

    def save(self, *args, **kwargs):
        """
        Sets the expiry_date **only when creating a new object**.
        If expiry_date is manually modified later, it won't be overwritten.
        """
        if not self.pk:  # This means the object is being created (added)
            self.expiry_date = self.calculate_expiry_date()

        super().save(*args, **kwargs)

    def __str__(self):
        return f"{self.user.email} - {self.remaining_time} mins remaining (Total: {self.total_time})"


class Appointment(models.Model):
    STATUS_CHOICES = [
        ("booked", "Booked"),
        ("check_in", "check_in"),
        ("no_show", "No-Show"),
        ("in_edit", "In Edit"),
    ]

    LOCATION_CHOICES = [
        ("A", "Studio Al Warqa Mall"),
        ("B", "Studio Al mizhar branch"),
    ]

    PAYMENT_METHOD_CHOICES = [("cash", "Cash"), ("card", "Card"), ("link", "Link")]

    customer = models.ForeignKey(
        settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name="appointments"
    )
    therapist = models.ForeignKey(
        TherapistProfile, on_delete=models.CASCADE, related_name="appointments"
    )
    package_option = models.ForeignKey(
        PackageOption,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="appointments",
    )
    user_package = models.ForeignKey(
        UserPackage,
        on_delete=models.CASCADE,
        null=True,
        blank=True,
        related_name="appointments",
    )
    shared_package = models.ForeignKey(
        SharedPackage,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="appointments",
    )
    unlimited_package = models.ForeignKey(
        UnlimitedPackage,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="appointments",
    )
    location = models.CharField(max_length=1, choices=LOCATION_CHOICES)
    date = models.DateField()
    time = models.TimeField()
    total_duration = models.PositiveIntegerField(
        help_text="Total duration of the appointment in minutes"
    )
    total_price = models.DecimalField(
        max_digits=10, decimal_places=2, help_text="Duration in minutes", default=100
    )
    notes = models.TextField(blank=True, null=True)
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default="booked")
    payment_method = models.CharField(
        max_length=20,
        choices=PAYMENT_METHOD_CHOICES,
        default="",
        blank=True,
    )
    needs_manual_split = models.BooleanField(default=False)
    reminder_sent = models.BooleanField(default=False)
    missing_services = models.JSONField(null=True, blank=True)
    discount_percentage = models.DecimalField(max_digits=10, decimal_places=5, default=0)
    discount = models.ForeignKey(
        Discount, null=True, blank=True, on_delete=models.SET_NULL
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Appointment: {self.customer.email} with {self.therapist.user.email} on {self.date} at {self.time}"


class AppointmentService(models.Model):
    """
    Links an individual service with an appointment.
    Tracks the duration of each selected service.
    """

    appointment = models.ForeignKey(
        Appointment, related_name="appointment_services", on_delete=models.CASCADE
    )
    service = models.ForeignKey(
        Service, on_delete=models.CASCADE, related_name="appointments"
    )
    duration = models.PositiveIntegerField(help_text="Duration of the service in minutes")
    price = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        help_text="Price for this specific service duration",
        default=0,
    )

    def __str__(self):
        return f"{self.appointment} - {self.service.name} ({self.duration} min)"


class AppointmentAdditionalService(models.Model):
    """
    Tracks additional services/minutes added to an existing appointment.
    """

    appointment = models.ForeignKey(
        "Appointment", related_name="additional_services", on_delete=models.CASCADE
    )
    service_type = models.CharField(
        max_length=50,
        choices=[
            ("Massage", "Massage"),
            ("Stretching", "Stretching"),
            ("Compression", "Compression"),
            ("Red-Light Therapy", "Red-Light Therapy"),
            ("Physio", "Physio"),
        ],
    )
    duration = models.PositiveIntegerField(
        help_text="Duration of the additional service in minutes"
    )
    price_per_minute = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text="Rate charged per minute for this service",
    )
    total_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        help_text="Total price for this additional service",
    )
    added_at = models.DateTimeField(auto_now_add=True)
    added_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name="added_services",
    )

    def save(self, *args, **kwargs):
        # Auto-calculate total price if not set
        if not self.total_price:
            self.total_price = Decimal(self.duration) * self.price_per_minute
        super().save(*args, **kwargs)

    def __str__(self):
        return (
            f"{self.appointment} - Additional {self.service_type} ({self.duration} min)"
        )


class Reward(models.Model):
    customer = models.ForeignKey(
        settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name="rewards"
    )
    appointment = models.OneToOneField(
        "Appointment", on_delete=models.CASCADE, related_name="reward"
    )
    points = models.IntegerField(help_text="Reward points earned")
    factor = models.IntegerField(
        default=10, help_text="Calculation factor (hidden in public views)"
    )
    reward_date = models.DateField(default=timezone.localdate)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Reward for {self.customer.get_full_name()} - {self.points} points"


class RewardBalance(models.Model):
    customer = models.OneToOneField(
        settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name="reward_balance"
    )
    total_points = models.IntegerField(default=0)
    last_updated = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.customer.get_full_name()} - {self.total_points} points"


class Activity(models.Model):
    """
    Tracks the activities on an appointment
    """

    ACTIVITY_TYPE_CHOICES = [
        ("booking", "Booking"),
        ("confirmation", "Confirmation"),
        ("cancellation", "Cancellation"),
        ("appointment_minutes_added", "Appointment Minutes Added"),
        ("appointment_minutes_removed", "Appointment Minutes Removed"),
    ]
    staff = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="staff",
        null=True,
    )
    customer = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="customer",
        null=True,
    )
    activity_type = models.CharField(max_length=50, choices=ACTIVITY_TYPE_CHOICES)
    description = models.CharField(max_length=255)
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return self.description


class Sale(models.Model):
    SALE_TYPE_CHOICES = [
        ("package", "Package Sale"),
        ("service", "Service Sale (Appointment)"),
    ]

    PAYMENT_METHOD_CHOICES = [("cash", "Cash"), ("card", "Card"), ("link", "Link")]
    LOCATION_CHOICES = [
        ("A", "Studio Al Warqa Mall"),
        ("B", "Studio Al mizhar branch"),
    ]

    user = models.ForeignKey(
        settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name="sales"
    )
    sale_type = models.CharField(max_length=20, choices=SALE_TYPE_CHOICES)

    appointment = models.ForeignKey(
        Appointment,
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name="sales",
    )
    user_package = models.ForeignKey(
        UserPackage,
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name="sales",
    )
    shared_package = models.ForeignKey(
        SharedPackage,
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name="sales",
    )
    unlimited_package = models.ForeignKey(
        UnlimitedPackage,
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
        related_name="sales",
    )
    payment_method = models.CharField(
        max_length=20,
        choices=PAYMENT_METHOD_CHOICES,
        default="card",
        blank=True,
    )

    package_option = models.ForeignKey(
        PackageOption, null=True, blank=True, on_delete=models.SET_NULL
    )

    total_price = models.DecimalField(max_digits=10, decimal_places=2)
    discount = models.ForeignKey(
        Discount, null=True, blank=True, on_delete=models.SET_NULL
    )
    location = models.CharField(max_length=1, choices=LOCATION_CHOICES, default="A")
    discount_percentage = models.DecimalField(
        max_digits=10, decimal_places=5, default=Decimal("0"), null=False
    )
    created_at = models.DateTimeField(default=timezone.now)

    # New fields for invoice
    invoice = models.FileField(upload_to="invoices/", null=True, blank=True)
    invoice_number = models.CharField(max_length=20, null=True, blank=True, unique=True)

    def save(self, *args, **kwargs):
        """
        Override save method to handle cash register operations and invoice generation.
        """
        # Get the original sale instance if it exists
        is_new = self.pk is None

        # Flag to control invoice generation to avoid recursive saves
        is_generating_invoice = kwargs.pop("is_generating_invoice", False)
        update_fields = kwargs.get("update_fields")

        # Skip cash register operations if just updating invoice
        skip_cash_register = update_fields and set(update_fields) == {"invoice"}

        # If this is an existing sale, load the original for comparison
        if not is_new and not skip_cash_register:
            try:
                # Get the original record from the database
                original = self.__class__.objects.get(pk=self.pk)
                original_total = original.total_price
                original_payment_method = original.payment_method

                # Check if total price or payment method changed
                price_changed = original_total != self.total_price
                payment_method_changed = original_payment_method != self.payment_method
                needs_cash_update = price_changed or payment_method_changed

                # Only if we need to update cash register
                if needs_cash_update:
                    from utils.logging import logger
                    from django.db import transaction

                    with transaction.atomic():
                        # Step 1: If original was cash payment, remove it from register
                        if original_payment_method == "cash" and original_total > 0:
                            date = (
                                self.appointment.date
                                if self.appointment
                                else self.created_at.date()
                            )
                            from api.retail.models import CashRegister

                            try:
                                cash_register = CashRegister.objects.get(
                                    date=date,
                                    location=self.location,
                                )

                                # Remove original amount
                                previous_balance = cash_register.current_balance
                                cash_register.current_balance -= original_total
                                cash_register.save()

                                logger.info(
                                    f"🔄 Removed previous amount from CashRegister:{cash_register.id} for Sale:{self.id}: "
                                    f"Balance {previous_balance} → {cash_register.current_balance}"
                                )
                            except CashRegister.DoesNotExist:
                                logger.warning(
                                    f"⚠️ No CashRegister found for Date:{date}, Location:{self.location} "
                                    f"when updating Sale:{self.id}"
                                )

                        # Step 2: If new payment is cash, add to register
                        if self.payment_method == "cash" and self.total_price > 0:
                            self._add_to_cash_register()

            except self.__class__.DoesNotExist:
                # This shouldn't happen, but just in case
                pass

        # For brand new sales with cash payment
        elif (
            is_new
            and self.payment_method == "cash"
            and self.total_price > 0
            and not skip_cash_register
        ):
            self._add_to_cash_register()

        # Generate invoice number for new sales
        if is_new:
            self.generate_invoice_number()

        # Save the model
        super().save(*args, **kwargs)

        # Generate/update the invoice PDF if needed
        if not is_generating_invoice and (
            is_new or update_fields is None or "total_price" in update_fields
        ):
            self.generate_invoice()

            # Save again with the new invoice file
            if update_fields is None or "invoice" not in update_fields:
                self.__class__.objects.filter(pk=self.pk).update(invoice=self.invoice)

    def _add_to_cash_register(self):
        """Helper method to add current sale amount to cash register"""
        from utils.logging import logger
        from django.db import transaction
        from api.retail.models import CashRegister

        with transaction.atomic():
            # Determine the appropriate date and location
            date = self.appointment.date if self.appointment else self.created_at.date()
            location = self.location

            # Find or create cash register for this date and location
            cash_register, created = CashRegister.objects.get_or_create(
                date=date,
                location=location,
                defaults={
                    "current_balance": 0,
                    "notes": "Auto-created for sale",
                },
            )

            if created:
                logger.info(
                    f"📝 Created new CashRegister for Date:{date}, Location:{location}"
                )

            # Add total price to cash register balance
            previous_balance = cash_register.current_balance
            cash_register.current_balance += self.total_price
            cash_register.save()

            logger.info(
                f"✅ Updated CashRegister:{cash_register.id} for Sale:{self.id}: Balance {previous_balance} → {cash_register.current_balance}"
            )

    def generate_invoice_number(self):
        """Generate a unique invoice number"""
        if self.invoice_number:
            return

        # Determine location code
        location_code = "AW" if self.location == "A" else "AM"

        # Get year from creation date (not current year)
        year = self.created_at.strftime("%y")

        from django.db import transaction

        # Use a transaction with select_for_update to prevent race conditions
        with transaction.atomic():
            # Get the highest existing sequential number
            last_invoice = (
                Sale.objects.filter(
                    invoice_number__startswith=f"INV-{location_code}-{year}"
                )
                .order_by("-invoice_number")
                .select_for_update()
                .first()
            )

            if last_invoice and last_invoice.invoice_number:
                # Extract the sequential number from the last invoice
                try:
                    seq_part = last_invoice.invoice_number.split("-")[-1][
                        2:
                    ]  # Get digits after year
                    last_seq = int(seq_part)
                    seq_number = f"{last_seq + 1:04d}"
                except (IndexError, ValueError):
                    # Fallback if parsing fails
                    seq_number = "0001"
            else:
                # No existing invoices for this year and location
                seq_number = "0001"

            # Create the invoice number
            new_invoice_number = f"INV-{location_code}-{year}{seq_number}"

            # Double-check uniqueness (just in case)
            while Sale.objects.filter(invoice_number=new_invoice_number).exists():
                last_seq += 1
                seq_number = f"{last_seq:04d}"
                new_invoice_number = f"INV-{location_code}-{year}{seq_number}"

            self.invoice_number = new_invoice_number

    def generate_invoice(self):
        """Generate a PDF invoice and save it to the invoice field"""
        try:
            # Prepare the context data
            context = self.get_invoice_context()

            # Render HTML template with context
            template_name = "invoice.html"
            html_string = render_to_string(template_name, context)

            # Generate PDF from HTML
            html = HTML(string=html_string)
            pdf_bytes = html.write_pdf()

            # Save to the invoice field
            filename = f"{self.invoice_number}.pdf"
            self.invoice.save(filename, ContentFile(pdf_bytes), save=False)

            self.__class__.objects.filter(pk=self.pk).update(invoice=self.invoice)

            return True
        except Exception as e:
            from utils.logging import logger

            logger.error(f"Error generating invoice for Sale:{self.id} - {str(e)}")
            return False

    def get_invoice_context(self):
        """Prepare the context data for the invoice template"""
        # Calculate VAT (5% of total)
        subtotal = self.total_price / Decimal("1.05")  # Remove VAT from total
        vat_amount = self.total_price - subtotal

        # Determine description based on sale type
        description = "Service Sale"

        if self.sale_type == "package":
            # Handle package sale
            package_option = None
            package_name = "Package"
            package_time = 0

            # Try to get package details from associated package objects
            if self.user_package and self.user_package.package_option:
                package_option = self.user_package.package_option
            elif self.shared_package and self.shared_package.package_option:
                package_option = self.shared_package.package_option
            elif self.unlimited_package and self.unlimited_package.package_option:
                package_option = self.unlimited_package.package_option
            elif self.package_option:
                package_option = self.package_option

            if package_option:
                package_name = package_option.package.name
                package_time = package_option.time
                if self.unlimited_package:
                    description = f"Package: {package_name}"
                else:
                    description = f"Package: {package_name} ({package_time} min)"
        else:
            # Service sale (appointment)
            if self.appointment:
                description = f"Appointment ({self.appointment.total_duration} min)"

        # Prepare the items list
        items = [{"description": description, "total": f"{float(self.total_price):.2f}"}]

        # Get customer name
        customer_name = f"{self.user.first_name} {self.user.last_name}".strip()
        if not customer_name:
            customer_name = self.user.email

        # Get location description
        location_description = (
            "Studio Al Warqa Mall" if self.location == "A" else "Studio Al Mizhar Branch"
        )

        # Prepare the context
        context = {
            # Invoice info
            "invoice_number": self.invoice_number,
            "payment_method": {"card": "Card", "cash": "Cash", "link": "Payment Link"}[
                self.payment_method
            ],
            "invoice_date": self.created_at.strftime("%d/%m/%Y"),
            "due_date": self.created_at.strftime(
                "%d/%m/%Y"
            ),  # Due date same as invoice date (paid immediately)
            "date_paid": self.created_at.strftime("%d/%m/%Y"),
            "is_paid": True,  # All sales are paid
            # Company and location
            "location": location_description,
            # Customer details
            "bill_to_name": customer_name,
            # Line items and calculations
            "items": items,
            "sub_total": f"{float(subtotal):.2f}",
            "vat_amount": f"{float(vat_amount):.2f}",
            "grand_total": f"{float(self.total_price):.2f}",
        }
        # Load logo as base64
        logo_path = os.path.join(settings.BASE_DIR, "static/images/logo.png")
        if os.path.exists(logo_path):
            with open(logo_path, "rb") as f:
                logo_data = base64.b64encode(f.read()).decode("utf-8")
                context["Logo"] = f"data:image/png;base64,{logo_data}"

        return context

    def delete(self, *args, **kwargs):
        """Override delete method to handle cash register operations."""
        # Only process cash register for cash payments with amount
        if self.payment_method == "cash" and self.total_price > 0:
            from utils.logging import logger
            from django.db import transaction
            from api.retail.models import CashRegister

            try:
                with transaction.atomic():
                    # Determine the appropriate date and location
                    date = (
                        self.appointment.date
                        if self.appointment
                        else self.created_at.date()
                    )
                    location = self.location

                    # Find cash register for this date and location
                    cash_register = CashRegister.objects.get(
                        date=date,
                        location=location,
                    )

                    # Subtract total price from cash register balance
                    previous_balance = cash_register.current_balance
                    cash_register.current_balance -= self.total_price
                    cash_register.save()

                    logger.info(
                        f"✅ Updated CashRegister:{cash_register.id} for deleted Sale:{self.id}: "
                        f"Balance {previous_balance} → {cash_register.current_balance}"
                    )
            except CashRegister.DoesNotExist:
                logger.warning(
                    f"⚠️ No CashRegister found for Date:{date}, Location:{location} when deleting Sale:{self.id}"
                )
            except Exception as e:
                logger.error(
                    f"Error updating cash register for deleted Sale:{self.id} - {str(e)}"
                )

        # Proceed with deletion
        super().delete(*args, **kwargs)

    def __str__(self):
        if self.sale_type == "service" and self.appointment:
            return f"{self.user.email} - Service Sale ({self.appointment.date})"
        elif self.sale_type == "package" and self.package_option:
            return (
                f"{self.user.email} - Package Sale ({self.package_option.package.name})"
            )
        return f"{self.user.email} - Undefined Sale"
