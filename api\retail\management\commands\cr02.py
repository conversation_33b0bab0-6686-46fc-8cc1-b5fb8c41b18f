# your_app/management/commands/update_cash_register.py

from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from api.retail.models import CashRegister


class Command(BaseCommand):
    help = "Updates today's cash register with yesterday's balance"

    def handle(self, *args, **kwargs):
        today = timezone.localdate()
        yesterday = today - timedelta(days=1)  # Look backward, not forward

        for location_code, _ in CashRegister.LOCATION_CHOICES:
            try:
                yesterday_register = CashRegister.objects.get(
                    date=yesterday, location=location_code
                )
            except CashRegister.DoesNotExist:
                self.stdout.write(
                    self.style.WARNING(
                        f"No cash register found for yesterday at location {location_code}"
                    )
                )
                continue

            # Get or create today's register
            today_register, created = CashRegister.objects.get_or_create(
                date=today,
                location=location_code,
                defaults={"current_balance": yesterday_register.current_balance},
            )

            if not created:
                today_register.current_balance = yesterday_register.current_balance
                today_register.save()
                self.stdout.write(f"Updated today's register for {location_code}")
            else:
                self.stdout.write(f"Created today's register for {location_code}")
