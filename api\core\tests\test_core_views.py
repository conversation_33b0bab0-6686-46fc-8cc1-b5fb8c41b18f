import pytest
from django.urls import reverse


@pytest.fixture
def auth_client(api_client, staff_user):
    api_client.force_authenticate(user=staff_user)
    return api_client


@pytest.fixture
def non_auth_client(api_client, non_staff_user):
    api_client.force_authenticate(user=non_staff_user)
    return api_client


@pytest.fixture
def unauthenticated_client(api_client):
    return api_client


@pytest.mark.urls("api.core.tests.urls")
@pytest.mark.django_db
def test_staff_list_create_api_view_methods(auth_client):
    url = reverse("test-list-create")
    assert auth_client.put(url, {}).status_code == 500
    assert auth_client.patch(url, {}).status_code == 500
    assert auth_client.delete(url).status_code == 500


@pytest.mark.urls("api.core.tests.urls")
@pytest.mark.django_db
def test_staff_retrieve_update_destroy_api_view_put(auth_client):
    url = reverse("test-retrieve-update-destroy", kwargs={"pk": 1})
    response = auth_client.put(url, {})
    assert response.status_code == 405


@pytest.mark.urls("api.core.tests.urls")
@pytest.mark.django_db
def test_staff_list_create_api_view_post_non_staff(non_auth_client):
    url = reverse("test-list-create")
    response = non_auth_client.post(url)
    assert response.status_code == 500


@pytest.mark.urls("api.core.tests.urls")
@pytest.mark.django_db
def test_staff_retrieve_update_destroy_api_view_get_non_staff(non_auth_client):
    url = reverse("test-retrieve-update-destroy", kwargs={"pk": 1})
    response = non_auth_client.get(url)
    assert response.status_code == 500


@pytest.mark.urls("api.core.tests.urls")
@pytest.mark.django_db
def test_staff_list_create_api_view_get_unauthenticated(unauthenticated_client):
    url = reverse("test-list-create")
    response = unauthenticated_client.get(url)
    assert response.status_code == 500


@pytest.mark.urls("api.core.tests.urls")
@pytest.mark.django_db
def test_staff_retrieve_update_destroy_api_view_get_unauthenticated(
    unauthenticated_client,
):
    url = reverse("test-retrieve-update-destroy", kwargs={"pk": 1})
    response = unauthenticated_client.get(url)
    assert response.status_code == 500
