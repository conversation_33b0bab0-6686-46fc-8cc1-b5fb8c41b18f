import pandas as pd
from fuzzywuzzy import fuzz, process
from django.core.management.base import BaseCommand
from api.appointments.models import (
    UserPackage,
    PackageOption,
    SharedPackage,
    SharedPackageUser,
    UnlimitedPackage,
    Appointment,
    Reward,
    RewardBalance,
)

from api.retail.models import (
    ProductSale,
    CashWithdrawal,
    CashDeposit,
    Expense,
    DailySalesReport,
)

from django.contrib.auth import get_user_model

User = get_user_model()


class Command(BaseCommand):
    help = "Populate User Packages from CSV file"

    def handle(self, *args, **kwargs):
        df = pd.read_csv("all_packages.csv")

        UserPackage.objects.all().delete()
        SharedPackage.objects.all().delete()
        SharedPackageUser.objects.all().delete()
        UnlimitedPackage.objects.all().delete()
        Appointment.objects.all().delete()
        Reward.objects.all().delete()
        RewardBalance.objects.all().delete()
        ProductSale.objects.all().delete()
        CashWithdrawal.objects.all().delete()
        CashDeposit.objects.all().delete()
        Expense.objects.all().delete()
        DailySalesReport.objects.all().delete()

        for index, row in df.iterrows():
            customer_name = row["Customer Name"]
            package_name = row["Package Type"]
            remaining_time = row["Remaining Minutes"]

            users = User.objects.all()
            matches = []

            for dummy_user in users:
                full_name = f"{dummy_user.first_name} {dummy_user.last_name}"
                ratio = fuzz.token_sort_ratio(customer_name.lower(), full_name.lower())

                if ratio > 80:  # adjust the threshold as needed
                    matches.append((dummy_user, ratio))
                    user = dummy_user

            if len(matches) == 0:
                user = None

            if not user:
                self.stdout.write(self.style.WARNING(f"User {customer_name} not found."))
                continue

            package_option = PackageOption.objects.filter(
                package__name=package_name
            ).first()
            if not package_option:
                self.stdout.write(
                    self.style.WARNING(f"Package {package_name} not found.")
                )
                continue

            if "Shared Package" in package_name or package_option.package.shared:
                shared_package, created = SharedPackage.objects.get_or_create(
                    package_option=package_option,
                    defaults={
                        "total_time": package_option.time,
                        "remaining_time": remaining_time,
                    },
                )
                if created:
                    self.stdout.write(
                        self.style.SUCCESS(f"Created Shared Package {package_name}")
                    )

                shared_package_user, created = SharedPackageUser.objects.get_or_create(
                    shared_package=shared_package,
                    user=user,
                )
                if created:
                    self.stdout.write(
                        self.style.SUCCESS(
                            f"Added {customer_name} to Shared Package {package_name}"
                        )
                    )
            else:
                user_package, created = UserPackage.objects.get_or_create(
                    user=user,
                    package_option=package_option,
                    defaults={
                        "total_time": package_option.time,
                        "remaining_time": remaining_time,
                        "active": remaining_time > 0,
                    },
                )

                if not created:
                    user_package.remaining_time = remaining_time
                    user_package.active = remaining_time > 0
                    user_package.save()

                self.stdout.write(
                    self.style.SUCCESS(
                        f"Created/Updated UserPackage for {customer_name} - {package_name}"
                    )
                )
