from rest_framework import serializers
from .models import (
    CommissionProfile, CommissionRule, ManualCommission,
    CommissionEarning, TherapistStats, TherapistYearStats, TherapistMonthStats
)
from api.staff.models import TherapistProfile
from api.services.models import Service, ServicePackage
from api.appointments.models import Sale


class CommissionProfileSerializer(serializers.ModelSerializer):
    therapist_name = serializers.SerializerMethodField()

    class Meta:
        model = CommissionProfile
        fields = [
            'id', 'therapist', 'therapist_name', 'name',
            'base_percentage', 'sessions_threshold',
            'is_active', 'is_default', 'created_at', 'updated_at'
        ]

    def get_therapist_name(self, obj):
        # Handle case where obj might be validated_data (OrderedDict) during creation
        if isinstance(obj, dict):
            therapist = obj.get('therapist')
            if therapist:
                return therapist.user.get_full_name()
            return None
        # Normal case where obj is a CommissionProfile instance
        return obj.therapist.user.get_full_name()


class CommissionRuleSerializer(serializers.ModelSerializer):
    profile_name = serializers.SerializerMethodField()
    service_name = serializers.SerializerMethodField()
    package_name = serializers.SerializerMethodField()

    class Meta:
        model = CommissionRule
        fields = [
            'id', 'profile', 'profile_name', 'name', 'rule_type',
            'service', 'service_name', 'package', 'package_name',
            'percentage', 'fixed_amount', 'min_sessions',
            'priority', 'is_active', 'created_at', 'updated_at'
        ]

    def get_profile_name(self, obj):
        if obj.profile:
            return obj.profile.name
        return 'Global Rule'

    def get_service_name(self, obj):
        if obj.service:
            return obj.service.name
        return None

    def get_package_name(self, obj):
        if obj.package:
            return obj.package.name
        return None

    def validate(self, data):
        # Validate rule type and related objects
        rule_type = data.get('rule_type')
        service = data.get('service')
        package = data.get('package')

        if rule_type == 'service' and not service:
            raise serializers.ValidationError(
                {"service": "Service must be specified for service rule type"}
            )

        if rule_type == 'package' and not package:
            raise serializers.ValidationError(
                {"package": "Package must be specified for package rule type"}
            )

        # Validate that either percentage or fixed_amount is provided
        # Only check this for creation or when these fields are being updated
        percentage = data.get('percentage')
        fixed_amount = data.get('fixed_amount')

        # For updates, check if we have an instance and if percentage/fixed_amount fields are in the data
        if self.instance is None:  # Creating new rule
            if percentage is None and fixed_amount is None:
                raise serializers.ValidationError(
                    "Either percentage or fixed amount must be specified"
                )
        else:  # Updating existing rule
            # Only validate if percentage or fixed_amount are being updated
            if ('percentage' in data or 'fixed_amount' in data):
                # If updating these fields, ensure at least one is provided
                if percentage is None and fixed_amount is None:
                    raise serializers.ValidationError(
                        "Either percentage or fixed amount must be specified"
                    )

        return data


class ManualCommissionSerializer(serializers.ModelSerializer):
    therapist_name = serializers.SerializerMethodField()
    sale_info = serializers.SerializerMethodField()
    created_by_name = serializers.SerializerMethodField()

    class Meta:
        model = ManualCommission
        fields = [
            'id', 'sale', 'sale_info', 'therapist', 'therapist_name',
            'amount', 'percentage', 'notes', 'created_by',
            'created_by_name', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_by', 'created_by_name']

    def get_therapist_name(self, obj):
        return obj.therapist.user.get_full_name()

    def get_sale_info(self, obj):
        if obj.sale:
            return {
                'id': obj.sale.id,
                'total_price': obj.sale.total_price,
                'sale_type': obj.sale.sale_type,
                'date': obj.sale.created_at.strftime('%Y-%m-%d'),
                'customer': obj.sale.user.get_full_name() if obj.sale.user else None
            }
        return None

    def get_created_by_name(self, obj):
        if obj.created_by:
            return obj.created_by.get_full_name()
        return None

    def validate(self, data):
        # Validate that either amount or percentage is provided
        amount = data.get('amount')
        percentage = data.get('percentage')

        if amount is None and percentage is None:
            raise serializers.ValidationError(
                "Either amount or percentage must be specified"
            )

        return data


class CommissionEarningSerializer(serializers.ModelSerializer):
    therapist_name = serializers.SerializerMethodField()
    sale_info = serializers.SerializerMethodField()
    rule_info = serializers.SerializerMethodField()
    month_stat_info = serializers.SerializerMethodField()

    class Meta:
        model = CommissionEarning
        fields = [
            'id', 'therapist', 'therapist_name', 'sale', 'sale_info',
            'amount', 'percentage_used', 'commission_rule', 'rule_info',
            'manual_commission', 'date_earned', 'is_paid', 'payment_date',
            'is_eligible', 'month_stat', 'month_stat_info',
            'notes', 'created_at', 'updated_at'
        ]
        read_only_fields = ['therapist_name', 'sale_info', 'rule_info', 'month_stat_info']

    def get_therapist_name(self, obj):
        return obj.therapist.user.get_full_name()

    def get_sale_info(self, obj):
        if obj.sale:
            return {
                'id': obj.sale.id,
                'total_price': obj.sale.total_price,
                'sale_type': obj.sale.sale_type,
                'date': obj.sale.created_at.strftime('%Y-%m-%d'),
                'customer': obj.sale.user.get_full_name() if obj.sale.user else None
            }
        return None

    def get_rule_info(self, obj):
        if obj.commission_rule:
            return {
                'id': obj.commission_rule.id,
                'name': obj.commission_rule.name,
                'rule_type': obj.commission_rule.rule_type
            }
        return None

    def get_month_stat_info(self, obj):
        if obj.month_stat:
            return {
                'id': obj.month_stat.id,
                'year': obj.month_stat.year,
                'month': obj.month_stat.month,
                'month_name': obj.month_stat.month_name,
                'total_sessions': obj.month_stat.total_sessions,
                'total_earnings': obj.month_stat.total_earnings,
                'is_paid': obj.month_stat.is_paid
            }
        return None


class TherapistStatsSerializer(serializers.ModelSerializer):
    therapist_name = serializers.SerializerMethodField()

    class Meta:
        model = TherapistStats
        fields = [
            'id', 'therapist', 'therapist_name', 'total_sessions',
            'total_earnings', 'last_updated'
        ]

    def get_therapist_name(self, obj):
        return obj.therapist.user.get_full_name()


class TherapistYearStatsSerializer(serializers.ModelSerializer):
    therapist_name = serializers.SerializerMethodField()

    class Meta:
        model = TherapistYearStats
        fields = [
            'id', 'therapist', 'therapist_name', 'year',
            'total_sessions', 'total_earnings', 'last_updated'
        ]

    def get_therapist_name(self, obj):
        return obj.therapist.user.get_full_name()


class TherapistMonthStatsSerializer(serializers.ModelSerializer):
    therapist_name = serializers.SerializerMethodField()
    month_name = serializers.ReadOnlyField()
    start_date = serializers.ReadOnlyField()
    end_date = serializers.ReadOnlyField()
    commissions_count = serializers.SerializerMethodField()

    class Meta:
        model = TherapistMonthStats
        fields = [
            'id', 'therapist', 'therapist_name', 'year', 'month',
            'month_name', 'start_date', 'end_date',
            'total_sessions', 'total_earnings', 'is_paid',
            'payment_date', 'commissions_count', 'last_updated'
        ]

    def get_therapist_name(self, obj):
        return obj.therapist.user.get_full_name()

    def get_commissions_count(self, obj):
        return obj.commissions.count()
