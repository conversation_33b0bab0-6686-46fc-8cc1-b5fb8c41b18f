from .base import *


DEBUG = env.bool("DEBUG", default=True)

ALLOWED_HOSTS = env.list("ALLOWED_HOSTS", default=["localhost", "127.0.0.1"])
CSRF_TRUSTED_ORIGINS = env.list(
    "CSRF_TRUSTED_ORIGINS", default=["http://127.0.0.1", "http://localhost"]
)

# CORS_ALLOWED_ORIGINS = [
#     "http://localhost:3000",
#     "http://localhost:5173",
#     "https://stretchup.alexander-til.de",
#     "https://app-stretchup.alexander-til.de",
#     # Add more origins if needed
# ]
ALLOWED_HOSTS = ["*"]
CORS_ALLOW_ALL_ORIGINS = True

# INSTALLED_APPS += ['debug_toolbar']
# MIDDLEWARE.insert(0, 'debug_toolbar.middleware.DebugToolbarMiddleware')


DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql",
        "NAME": env("DB_NAME", default="postgres"),
        "USER": env("DB_USER", default="postgres"),
        "PASSWORD": env("DB_PASSWORD", default=""),
        "HOST": env("DB_HOST", default="db"),
        "PORT": env("DB_PORT", default="5432"),
        "TEST": {
            "NAME": "postgres_test",  # Test database name
            "USER": "postgres",
            "PASSWORD": "password",
            "HOST": "localhost",  # Assuming Docker is accessible via localhost
            "PORT": "5433",  # Port for the test database
        },
    },
    # 'test': {
    #     'ENGINE': 'django.db.backends.postgresql',
    #     'NAME': 'postgres_test',
    #     'USER': 'postgres',
    #     'PASSWORD': 'password',
    #     'HOST': 'localhost',  # Assuming Docker is accessible via localhost
    #     'PORT': '5433',  # Port for the test database
    # }
}


# ==============================================================================
# EMAIL SETTINGS
# ==============================================================================
