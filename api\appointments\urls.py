from django.urls import path, include
from rest_framework.routers import Default<PERSON>out<PERSON>
from .views import (
    ActivePackage,
    ActiveSharedPackage,
    ActiveUnlimitedPackage,
    AppointmentListCreateView,
    SplitAppointmentView,
    AppointmentDayView,
    AppointmentManageView,
    AvailableTimesForPackagesView,
    AvailableTimesForServicesView,
    CustomerAllPackagesView,
    CustomerAllSharedPackagesView,
    CustomerAllUnlimitedPackagesView,
    CustomerActivePackageView,
    CustomerListView,
    CustomerView,
    AppointmentSharedPackageView,
    AppointmentUnlimitedPackageView,
    CustomerActiveSharedPackageView,
    CustomerActiveUnlimitedPackageView,
    AddMinutesToAppointmentView,
    DeleteAdditionalServiceView,
    adjust_appointment_minutes,
    UserPackageDetailView,
    user_package_usage,
    SharedPackageDetailView,
    shared_package_usage,
    SharedPackageUsersView,
    SharedPackageUserRemoveView,
    UnlimitedPackageDetailView,
    unlimited_package_usage,
    PackageUpdateExpiryView,
    PackageDeleteView,
    CustomerProfileView,
    CustomerAppointmentsView,
    TherapistProfileView,
    TherapistAppointmentsView,
    ApplyDiscountView,
    AvailableDiscountCodesView,
    PackageUpdateRemainingTimeView,
    DirectPackageSaleView,
    SaleListView,
    SaleDetailView,
    SaleViewSet,
    AppointmentMobileTodayView,
    AppointmentMobileNextView,
    AppointmentMobilePastView,
)

# Create a router for SaleViewSet
router = DefaultRouter()
router.register(r"sales/management", SaleViewSet, basename="sale-management")


urlpatterns = [
    path(
        "sales/direct-package/",
        DirectPackageSaleView.as_view(),
        name="direct-package-sale",
    ),
    path("sales/", SaleListView.as_view(), name="sale-list"),
    path("sales/<int:pk>/", SaleDetailView.as_view(), name="sale-detail"),
    path("active-package/", ActivePackage.as_view(), name="active-package"),
    path(
        "active-shared-package/",
        ActiveSharedPackage.as_view(),
        name="active-shared-package",
    ),
    path(
        "active-unlimited-package/",
        ActiveUnlimitedPackage.as_view(),
        name="active-unlimited-package",
    ),
    path("appointments/", AppointmentListCreateView.as_view(), name="appointments"),
    path("appointments-split/", SplitAppointmentView.as_view(), name="appointment-split"),
    path(
        "appointments/<int:appointment_id>/apply-discount/",
        ApplyDiscountView.as_view(),
        name="apply-discount",
    ),
    path(
        "discounts/",
        AvailableDiscountCodesView.as_view(),
        name="available-discount-codes",
    ),
    path(
        "appointments/shared/",
        AppointmentSharedPackageView.as_view(),
        name="appointments-shared",
    ),
    path(
        "appointments/unlimited/",
        AppointmentUnlimitedPackageView.as_view(),
        name="appointments-unlimited",
    ),
    path(
        "appointments/add-minutes/",
        AddMinutesToAppointmentView.as_view(),
        name="add-minutes-to-appointment",
    ),
    path(
        "appointments/additional-service/<int:service_id>/",
        DeleteAdditionalServiceView.as_view(),
        name="delete-additional-service",
    ),
    path("appointments/day/", AppointmentDayView.as_view(), name="appointments_day"),
    path(
        "appointments/manage/<int:pk>/",
        AppointmentManageView.as_view(),
        name="appointments_manage",
    ),
    path(
        "appointments/<int:appointment_id>/adjust-minutes/",
        adjust_appointment_minutes,
        name="adjust-appointment-minutes",
    ),
    path(
        "available-times/packages/",
        AvailableTimesForPackagesView.as_view(),
        name="available_times_packages",
    ),
    path(
        "available-times/services/",
        AvailableTimesForServicesView.as_view(),
        name="available_times_services",
    ),
    path(
        "customers/<int:customer_id>/packages/",
        CustomerAllPackagesView.as_view(),
        name="customer-all-packages",
    ),
    path(
        "customers/<int:customer_id>/shared-packages/",
        CustomerAllSharedPackagesView.as_view(),
        name="customer-all-shared-packages",
    ),
    path(
        "customers/<int:customer_id>/unlimited-packages/",
        CustomerAllUnlimitedPackagesView.as_view(),
        name="customer-all-unlimited-packages",
    ),
    path(
        "customers/<int:customer_id>/active-package/",
        CustomerActivePackageView.as_view(),
        name="customer-active-package",
    ),
    path(
        "customers/<int:customer_id>/active-shared-package/",
        CustomerActiveSharedPackageView.as_view(),
        name="customer-active-shared-package",
    ),
    path(
        "customers/<int:customer_id>/active-unlimited-package/",
        CustomerActiveUnlimitedPackageView.as_view(),
        name="customer-active-unlimited-package",
    ),
    path("customers/", CustomerListView.as_view(), name="customers"),
    path(
        "customer_add/",
        CustomerView.as_view(),
        name="customer_add",
    ),
    path(
        "customer/<int:customer_id>/",
        CustomerView.as_view(),
        name="customer_update",
    ),
    # User Package URLs
    path(
        "packages/user/<int:pk>/",
        UserPackageDetailView.as_view(),
        name="user-package-detail",
    ),
    path("packages/user/<int:pk>/usage/", user_package_usage, name="user-package-usage"),
    # Shared Package URLs
    path(
        "packages/shared/<int:pk>/",
        SharedPackageDetailView.as_view(),
        name="shared-package-detail",
    ),
    path(
        "packages/shared/<int:pk>/usage/",
        shared_package_usage,
        name="shared-package-usage",
    ),
    path(
        "packages/shared/<int:pk>/users/",
        SharedPackageUsersView.as_view(),
        name="shared-package-users",
    ),
    path(
        "packages/shared/<int:pk>/users/<int:user_id>/",
        SharedPackageUserRemoveView.as_view(),
        name="shared-package-user-remove",
    ),
    # Unlimited Package URLs
    path(
        "packages/unlimited/<int:pk>/",
        UnlimitedPackageDetailView.as_view(),
        name="unlimited-package-detail",
    ),
    path(
        "packages/unlimited/<int:pk>/usage/",
        unlimited_package_usage,
        name="unlimited-package-usage",
    ),
    path(
        "packages/<str:package_type>/<int:pk>/update-expiry/",
        PackageUpdateExpiryView.as_view(),
        name="package-update-expiry",
    ),
    path(
        "packages/<str:package_type>/<int:pk>/update-remaining-time/",
        PackageUpdateRemainingTimeView.as_view(),
        name="package-update-remaining-time",
    ),
    path(
        "packages/<str:package_type>/<int:pk>/delete/",
        PackageDeleteView.as_view(),
        name="package-delete",
    ),
    path(
        "customers/<int:customer_id>/profile/",
        CustomerProfileView.as_view(),
        name="customer-profile",
    ),
    # Appointments with date range filter and export
    path(
        "appointments-custom/",
        CustomerAppointmentsView.as_view(),
        name="appointments-customer-list",
    ),
    # Customer-specific appointments
    path(
        "customers/<int:customer_id>/appointments/",
        CustomerAppointmentsView.as_view(),
        name="customer-appointments",
    ),
    # Therapist profile endpoint
    path(
        "therapists/<int:therapist_id>/profile/",
        TherapistProfileView.as_view(),
        name="therapist-profile",
    ),
    # Therapist appointments endpoint
    path(
        "therapists/<int:therapist_id>/appointments/",
        TherapistAppointmentsView.as_view(),
        name="therapist-appointments",
    ),
    path(
        "appointments-mobile/today/",
        AppointmentMobileTodayView.as_view(),
        name="appointments-mobile-today",
    ),
    path(
        "appointments-mobile/next/",
        AppointmentMobileNextView.as_view(),
        name="appointments-mobile-next",
    ),
    path(
        "appointments-mobile/past/",
        AppointmentMobilePastView.as_view(),
        name="appointments-mobile-past",
    ),
    path("", include(router.urls)),
]
