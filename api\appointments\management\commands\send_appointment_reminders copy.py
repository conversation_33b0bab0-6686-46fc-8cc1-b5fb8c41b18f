# management/commands/send_appointment_reminders.py
# Place this file in: your_app/management/commands/send_appointment_reminders.py

from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import datetime, timedelta
from api.appointments.models import Appointment
from api.utils.push_notifications import send_appointment_reminder_notification
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Send push notification reminders for appointments scheduled 24 hours from now'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be sent without actually sending notifications',
        )
        parser.add_argument(
            '--hours',
            type=int,
            default=24,
            help='Number of hours in advance to send reminders (default: 24)',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        hours_ahead = options['hours']
        
        # Calculate the target date (24 hours from now, or custom hours)
        target_datetime = timezone.now() + timedelta(hours=hours_ahead)
        target_date = target_datetime.date()
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Looking for appointments on {target_date} ({hours_ahead} hours from now)...'
            )
        )
        
        # Find appointments for tomorrow that haven't had reminders sent
        appointments = Appointment.objects.filter(
            date=target_date,
            status='booked',  # Only send reminders for booked appointments
            reminder_sent=False,  # Only appointments without reminders
            customer__expo_push_token__isnull=False,  # Only customers with push tokens
        ).exclude(
            customer__expo_push_token=''  # Exclude empty push tokens
        ).select_related('customer', 'therapist__user')
        
        if not appointments.exists():
            self.stdout.write(
                self.style.WARNING(
                    f'No appointments found for {target_date} that need reminders.'
                )
            )
            return
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Found {appointments.count()} appointment(s) to send reminders for:'
            )
        )
        
        success_count = 0
        error_count = 0
        
        for appointment in appointments:
            customer_name = appointment.customer.get_full_name() or appointment.customer.email
            therapist_name = appointment.therapist.user.get_full_name() or appointment.therapist.user.email
            
            self.stdout.write(
                f'  - {customer_name} at {appointment.time.strftime("%I:%M %p")} with {therapist_name}'
            )
            
            if not dry_run:
                try:
                    # Send the notification
                    send_appointment_reminder_notification(appointment.customer, appointment)
                    success_count += 1
                    
                    self.stdout.write(
                        self.style.SUCCESS(
                            f'    ✅ Reminder sent to {customer_name}'
                        )
                    )
                    
                except Exception as e:
                    error_count += 1
                    self.stdout.write(
                        self.style.ERROR(
                            f'    ❌ Failed to send reminder to {customer_name}: {str(e)}'
                        )
                    )
                    logger.error(f'Failed to send reminder for appointment {appointment.id}: {str(e)}')
            else:
                self.stdout.write(
                    self.style.WARNING(
                        f'    📝 Would send reminder to {customer_name} (DRY RUN)'
                    )
                )
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING(
                    f'\n🔍 DRY RUN COMPLETE: Would have sent {appointments.count()} reminder(s)'
                )
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(
                    f'\n📱 REMINDERS SENT: {success_count} successful, {error_count} failed'
                )
            )
            
            if error_count > 0:
                self.stdout.write(
                    self.style.WARNING(
                        f'⚠️  Check logs for details about failed notifications'
                    )
                )


# Additional command for testing notifications
class Command(BaseCommand):
    help = 'Send push notification reminders for appointments scheduled in the specified time frame'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be sent without actually sending notifications',
        )
        parser.add_argument(
            '--hours',
            type=int,
            default=24,
            help='Number of hours in advance to send reminders (default: 24)',
        )
        parser.add_argument(
            '--test',
            action='store_true',
            help='Send test notifications to all users with push tokens',
        )
        parser.add_argument(
            '--user-email',
            type=str,
            help='Send test notification to specific user email',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        hours_ahead = options['hours']
        test_mode = options['test']
        user_email = options['user_email']
        
        if test_mode:
            self.handle_test_notifications(dry_run, user_email)
            return
            
        # Calculate the target date (24 hours from now, or custom hours)
        target_datetime = timezone.now() + timedelta(hours=hours_ahead)
        target_date = target_datetime.date()
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Looking for appointments on {target_date} ({hours_ahead} hours from now)...'
            )
        )
        
        # Find appointments for the target date that haven't had reminders sent
        appointments = Appointment.objects.filter(
            date=target_date,
            status='booked',  # Only send reminders for booked appointments
            reminder_sent=False,  # Only appointments without reminders
            customer__expo_push_token__isnull=False,  # Only customers with push tokens
        ).exclude(
            customer__expo_push_token=''  # Exclude empty push tokens
        ).select_related('customer', 'therapist__user')
        
        if not appointments.exists():
            self.stdout.write(
                self.style.WARNING(
                    f'No appointments found for {target_date} that need reminders.'
                )
            )
            return
        
        self.stdout.write(
            self.style.SUCCESS(
                f'Found {appointments.count()} appointment(s) to send reminders for:'
            )
        )
        
        success_count = 0
        error_count = 0
        
        for appointment in appointments:
            customer_name = appointment.customer.get_full_name() or appointment.customer.email
            therapist_name = appointment.therapist.user.get_full_name() or appointment.therapist.user.email
            
            self.stdout.write(
                f'  - {customer_name} at {appointment.time.strftime("%I:%M %p")} with {therapist_name}'
            )
            
            if not dry_run:
                try:
                    # Send the notification
                    send_appointment_reminder_notification(appointment.customer, appointment)
                    success_count += 1
                    
                    self.stdout.write(
                        self.style.SUCCESS(
                            f'    ✅ Reminder sent to {customer_name}'
                        )
                    )
                    
                except Exception as e:
                    error_count += 1
                    self.stdout.write(
                        self.style.ERROR(
                            f'    ❌ Failed to send reminder to {customer_name}: {str(e)}'
                        )
                    )
                    logger.error(f'Failed to send reminder for appointment {appointment.id}: {str(e)}')
            else:
                self.stdout.write(
                    self.style.WARNING(
                        f'    📝 Would send reminder to {customer_name} (DRY RUN)'
                    )
                )
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING(
                    f'\n🔍 DRY RUN COMPLETE: Would have sent {appointments.count()} reminder(s)'
                )
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(
                    f'\n📱 REMINDERS SENT: {success_count} successful, {error_count} failed'
                )
            )
            
            if error_count > 0:
                self.stdout.write(
                    self.style.WARNING(
                        f'⚠️  Check logs for details about failed notifications'
                    )
                )

    def handle_test_notifications(self, dry_run, user_email):
        """Handle test notification sending"""
        from django.contrib.auth import get_user_model
        from api.utils.push_notifications import send_push_notification
        
        User = get_user_model()
        
        if user_email:
            # Send to specific user
            try:
                user = User.objects.get(email=user_email)
                if not user.expo_push_token:
                    self.stdout.write(
                        self.style.ERROR(
                            f'❌ User {user_email} does not have an Expo push token'
                        )
                    )
                    return
                
                if not dry_run:
                    send_push_notification(
                        expo_push_token=user.expo_push_token,
                        title="Test Notification",
                        body=f"Hello {user.get_full_name()}! This is a test notification from your appointment system.",
                        data={"test": True}
                    )
                    self.stdout.write(
                        self.style.SUCCESS(
                            f'✅ Test notification sent to {user_email}'
                        )
                    )
                else:
                    self.stdout.write(
                        self.style.WARNING(
                            f'📝 Would send test notification to {user_email} (DRY RUN)'
                        )
                    )
                    
            except User.DoesNotExist:
                self.stdout.write(
                    self.style.ERROR(
                        f'❌ User with email {user_email} not found'
                    )
                )
        else:
            # Send to all users with push tokens
            users_with_tokens = User.objects.filter(
                expo_push_token__isnull=False
            ).exclude(expo_push_token='')
            
            if not users_with_tokens.exists():
                self.stdout.write(
                    self.style.WARNING(
                        'No users found with Expo push tokens'
                    )
                )
                return
            
            self.stdout.write(
                self.style.SUCCESS(
                    f'Found {users_with_tokens.count()} user(s) with push tokens'
                )
            )
            
            success_count = 0
            error_count = 0
            
            for user in users_with_tokens:
                if not dry_run:
                    try:
                        send_push_notification(
                            expo_push_token=user.expo_push_token,
                            title="Test Notification",
                            body=f"Hello {user.get_full_name()}! This is a test notification from your appointment system.",
                            data={"test": True}
                        )
                        success_count += 1
                        self.stdout.write(
                            self.style.SUCCESS(
                                f'✅ Test notification sent to {user.email}'
                            )
                        )
                    except Exception as e:
                        error_count += 1
                        self.stdout.write(
                            self.style.ERROR(
                                f'❌ Failed to send test notification to {user.email}: {str(e)}'
                            )
                        )
                else:
                    self.stdout.write(
                        self.style.WARNING(
                            f'📝 Would send test notification to {user.email} (DRY RUN)'
                        )
                    )
            
            if not dry_run:
                self.stdout.write(
                    self.style.SUCCESS(
                        f'\n📱 TEST NOTIFICATIONS: {success_count} successful, {error_count} failed'
                    )
                )
            else:
                self.stdout.write(
                    self.style.WARNING(
                        f'\n🔍 DRY RUN: Would have sent {users_with_tokens.count()} test notifications'
                    )
                )