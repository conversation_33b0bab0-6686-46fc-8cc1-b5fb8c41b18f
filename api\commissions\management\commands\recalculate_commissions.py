from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from django.utils import timezone
from datetime import datetime, date
from decimal import Decimal
import logging

from api.appointments.models import Sale
from api.retail.models import ProductSale
from api.commissions.models import (
    CommissionEarning, TherapistMonthStats, TherapistYearStats
)
from api.commissions.services import CommissionCalculator
from api.staff.models import TherapistProfile


class Command(BaseCommand):
    help = 'Recalculate commissions for sales within a specified date range'

    def add_arguments(self, parser):
        parser.add_argument(
            '--start-date',
            type=str,
            required=True,
            help='Start date in YYYY-MM-DD format'
        )
        parser.add_argument(
            '--end-date',
            type=str,
            required=True,
            help='End date in YYYY-MM-DD format'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without making changes'
        )
        parser.add_argument(
            '--verbose',
            action='store_true',
            help='Show detailed progress information'
        )
        parser.add_argument(
            '--missing-only',
            action='store_true',
            help='Only calculate commissions for sales that have no existing commissions'
        )
        parser.add_argument(
            '--force-recalculate',
            action='store_true',
            help='Force recalculation even for sales that already have commissions'
        )
        parser.add_argument(
            '--auto',
            action='store_true',
            help='Automatically handle both missing commissions and outdated commissions (recommended)'
        )

    def handle(self, *args, **options):
        # Set up logging
        logging.basicConfig(level=logging.INFO)
        logger = logging.getLogger('commission_recalculation')

        # Parse and validate dates
        try:
            start_date = datetime.strptime(options['start_date'], '%Y-%m-%d').date()
            end_date = datetime.strptime(options['end_date'], '%Y-%m-%d').date()
        except ValueError as e:
            raise CommandError(f'Invalid date format: {e}')

        if start_date > end_date:
            raise CommandError('Start date must be before or equal to end date')

        if end_date > timezone.now().date():
            raise CommandError('End date cannot be in the future')

        dry_run = options.get('dry_run', False)
        verbose = options.get('verbose', False)
        missing_only = options.get('missing_only', False)
        force_recalculate = options.get('force_recalculate', False)
        auto_mode = options.get('auto', False)

        # Determine operation mode
        mode_count = sum([missing_only, force_recalculate, auto_mode])
        if mode_count > 1:
            raise CommandError('Cannot use multiple mode options together. Choose one: --missing-only, --force-recalculate, or --auto')

        if auto_mode:
            operation_mode = "AUTO MODE (SMART RECALCULATION)"
        elif missing_only:
            operation_mode = "MISSING COMMISSIONS ONLY"
        elif force_recalculate:
            operation_mode = "FORCE RECALCULATE ALL"
        else:
            operation_mode = "STANDARD RECALCULATION"

        self.stdout.write(
            self.style.SUCCESS(
                f'Commission Recalculation Script\n'
                f'Date Range: {start_date} to {end_date}\n'
                f'Mode: {"DRY RUN" if dry_run else "LIVE"}\n'
                f'Operation: {operation_mode}\n'
                f'{"=" * 50}'
            )
        )

        # Get sales within date range
        appointment_sales = Sale.objects.filter(
            created_at__date__range=[start_date, end_date]
        ).select_related('user', 'appointment', 'user_package').order_by('created_at')

        product_sales = ProductSale.objects.filter(
            created_at__date__range=[start_date, end_date],
            status='COMPLETED'
        ).select_related('customer', 'staff').order_by('created_at')

        # Filter based on operation mode
        if auto_mode:
            # Auto mode: intelligently handle both missing and outdated commissions
            self.stdout.write('🤖 Auto mode: Analyzing commission status...')

            # Get sales with and without commissions
            sales_with_commissions = CommissionEarning.objects.filter(
                sale__in=appointment_sales
            ).values_list('sale_id', flat=True)

            sales_without_commissions = appointment_sales.exclude(id__in=sales_with_commissions)
            sales_with_existing_commissions = appointment_sales.filter(id__in=sales_with_commissions)

            missing_count = sales_without_commissions.count()
            existing_count = sales_with_existing_commissions.count()

            self.stdout.write(f'   📊 Found {missing_count} sales without commissions')
            self.stdout.write(f'   📊 Found {existing_count} sales with existing commissions')

            if missing_count > 0 and existing_count > 0:
                self.stdout.write('   🔄 Will calculate missing commissions AND recalculate existing ones')
            elif missing_count > 0:
                self.stdout.write('   ➕ Will calculate missing commissions only')
            elif existing_count > 0:
                self.stdout.write('   🔄 Will recalculate existing commissions only')
            else:
                self.stdout.write('   ℹ️ No commissions to process')

        elif missing_only:
            # Only include sales that have no existing commissions
            sales_with_commissions = CommissionEarning.objects.filter(
                sale__in=appointment_sales
            ).values_list('sale_id', flat=True)
            appointment_sales = appointment_sales.exclude(id__in=sales_with_commissions)

            self.stdout.write(f'Filtering to sales with missing commissions only...')
        elif not force_recalculate:
            # Standard mode: warn about existing commissions but proceed
            existing_commission_count = CommissionEarning.objects.filter(
                sale__in=appointment_sales
            ).count()
            if existing_commission_count > 0:
                self.stdout.write(
                    self.style.WARNING(
                        f'Found {existing_commission_count} existing commission records that will be deleted and recalculated'
                    )
                )

        total_appointment_sales = appointment_sales.count()
        total_product_sales = product_sales.count()
        total_sales = total_appointment_sales + total_product_sales

        self.stdout.write(
            f'Found {total_appointment_sales} appointment/package sales\n'
            f'Found {total_product_sales} product sales\n'
            f'Total sales to process: {total_sales}'
        )

        if total_sales == 0:
            self.stdout.write(self.style.WARNING('No sales found in the specified date range'))
            return

        if not dry_run:
            # Confirm before proceeding
            confirm = input(f'\nThis will recalculate commissions for {total_sales} sales. Continue? (y/N): ')
            if confirm.lower() != 'y':
                self.stdout.write(self.style.WARNING('Operation cancelled'))
                return

        # Process the recalculation
        try:
            with transaction.atomic():
                if dry_run:
                    self.stdout.write(self.style.WARNING('\n--- DRY RUN MODE - No changes will be made ---'))

                # Step 1: Delete existing commission records (skip if missing-only mode)
                if auto_mode:
                    # Auto mode: always delete existing commissions to ensure clean recalculation
                    self._delete_existing_commissions(appointment_sales, product_sales, dry_run, verbose)
                elif not missing_only:
                    self._delete_existing_commissions(appointment_sales, product_sales, dry_run, verbose)
                else:
                    self.stdout.write('\n1. Skipping deletion (missing-only mode)')

                # Step 2: Recalculate commissions for appointment/package sales
                self._recalculate_appointment_commissions(appointment_sales, dry_run, verbose, missing_only or auto_mode)

                # Step 3: Handle product sales (if they have commission logic)
                self._handle_product_sales(product_sales, dry_run, verbose)

                # Step 4: Update therapist statistics
                self._update_therapist_statistics(start_date, end_date, dry_run, verbose)

                if dry_run:
                    # Rollback transaction in dry run mode
                    transaction.set_rollback(True)
                    self.stdout.write(self.style.SUCCESS('\n--- DRY RUN COMPLETED - No changes made ---'))
                else:
                    self.stdout.write(self.style.SUCCESS('\n--- COMMISSION RECALCULATION COMPLETED ---'))

        except Exception as e:
            logger.error(f'Error during commission recalculation: {e}')
            raise CommandError(f'Commission recalculation failed: {e}')

    def _delete_existing_commissions(self, appointment_sales, product_sales, dry_run, verbose):
        """Delete existing commission records for the sales in date range"""
        self.stdout.write('\n1. Deleting existing commission records...')

        # Get commission earnings for appointment sales
        appointment_sale_ids = list(appointment_sales.values_list('id', flat=True))
        appointment_commissions = CommissionEarning.objects.filter(sale_id__in=appointment_sale_ids)

        # Note: ProductSale commissions would need to be handled if they exist
        # For now, focusing on appointment/package sales

        commission_count = appointment_commissions.count()

        if verbose:
            self.stdout.write(f'   Found {commission_count} existing commission records to delete')

        if not dry_run and commission_count > 0:
            deleted_count, _ = appointment_commissions.delete()
            self.stdout.write(f'   Deleted {deleted_count} commission records')
        elif dry_run and commission_count > 0:
            self.stdout.write(f'   Would delete {commission_count} commission records')

        # Also clean up orphaned monthly stats that might have zero earnings
        # This will be recalculated in the statistics update step

    def _recalculate_appointment_commissions(self, sales, dry_run, verbose, smart_mode=False):
        """Recalculate commissions for appointment and package sales"""
        if smart_mode:
            self.stdout.write('\n2. Processing commissions for appointment/package sales (smart mode)...')
        else:
            self.stdout.write('\n2. Recalculating commissions for appointment/package sales...')

        processed_count = 0
        commission_count = 0
        error_count = 0

        for sale in sales:
            if verbose:
                self.stdout.write(f'   Processing Sale ID {sale.id} - {sale.sale_type} - {sale.total_price} AED')

            try:
                if not dry_run:
                    # Use the full calculate_for_sale method which handles everything
                    # including saving and updating therapist stats
                    CommissionCalculator.calculate_for_sale(sale)

                    # Count the commissions that were created
                    new_commissions = CommissionEarning.objects.filter(sale=sale)
                    sale_commission_count = new_commissions.count()
                    commission_count += sale_commission_count

                    if verbose:
                        if sale_commission_count > 0:
                            for commission in new_commissions:
                                self.stdout.write(
                                    f'     → {commission.therapist.user.get_full_name()}: '
                                    f'{commission.amount} AED ({commission.commission_type})'
                                )
                        else:
                            self.stdout.write(f'     → No commissions created (not eligible or no therapist)')
                else:
                    # In dry run, just simulate
                    commissions = CommissionCalculator.calculate_commissions(sale)
                    if verbose:
                        if commissions:
                            for commission in commissions:
                                if commission:
                                    self.stdout.write(
                                        f'     → Would create: {commission.therapist.user.get_full_name()}: '
                                        f'{commission.amount} AED ({commission.commission_type})'
                                    )
                        else:
                            self.stdout.write(f'     → Would create: No commissions (not eligible or no therapist)')

            except Exception as e:
                error_count += 1
                if verbose:
                    self.stdout.write(f'     → Error processing sale: {e}')
                else:
                    self.stdout.write(f'   Error processing Sale ID {sale.id}: {e}')

            processed_count += 1

            # Progress indicator for large datasets
            if processed_count % 100 == 0:
                self.stdout.write(f'   Processed {processed_count} sales...')

        self.stdout.write(f'   Processed {processed_count} appointment/package sales')
        if not dry_run:
            self.stdout.write(f'   Created {commission_count} commission records')
        if error_count > 0:
            self.stdout.write(self.style.WARNING(f'   {error_count} sales had errors during processing'))

    def _handle_product_sales(self, product_sales, dry_run, verbose):
        """Handle product sales - currently no commission logic implemented"""
        if product_sales.count() > 0:
            self.stdout.write('\n3. Handling product sales...')
            self.stdout.write(f'   Found {product_sales.count()} product sales')
            self.stdout.write('   Note: Product sales commission logic not implemented yet')

    def _update_therapist_statistics(self, start_date, end_date, dry_run, verbose):
        """Update therapist monthly and yearly statistics"""
        self.stdout.write('\n4. Updating therapist statistics...')

        # Get all therapists who might be affected by the recalculation
        # Include therapists who had sales in the date range (even if no commissions)
        therapists_with_commissions = TherapistProfile.objects.filter(
            commission_earnings__date_earned__range=[start_date, end_date]
        ).distinct()

        # Also include therapists who had appointments in the date range
        therapists_with_appointments = TherapistProfile.objects.filter(
            appointments__date__range=[start_date, end_date]
        ).distinct()

        # Combine both querysets
        all_affected_therapists = (therapists_with_commissions | therapists_with_appointments).distinct()

        if not all_affected_therapists.exists():
            self.stdout.write('   No therapists found with activity in date range')
            return

        updated_therapists = 0

        for therapist in all_affected_therapists:
            if verbose:
                self.stdout.write(f'   Updating stats for {therapist.user.get_full_name()}')

            if not dry_run:
                # Use existing method to update therapist statistics
                # This will recalculate all monthly and yearly stats
                CommissionCalculator._update_therapist_stats(therapist)

            updated_therapists += 1

        self.stdout.write(f'   Updated statistics for {updated_therapists} therapists')

        # Clean up any orphaned monthly stats with zero earnings
        if not dry_run:
            self._cleanup_empty_monthly_stats(start_date, end_date, verbose)

    def _cleanup_empty_monthly_stats(self, start_date, end_date, verbose):
        """Remove monthly stats records that have zero earnings and sessions"""
        if verbose:
            self.stdout.write('   Cleaning up empty monthly statistics...')

        # Find monthly stats in the date range with zero earnings and sessions
        empty_stats = TherapistMonthStats.objects.filter(
            year__gte=start_date.year,
            year__lte=end_date.year,
            total_earnings=Decimal('0.00'),
            total_sessions=0
        )

        # Further filter by month if needed
        if start_date.year == end_date.year:
            empty_stats = empty_stats.filter(
                month__gte=start_date.month,
                month__lte=end_date.month
            )

        deleted_count = empty_stats.count()
        if deleted_count > 0:
            empty_stats.delete()
            if verbose:
                self.stdout.write(f'   Removed {deleted_count} empty monthly stat records')
