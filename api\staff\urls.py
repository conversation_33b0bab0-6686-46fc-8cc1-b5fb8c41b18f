# urls.py - Update your existing URLs

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    DashboardActivityViewSet,
    DashboardAppointmentsViewSet,
    DashboardCustomerViewSet,
    DashboardRevenueViewSet,
    TherapistProfileViewSet,
    TherapistPublicProfileViewSet,
    WorkingHourViewSet,
    # Add new views
    TherapistManagementViewSet,
    WorkingHourManagementViewSet,
)

router = DefaultRouter()
router.register(r"therapists", TherapistProfileViewSet, basename="therapists")
router.register(r"therapists-p", TherapistPublicProfileViewSet, basename="therapists")
router.register(r"working-hours", WorkingHourViewSet)
router.register(
    r"dashboard/activities", DashboardActivityViewSet, basename="dashboard-activities"
)
router.register(
    r"dashboard/customers", DashboardCustomerViewSet, basename="dashboard-customers"
)
router.register(
    r"dashboard/appointments",
    DashboardAppointmentsViewSet,
    basename="dashboard-appointments",
)
router.register(
    r"dashboard/revenue", DashboardRevenueViewSet, basename="dashboard-revenue"
)
# Add new routes
router.register(
    r"manage/therapists", TherapistManagementViewSet, basename="manage-therapists"
)
router.register(
    r"manage/working-hours", WorkingHourManagementViewSet, basename="manage-working-hours"
)

urlpatterns = [
    path("", include(router.urls)),
]
