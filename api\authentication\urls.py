from django.urls import path, include
from api.authentication import views
from rest_framework_simplejwt.views import TokenRefreshView, TokenVerifyView

urlpatterns = [
    # User registration
    path("token/register/", views.RegisterUserView.as_view(), name="token_register"),
    # Generate OTP
    path(
        "token/generate-otp/", views.GenerateOTPView.as_view(), name="token_generate_otp"
    ),
    # Verify OTP and get JWT tokens
    path("token/", views.ObtainTokenPairView.as_view(), name="token_obtain_pair"),
    # JWT token refresh
    path("token/refresh/", TokenRefreshView.as_view(), name="token_refresh"),
    # JWT token verification
    path("token/verify/", TokenVerifyView.as_view(), name="token_verify"),
    path("users/me/", views.UserProfileView.as_view(), name="user_profile"),
    # path("customers/", views.UserCustomerView.as_view(), name="customers"),
    path("cache-tests/", views.cache_test_list, name="cache_test_list"),
    path("test/", views.TestXSSView.as_view(), name="test-xss"),
    # Check if email exists
    path("check-email/", views.EmailCheckView.as_view(), name="check_email"),
    path("deactivate/", views.DeactivateAccountView.as_view(), name="deactivate-account"),
    path(
        "save-expo-push-token/",
        views.SaveExpoPushTokenView.as_view(),
        name="save_expo_push_token",
    ),
    path("notify-user/", views.NotifyUserView.as_view(), name="notify_user"),
    path(
        "remove-expo-push-token/",
        views.RemoveExpoPushTokenView.as_view(),
        name="remove_expo_push_token",
    ),
    path(
        "test-notification/",
        views.TestNotificationView.as_view(),
        name="test_notification",
    ),
]
