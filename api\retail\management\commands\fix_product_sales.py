# File: api/retail/management/commands/generate_product_invoices.py

import time
from django.core.management.base import BaseCommand
from django.db import transaction
from api.retail.models import ProductSale
from django.utils import timezone


class Command(BaseCommand):
    help = "Generate PDF invoices for all ProductSale objects that do not have invoices"

    def add_arguments(self, parser):
        parser.add_argument(
            "--location",
            type=str,
            help="Filter by location (A or B)",
        )
        parser.add_argument(
            "--dry-run",
            action="store_true",
            help="Only show what would be done without making changes",
        )
        parser.add_argument(
            "--batch-size",
            type=int,
            default=100,
            help="Number of sales to process in each batch (default: 100)",
        )
        parser.add_argument(
            "--from-date",
            type=str,
            help="Process sales from this date (YYYY-MM-DD)",
        )
        parser.add_argument(
            "--to-date",
            type=str,
            help="Process sales up to this date (YYYY-MM-DD)",
        )

    def handle(self, *args, **options):
        location = options.get("location")
        dry_run = options.get("dry_run")
        batch_size = options.get("batch_size")
        from_date = options.get("from_date")
        to_date = options.get("to_date")

        # Build the queryset with all ProductSale objects that don't have invoices
        queryset = ProductSale.objects.filter()

        # Apply filters if provided
        if location:
            queryset = queryset.filter(location=location)
            self.stdout.write(f"Filtering by location: {location}")

        if from_date:
            try:
                from_datetime = timezone.datetime.strptime(from_date, "%Y-%m-%d")
                queryset = queryset.filter(created_at__gte=from_datetime)
                self.stdout.write(f"Filtering sales from: {from_date}")
            except ValueError:
                self.stderr.write(
                    self.style.ERROR(f"Invalid from-date format. Use YYYY-MM-DD")
                )
                return

        if to_date:
            try:
                to_datetime = timezone.datetime.strptime(to_date, "%Y-%m-%d")
                to_datetime = to_datetime.replace(hour=23, minute=59, second=59)
                queryset = queryset.filter(created_at__lte=to_datetime)
                self.stdout.write(f"Filtering sales until: {to_date}")
            except ValueError:
                self.stderr.write(
                    self.style.ERROR(f"Invalid to-date format. Use YYYY-MM-DD")
                )
                return

        # Get count of sales that will be processed
        total_sales = queryset.count()
        self.stdout.write(f"Found {total_sales} product sales without invoices")

        if dry_run:
            self.stdout.write(self.style.WARNING("DRY RUN - No changes will be made"))
            return

        if total_sales == 0:
            self.stdout.write(self.style.SUCCESS("No sales to process. All done!"))
            return

        # Process in batches to avoid memory issues with large datasets
        processed = 0
        successful = 0
        failed = 0

        self.stdout.write(f"Processing sales in batches of {batch_size}...")
        start_time = time.time()

        # Process in batches
        for i in range(0, total_sales, batch_size):
            batch = queryset[i : i + batch_size]
            self.stdout.write(
                f"Processing batch {i//batch_size + 1} ({len(batch)} sales)..."
            )

            for sale in batch:
                try:
                    with transaction.atomic():
                        # Check if sale has items
                        if not sale.sale_items.exists():
                            self.stdout.write(
                                self.style.WARNING(
                                    f"Skipping sale {sale.invoice_number} (ID: {sale.id}) - No sale items found"
                                )
                            )
                            failed += 1
                            continue

                        # Generate the invoice
                        result = sale.generate_invoice()
                        if result:
                            successful += 1
                            self.stdout.write(
                                f"Generated invoice for sale {sale.invoice_number} (ID: {sale.id})"
                            )
                        else:
                            failed += 1
                            self.stdout.write(
                                self.style.ERROR(
                                    f"Failed to generate invoice for sale {sale.invoice_number} (ID: {sale.id})"
                                )
                            )
                except Exception as e:
                    failed += 1
                    self.stdout.write(
                        self.style.ERROR(
                            f"Error for sale {sale.invoice_number} (ID: {sale.id}): {str(e)}"
                        )
                    )

                processed += 1

                # Show progress
                if processed % 10 == 0 or processed == total_sales:
                    elapsed_time = time.time() - start_time
                    progress = (processed / total_sales) * 100
                    self.stdout.write(
                        f"Progress: {processed}/{total_sales} ({progress:.1f}%) - Time elapsed: {elapsed_time:.1f}s"
                    )

        # Final summary
        elapsed_time = time.time() - start_time
        self.stdout.write(
            self.style.SUCCESS(
                f"Task completed in {elapsed_time:.1f} seconds.\n"
                f"Processed: {processed} sales\n"
                f"Successful: {successful}\n"
                f"Failed: {failed}"
            )
        )
