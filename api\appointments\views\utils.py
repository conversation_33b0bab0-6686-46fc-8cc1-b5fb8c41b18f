from api.appointments.models import Appointment, SharedPackageUser
from api.appointments.serializers import PackageUsageSerializer
from datetime import datetime

from django.template.loader import render_to_string
from django.utils.html import strip_tags
from django.core.mail import send_mail
from django.conf import settings

from django.contrib.auth import get_user_model

User = get_user_model()


class PackageViewMixin:
    """Base mixin for package views with common methods"""

    def get_package_usage(self, package, package_type):
        """Get usage history for a package"""
        if package_type == "user_package":
            appointments = Appointment.objects.filter(user_package=package).order_by(
                "-date", "-time"
            )
        elif package_type == "shared_package":
            appointments = Appointment.objects.filter(shared_package=package).order_by(
                "-date", "-time"
            )
        elif package_type == "unlimited_package":
            appointments = Appointment.objects.filter(unlimited_package=package).order_by(
                "-date", "-time"
            )
        else:
            return []

        # Use custom serializer that includes relevant appointment details
        return PackageUsageSerializer(appointments, many=True).data

    def check_package_access(self, package, request, package_type):
        """Check if user has access to view this package"""
        user = request.user

        # Admin users and receptionists can access all packages
        if user.role in ["owner", "receptionist"]:
            return True

        # For user packages, only the owner can access
        if package_type == "user_package" and package.user == user:
            return True

        # For shared packages, users who are part of the package can access
        if package_type == "shared_package":
            if SharedPackageUser.objects.filter(
                shared_package=package, user=user
            ).exists():
                return True

        # For unlimited packages, only the owner can access
        if package_type == "unlimited_package" and package.user == user:
            return True

        return False


# Helper function to send appointment confirmation emails
def send_appointment_confirmation_email(serialized_appointment):
    """
    Send a confirmation email to the customer for their appointment.
    """
    subject = f"Appointment Confirmation - StretchUp"

    # Format the main appointment date
    if "date" in serialized_appointment:
        try:
            date_obj = datetime.strptime(serialized_appointment["date"], "%Y-%m-%d")
            serialized_appointment["date"] = date_obj.strftime(
                "%B %d, %Y"
            )  # March 25, 2025
        except (ValueError, TypeError):
            pass

    # Format package expiry dates
    # User Package
    if (
        "user_package_obj" in serialized_appointment
        and serialized_appointment["user_package_obj"]
    ):
        if "expiry_date" in serialized_appointment["user_package_obj"]:
            try:
                date_str = serialized_appointment["user_package_obj"]["expiry_date"]
                date_obj = datetime.strptime(date_str, "%Y-%m-%d")
                serialized_appointment["user_package_obj"]["expiry_date"] = (
                    date_obj.strftime("%B %d, %Y")
                )
            except (ValueError, TypeError):
                pass

    # Shared Package
    if (
        "shared_package_obj" in serialized_appointment
        and serialized_appointment["shared_package_obj"]
    ):
        if "expiry_date" in serialized_appointment["shared_package_obj"]:
            try:
                date_str = serialized_appointment["shared_package_obj"]["expiry_date"]
                date_obj = datetime.strptime(date_str, "%Y-%m-%d")
                serialized_appointment["shared_package_obj"]["expiry_date"] = (
                    date_obj.strftime("%B %d, %Y")
                )
            except (ValueError, TypeError):
                pass

    # Unlimited Package
    if (
        "unlimited_package_obj" in serialized_appointment
        and serialized_appointment["unlimited_package_obj"]
    ):
        if "expiry_date" in serialized_appointment["unlimited_package_obj"]:
            try:
                date_str = serialized_appointment["unlimited_package_obj"]["expiry_date"]
                date_obj = datetime.strptime(date_str, "%Y-%m-%d")
                serialized_appointment["unlimited_package_obj"]["expiry_date"] = (
                    date_obj.strftime("%B %d, %Y")
                )
            except (ValueError, TypeError):
                pass

    # Prepare context for email template
    context = {"appointment": serialized_appointment}

    # Render HTML email template
    html_message = render_to_string("email/app_confirm.html", context)
    plain_message = strip_tags(html_message)

    # Get recipient email
    recipient_email = serialized_appointment["customer_obj"]["email"]
    # Check if in development/staging environment to redirect emails
    if settings.DEBUG or getattr(settings, "ENVIRONMENT", "production") in [
        "local",
        "staging",
    ]:
        # Optionally redirect emails in non-production environments
        recipient_email = settings.DEFAULT_FROM_EMAIL

    # Send the email
    send_mail(
        subject=subject,
        message=plain_message,
        from_email=settings.DEFAULT_FROM_EMAIL,
        recipient_list=["<EMAIL>"],
        # recipient_list=[recipient_email],
        html_message=html_message,
        fail_silently=False,
    )
