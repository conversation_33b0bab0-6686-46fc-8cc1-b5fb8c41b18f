# Generated by Django 4.0 on 2024-12-13 15:09

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Service',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='ServicePackage',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100)),
                ('description', models.TextField()),
                ('benefits', models.J<PERSON><PERSON>ield(help_text='List of benefits for the package')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('services_included', models.ManyToManyField(related_name='packages', to='services.Service')),
            ],
        ),
        migrations.CreateModel(
            name='ServiceDuration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('time', models.PositiveIntegerField(help_text='Duration in minutes')),
                ('price', models.DecimalField(decimal_places=2, max_digits=8)),
                ('service', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='durations', to='services.service')),
            ],
        ),
        migrations.CreateModel(
            name='PackageOption',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('time', models.PositiveIntegerField(help_text='Duration in minutes')),
                ('price', models.DecimalField(decimal_places=2, max_digits=8)),
                ('package', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='options', to='services.servicepackage')),
            ],
        ),
    ]
