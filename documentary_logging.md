# Using the Enhanced Logging System

## Quick Start

```python
# Import from utils
from utils.logging import api_logger, logger, log_request_data, log_response_data, log_error

# Basic information logging
api_logger.info("User accessed dashboard")

# Warning logging
api_logger.warning(f"Unusual activity detected for user: {user_id}")

# Error logging with context
try:
    # Your code here
except Exception as e:
    log_error(e, "Processing payment", log_full_trace=True)

# Request/response logging in views
def my_view(request):
    log_request_data(request, "Payment Request")
    # Process request...
    response = Response(...)
    log_response_data(response, "Payment Response")
    return response
```

## Available Loggers

- `logger` - Main Django application logger (logs to console and django.log)
- `api_logger` - API-specific logger (logs to console and api.log)
- `gemini_logger` - Integration logger (logs to console and gemini.log)

## Logging Levels

Use the appropriate level for different situations:

- `DEBUG` - Detailed information, typically of interest only when diagnosing problems
- `INFO` - Confirmation that things are working as expected
- `WARNING` - An indication that something unexpected happened, but the application still works
- `ERROR` - Due to a more serious problem, the software has not been able to perform a function
- `CRITICAL` - A serious error, indicating that the program itself may be unable to continue running

## Helper Functions

### `log_request_data(request, log_prefix="📥 Request")`

Safely logs incoming request data, sanitizing sensitive information.

```python
log_request_data(request, "User Registration")
```

### `log_response_data(response, log_prefix="📤 Response")`

Safely logs outgoing response data.

```python
log_response_data(response, "Authentication Response")
```

### `log_error(e, context="", log_full_trace=True)`

Records exceptions with proper context.

```python
try:
    # Complex operation
except ValidationError as ve:
    log_error(ve, "Email validation", log_full_trace=False)
except Exception as e:
    log_error(e, "User creation process")
```

### `sanitize_data(data, sensitive_fields=None)`

Redacts sensitive information from dictionaries or lists.

```python
safe_data = sanitize_data(user_data, ["password", "credit_card", "ssn"])
```

## Best Practices

1. **Use Descriptive Log Messages**

   - Include identifiers (IDs, names)
   - Add operation context

2. **Structure Logs with Emojis**

   - 📥 For incoming requests
   - 📤 For outgoing responses
   - ✅ For successful operations
   - ❌ For errors/failures
   - 🔄 For processing steps
   - 🔍 For inspection points

3. **Log Operation Boundaries**

   - Start/end of important processes
   - Entry/exit points for key functions

4. **Track Performance**

   - Log timestamps at key points
   - Record durations for slow operations

5. **Handle Errors Properly**

   - Always log exceptions
   - Include context about what operation failed
   - Don't expose sensitive information in error logs

6. **Add Metrics When Possible**
   - Count items processed
   - Record sizes/durations
   - Note resource usage

## Implementation in Different Contexts

### Models

```python
from utils.logging import logger

class MyModel(models.Model):
    def save(self, *args, **kwargs):
        try:
            logger.info(f"Saving {self.__class__.__name__} ID:{self.id or 'new'}")
            super().save(*args, **kwargs)
        except Exception as e:
            log_error(e, f"Saving {self.__class__.__name__}")
            raise
```

### Services

```python
from utils.logging import api_logger, log_error

def process_payment(order_id, amount):
    api_logger.info(f"Processing payment for Order:{order_id}, Amount:{amount}")
    try:
        # Payment processing logic
        api_logger.info(f"✅ Payment successful for Order:{order_id}")
        return True
    except PaymentGatewayError as pge:
        log_error(pge, f"Payment gateway error for Order:{order_id}")
        return False
    except Exception as e:
        log_error(e, f"Processing payment for Order:{order_id}")
        raise
```

### Third-party Integrations

```python
from utils.logging import gemini_logger, log_error

def fetch_external_data(resource_id):
    gemini_logger.info(f"Fetching external data for Resource:{resource_id}")
    try:
        response = requests.get(f"https://api.example.com/resources/{resource_id}")
        gemini_logger.debug(f"Response status: {response.status_code}")
        response.raise_for_status()
        gemini_logger.info(f"✅ Successfully fetched data for Resource:{resource_id}")
        return response.json()
    except requests.exceptions.RequestException as re:
        log_error(re, f"API request for Resource:{resource_id}", log_full_trace=False)
        return None
```
