from django.conf import settings
from django.db import models
from api.services.models import Service, ServicePackage


class TherapistProfile(models.Model):
    """
    Extends the User model to include therapist-specific information.
    """

    GENDER_PREFERENCE_CHOICES = [
        ("M", "Male"),
        ("F", "Female"),
        ("B", "Both"),
    ]

    user = models.OneToOneField(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    qualifications = models.TextField(blank=True, help_text="Therapist's qualifications")
    start_year = models.PositiveIntegerField(
        blank=True, null=True, help_text="Year the therapist started practicing"
    )
    gender_preference = models.CharField(
        max_length=1,
        choices=GENDER_PREFERENCE_CHOICES,
        default="B",
        help_text="Client gender preference: Male, Female, or Both",
    )
    location = models.CharField(
        max_length=1,
        default="A",
    )
    services = models.ManyToManyField(Service)
    eligible_packages = models.ManyToManyField(ServicePackage, blank=True)
    is_active = models.BooleanField(
        default=True, help_text="Whether the therapist is currently active"
    )
    is_freelancer = models.BooleanField(default=False)
    freelancer_service_rate = models.DecimalField(
        max_digits=5, decimal_places=2, default=50.00,
        help_text="Percentage commission for freelancer services (default 50%)"
    )
    freelancer_package_rate = models.DecimalField(
        max_digits=5, decimal_places=2, default=40.00,
        help_text="Percentage commission for freelancer packages (default 40%)"
    )

    def __str__(self):
        return f"{self.user.first_name} {self.user.last_name} - Therapist"


class WorkingHour(models.Model):
    """
    Represents a therapist's working hours for a specific day.
    """

    DAY_CHOICES = [
        (0, "Monday"),
        (1, "Tuesday"),
        (2, "Wednesday"),
        (3, "Thursday"),
        (4, "Friday"),
        (5, "Saturday"),
        (6, "Sunday"),
    ]

    therapist = models.ForeignKey(
        TherapistProfile, related_name="working_hours", on_delete=models.CASCADE
    )
    day = models.IntegerField(choices=DAY_CHOICES)
    start_time = models.TimeField()
    end_time = models.TimeField()

    class Meta:
        unique_together = ("therapist", "day", "start_time", "end_time")
        ordering = ["therapist", "day", "start_time"]

    def __str__(self):
        day_name = self.get_day_display()
        return f"{self.therapist.user.get_full_name()} - {day_name}: {self.start_time} to {self.end_time}"
