"""
Django settings for base configuration of the personnel planning project.

Generated by 'django-admin startproject' using Django 4.0.
"""

import os
import environ
from datetime import timed<PERSON><PERSON>
from pathlib import Path

ROOT_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
env = environ.Env()

env_file = os.path.join(ROOT_DIR, ".env")

if os.path.isfile(env_file):
    print(f"Loading environment variables from {env_file}")
    env.read_env(env_file)
else:
    print(f".env file not found at {env_file}")
environ.Env.read_env()

BASE_DIR = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


# ==============================================================================
# CORE SETTINGS
# ==============================================================================

SECRET_KEY = env("SECRET_KEY", default="django-insecure-please-change-me")

DEBUG = env.bool("DEBUG", default=False)

ALLOWED_HOSTS = env.list("ALLOWED_HOSTS", default=["127.0.0.1", "localhost"])

INSTALLED_APPS = [
    "django.contrib.admin",
    "django.contrib.auth",
    "django.contrib.contenttypes",
    "django.contrib.sessions",
    "django.contrib.messages",
    "django.contrib.staticfiles",
    "corsheaders",
    "rest_framework",
    "simple_history",
    "api.core",
    "api.authentication",
    "api.services",
    "api.staff",
    "api.appointments",
    "api.retail",
    "api.commissions",
]

DEFAULT_AUTO_FIELD = "django.db.models.BigAutoField"

ROOT_URLCONF = "config.urls"

INTERNAL_IPS = ["127.0.0.1"]

WSGI_APPLICATION = "config.wsgi.application"

AUTH_USER_MODEL = "authentication.User"

# ========================================================
# MIDDLEWARE SETTINGS
# ========================================================

MIDDLEWARE = [
    # "utils.sanitize_input_middleware.SanitizeInputMiddleware",
    "django.middleware.security.SecurityMiddleware",
    "django.contrib.sessions.middleware.SessionMiddleware",
    "corsheaders.middleware.CorsMiddleware",
    "django.middleware.common.CommonMiddleware",
    "django.middleware.csrf.CsrfViewMiddleware",
    "django.contrib.auth.middleware.AuthenticationMiddleware",
    "django.contrib.messages.middleware.MessageMiddleware",
    "django.middleware.clickjacking.XFrameOptionsMiddleware",
    "simple_history.middleware.HistoryRequestMiddleware",
]

REST_FRAMEWORK = {
    # "EXCEPTION_HANDLER": "utils.handlers.custom_exception_handler",
    "DEFAULT_AUTHENTICATION_CLASSES": [
        "rest_framework_simplejwt.authentication.JWTAuthentication",
    ],
    "DEFAULT_PERMISSION_CLASSES": [
        "rest_framework.permissions.IsAuthenticated",
    ],
    "DEFAULT_THROTTLE_CLASSES": [
        "rest_framework.throttling.AnonRateThrottle",
        "rest_framework.throttling.UserRateThrottle",
        "rest_framework.throttling.ScopedRateThrottle",
    ],
    "DEFAULT_THROTTLE_RATES": {
        "anon": "50000/day",
        "user": "10000/day",
        "login": "10/minute",
    },
}

SIMPLE_JWT = {
    "ACCESS_TOKEN_LIFETIME": timedelta(minutes=600),
    "REFRESH_TOKEN_LIFETIME": timedelta(days=300),
}
# ==============================================================================
# TEMPLATES SETTINGS
# ==============================================================================

TEMPLATES = [
    {
        "BACKEND": "django.template.backends.django.DjangoTemplates",
        "DIRS": [os.path.join(ROOT_DIR, "templates")],
        "APP_DIRS": True,
        "OPTIONS": {
            "context_processors": [
                "django.template.context_processors.debug",
                "django.template.context_processors.request",
                "django.contrib.auth.context_processors.auth",
                "django.contrib.messages.context_processors.messages",
            ],
        },
    },
]


# ==============================================================================
# DATABASES SETTINGS
# ==============================================================================

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.postgresql",
        "NAME": env("DB_NAME", default="postgres"),
        "USER": env("DB_USER", default="postgres"),
        "PASSWORD": env("DB_PASSWORD", default=""),
        "HOST": env("DB_HOST", default="db"),
        "PORT": env("DB_PORT", default="5432"),
    },
}


# ==============================================================================
# EMAIL SETTINGS
# ==============================================================================

EMAIL_BACKEND = "django.core.mail.backends.smtp.EmailBackend"
EMAIL_HOST = env("EMAIL_HOST")
EMAIL_PORT = env.int("EMAIL_PORT")
EMAIL_USE_TLS = env.bool("EMAIL_USE_TLS")
EMAIL_USE_SSL = env.bool("EMAIL_USE_SSL")
EMAIL_HOST_USER = env("EMAIL_HOST_USER")
EMAIL_HOST_PASSWORD = env("EMAIL_HOST_PASSWORD")
DEFAULT_FROM_EMAIL = env("DEFAULT_FROM_EMAIL")


# ==============================================================================
# LOG SETTINGS
# ==============================================================================


logs_dir = os.path.join(BASE_DIR, "logs")
Path(logs_dir).mkdir(parents=True, exist_ok=True)

LOGGING = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "verbose": {
            "format": "{levelname} {asctime} {module} {message}",
            "style": "{",
        },
        "api": {
            "format": "{levelname} {asctime} {module} {message}",
            "style": "{",
        },
        "gemini": {
            # Removed problematic fields like 'function' and 'duration'
            "format": "{levelname} {asctime} {module} {message}",
            "style": "{",
        },
        "commission_detailed": {
            "format": "{asctime} | {levelname:8} | {name:20} | {message}",
            "style": "{",
        },
    },
    "filters": {
        "require_debug_true": {
            "()": "django.utils.log.RequireDebugTrue",
        },
    },
    "handlers": {
        "console": {
            "level": "INFO",
            "class": "logging.StreamHandler",
            "formatter": "verbose",
        },
        "file": {
            "level": "INFO",
            "class": "logging.handlers.RotatingFileHandler",
            "filename": os.path.join(logs_dir, "django.log"),
            "formatter": "verbose",
            "maxBytes": 10485760,  # 10MB
            "backupCount": 5,
            "delay": True,  # Don't open the file until first log
        },
        "api_file": {
            "level": "INFO",
            "class": "logging.handlers.RotatingFileHandler",
            "filename": os.path.join(logs_dir, "api.log"),
            "formatter": "api",
            "maxBytes": 10485760,  # 10MB
            "backupCount": 5,
            "delay": True,  # Don't open the file until first log
        },
        "gemini_file": {
            "level": "INFO",
            "class": "logging.handlers.RotatingFileHandler",
            "filename": os.path.join(logs_dir, "gemini.log"),
            "formatter": "gemini",
            "maxBytes": 10485760,  # 10MB
            "backupCount": 5,
            "delay": True,  # Don't open the file until first log
        },
        "error_file": {
            "level": "ERROR",
            "class": "logging.handlers.RotatingFileHandler",
            "filename": os.path.join(logs_dir, "error.log"),
            "formatter": "verbose",
            "maxBytes": 10485760,  # 10MB
            "backupCount": 5,
            "delay": True,  # Don't open the file until first log
        },
        "commission_console": {
            "level": "INFO",
            "class": "logging.StreamHandler",
            "formatter": "commission_detailed",
        },
        "commission_file": {
            "level": "INFO",
            "class": "logging.handlers.RotatingFileHandler",
            "filename": os.path.join(logs_dir, "commission_calculations.log"),
            "formatter": "commission_detailed",
            "maxBytes": 10485760,  # 10MB
            "backupCount": 5,
            "delay": True,  # Don't open the file until first log
        },
    },
    "loggers": {
        "django": {
            "handlers": ["console", "file"],
            "level": "INFO",
            "propagate": True,
        },
        "api": {
            "handlers": ["console", "api_file"],
            "level": "INFO",
            "propagate": False,
        },
        "gemini": {
            "handlers": ["console", "gemini_file"],
            "level": "INFO",
            "propagate": False,
        },
        "commission_calculator": {
            "handlers": ["commission_console", "commission_file"],
            "level": "INFO",
            "propagate": False,
        },
        "": {  # Root logger
            "handlers": ["console", "error_file"],
            "level": "ERROR",
        },
    },
}

import platform
import sys
import io

# If running on Windows, adjust log file handling to avoid permission errors
if platform.system() == "Windows":
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding="utf-8")
    sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding="utf-8")

    # Add UTF-8 encoding to all file handlers in the LOGGING configuration
    for handler_name, handler_config in LOGGING["handlers"].items():
        if handler_config.get("class") in [
            "logging.handlers.RotatingFileHandler",
            "logging.handlers.WatchedFileHandler",
            "logging.FileHandler",
        ]:
            handler_config["encoding"] = "utf-8"

    # Modify handlers to use WatchedFileHandler instead of RotatingFileHandler
    # This handles the case where Windows locks files during log rotation
    for handler_name in ["file", "api_file", "gemini_file", "error_file", "commission_file"]:
        if handler_name in LOGGING["handlers"]:
            # Change handler type
            LOGGING["handlers"][handler_name][
                "class"
            ] = "logging.handlers.WatchedFileHandler"

            # Remove rotation-specific settings that aren't needed
            if "maxBytes" in LOGGING["handlers"][handler_name]:
                del LOGGING["handlers"][handler_name]["maxBytes"]
            if "backupCount" in LOGGING["handlers"][handler_name]:
                del LOGGING["handlers"][handler_name]["backupCount"]


# ==============================================================================
# AUTHENTICATION AND AUTHORIZATION SETTINGS
# ==============================================================================

AUTH_PASSWORD_VALIDATORS = [
    {
        "NAME": "django.contrib.auth.password_validation.UserAttributeSimilarityValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.MinimumLengthValidator",
        "OPTIONS": {
            "min_length": 9,
        },
    },
    {
        "NAME": "django.contrib.auth.password_validation.CommonPasswordValidator",
    },
    {
        "NAME": "django.contrib.auth.password_validation.NumericPasswordValidator",
    },
]


# ==============================================================================
# I18N AND L10N SETTINGS
# ==============================================================================

LANGUAGE_CODE = "de-de"

TIME_ZONE = "Asia/Dubai"

USE_I18N = True

USE_L10N = True

USE_TZ = True


# ==============================================================================
# STATIC FILES SETTINGS
# ==============================================================================

STATIC_URL = "/static/"
STATIC_ROOT = os.path.join(BASE_DIR, "static")


# ==============================================================================
# MEDIA FILES SETTINGS
# ==============================================================================

# Base URL to serve media files
MEDIA_URL = "/media/"

# Absolute filesystem path to the directory that will hold user-uploaded files
MEDIA_ROOT = os.path.join(BASE_DIR, "media")

# Ensure the directory exists
os.makedirs(MEDIA_ROOT, exist_ok=True)


# ==============================================================================
# THIRD-PARTY SETTINGS
# ==============================================================================

# CELERY_BROKER_URL = env("CELERY_BROKER_URL", default="redis://redis:6379/0")
# CELERY_RESULT_BACKEND = env("CELERY_RESULT_BACKEND", default="redis://redis:6379/0")
# CELERY_ACCEPT_CONTENT = ["json"]
# CELERY_TASK_SERIALIZER = "json"
# CELERY_RESULT_SERIALIZER = "json"


# CACHES = {
#     "default": {
#         "BACKEND": "django_redis.cache.RedisCache",
#         "LOCATION": "redis://redis:6379/1",
#         "OPTIONS": {
#             "CLIENT_CLASS": "django_redis.client.DefaultClient",
#         },
#     }
# }


SIMPLE_HISTORY_REVERT_DISABLED = True
