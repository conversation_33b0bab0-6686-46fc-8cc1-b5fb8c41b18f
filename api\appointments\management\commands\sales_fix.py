from django.core.management.base import BaseCommand
from django.db import transaction
from api.appointments.models import Appointment, Sale
from datetime import datetime, time
from utils.logging import logger
import re


class Command(BaseCommand):
    help = "Creates missing Sale objects for checked-in appointments"

    def add_arguments(self, parser):
        parser.add_argument(
            "--dry-run",
            action="store_true",
            help="Run in dry-run mode without creating sales",
        )

    def handle(self, *args, **options):
        dry_run = options.get("dry-run", False)

        if dry_run:
            self.stdout.write(
                self.style.WARNING("Running in DRY RUN mode - no sales will be created")
            )

        # Get all checked-in appointments with positive price but no sales
        appointments = Appointment.objects.filter(status="check_in", total_price__gt=0)

        total_appointments = appointments.count()
        missing_sales_count = 0
        created_sales_count = 0

        self.stdout.write(
            self.style.SUCCESS(
                f"Found {total_appointments} checked-in appointments to process"
            )
        )

        # Process each appointment
        for appointment in appointments:
            # Check if sale already exists
            if Sale.objects.filter(appointment=appointment).exists():
                continue

            missing_sales_count += 1
            self.stdout.write(
                f"Found appointment {appointment.id} without sale (Customer: {appointment.customer.email}, Date: {appointment.date})"
            )

            if dry_run:
                continue

            # Create the sales object
            try:
                with transaction.atomic():
                    # Generate sale datetime
                    if appointment.time:
                        sale_datetime = datetime.combine(
                            appointment.date, appointment.time
                        )
                    else:
                        sale_datetime = datetime.combine(appointment.date, time())

                    # Get package_option
                    package_option = None
                    if (
                        hasattr(appointment, "package_option")
                        and appointment.package_option
                    ):
                        package_option = appointment.package_option
                    elif (
                        appointment.user_package
                        and appointment.user_package.package_option
                    ):
                        package_option = appointment.user_package.package_option
                    elif (
                        appointment.shared_package
                        and appointment.shared_package.package_option
                    ):
                        package_option = appointment.shared_package.package_option
                    elif (
                        appointment.unlimited_package
                        and appointment.unlimited_package.package_option
                    ):
                        package_option = appointment.unlimited_package.package_option

                    # Determine sale type
                    sale_type = (
                        "package"
                        if (
                            package_option
                            or appointment.user_package
                            or appointment.shared_package
                            or appointment.unlimited_package
                        )
                        else "service"
                    )

                    # Create sale with appropriate package type
                    if appointment.user_package:
                        sale = Sale.objects.create(
                            user=appointment.customer,
                            sale_type=sale_type,
                            appointment=appointment,
                            location=appointment.location,
                            user_package=appointment.user_package,
                            package_option=package_option,
                            total_price=appointment.total_price,
                            discount=appointment.discount,
                            payment_method=appointment.payment_method
                            or "card",  # Default to card if empty
                            discount_percentage=appointment.discount_percentage,
                            created_at=sale_datetime,
                        )
                    elif appointment.shared_package:
                        sale = Sale.objects.create(
                            user=appointment.customer,
                            sale_type=sale_type,
                            appointment=appointment,
                            location=appointment.location,
                            shared_package=appointment.shared_package,
                            package_option=package_option,
                            total_price=appointment.total_price,
                            payment_method=appointment.payment_method or "card",
                            discount=appointment.discount,
                            discount_percentage=appointment.discount_percentage,
                            created_at=sale_datetime,
                        )
                    elif appointment.unlimited_package:
                        sale = Sale.objects.create(
                            user=appointment.customer,
                            sale_type=sale_type,
                            appointment=appointment,
                            location=appointment.location,
                            unlimited_package=appointment.unlimited_package,
                            package_option=package_option,
                            total_price=appointment.total_price,
                            payment_method=appointment.payment_method or "card",
                            discount=appointment.discount,
                            discount_percentage=appointment.discount_percentage,
                            created_at=sale_datetime,
                        )
                    else:
                        sale = Sale.objects.create(
                            user=appointment.customer,
                            sale_type=sale_type,
                            appointment=appointment,
                            location=appointment.location,
                            package_option=package_option,
                            total_price=appointment.total_price,
                            payment_method=appointment.payment_method or "card",
                            discount=appointment.discount,
                            discount_percentage=appointment.discount_percentage,
                            created_at=sale_datetime,
                        )

                    created_sales_count += 1
                    self.stdout.write(
                        self.style.SUCCESS(
                            f"✅ Created Sale:{sale.id} for Appointment:{appointment.id}"
                        )
                    )

            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(
                        f"❌ Error creating sale for Appointment:{appointment.id} - {str(e)}"
                    )
                )

        # Summary
        self.stdout.write("=" * 50)
        self.stdout.write(
            self.style.SUCCESS(f"Found {missing_sales_count} appointments without sales")
        )
        if not dry_run:
            self.stdout.write(
                self.style.SUCCESS(f"Successfully created {created_sales_count} sales")
            )

        # Fix the invoice number generation method
        if not dry_run:
            self.stdout.write(
                "Updating the Sale model invoice_number generation method..."
            )
            Sale.generate_invoice_number = self.fixed_generate_invoice_number
            self.stdout.write(
                self.style.SUCCESS(
                    "Successfully updated the invoice_number generation method"
                )
            )

    @staticmethod
    def fixed_generate_invoice_number(self):
        """Generate a unique invoice number"""
        if self.invoice_number:
            return

        # Determine location code
        location_code = "AW" if self.location == "A" else "AM"

        # Get current year (2-digit)
        year = datetime.datetime.now().strftime("%y")

        # Find the highest sequential number used so far for this year and location
        highest_number = 0
        pattern = f"INV-{location_code}-{year}(\\d{{4}})"

        for sale in Sale.objects.filter(
            invoice_number__startswith=f"INV-{location_code}-{year}"
        ):
            match = re.match(pattern, sale.invoice_number)
            if match:
                seq_num = int(match.group(1))
                highest_number = max(highest_number, seq_num)

        # Generate sequential number (1-based, padded to 4 digits)
        seq_number = f"{highest_number + 1:04d}"

        # Create the invoice number
        invoice_number = f"INV-{location_code}-{year}{seq_number}"

        # Make sure it's unique (just in case)
        while Sale.objects.filter(invoice_number=invoice_number).exists():
            highest_number += 1
            seq_number = f"{highest_number + 1:04d}"
            invoice_number = f"INV-{location_code}-{year}{seq_number}"

        self.invoice_number = invoice_number
