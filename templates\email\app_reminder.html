<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <meta name="x-apple-disable-message-reformatting" />
    <title>Appointment Confirmation</title>
  </head>
  <body
    style="
      margin: 0;
      padding: 0;
      background-color: #f5f5f5;
      font-family: Arial, Helvetica, sans-serif;
    "
  >
    <table
      align="center"
      width="100%"
      cellpadding="0"
      cellspacing="0"
      style="
        max-width: 600px;
        margin: 20px auto;
        background-color: #ffffff;
        border-radius: 8px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
      "
    >
      <!-- Preheader (hidden preview text) -->
      <tr>
        <td
          style="
            display: none;
            font-size: 1px;
            color: #f5f5f5;
            line-height: 1px;
            max-height: 0px;
            max-width: 0px;
            opacity: 0;
            overflow: hidden;
          "
        >
          Your appointment with StretchUp has been confirmed for {{ appointment.date }} at {{ appointment.time }}!
        </td>
      </tr>
      <!-- Header -->
      <tr>
        <td
          style="
            background-color: #cc9648;
            padding: 20px;
            text-align: center;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
          "
        >
          <h1 style="color: #ffffff; margin: 0; font-size: 24px">
            Appointment Confirmed
          </h1>
        </td>
      </tr>
      <!-- Content -->
      <tr>
        <td style="padding: 30px; color: #2a2828">
          <p style="font-size: 16px; line-height: 1.5; margin: 0 0 10px">
            Hello {{ appointment.customer_obj.first_name }},
          </p>
          <p style="font-size: 16px; line-height: 1.5; margin: 0 0 20px">
            Your appointment has been successfully booked. Here are your
            appointment details:
          </p>

          <!-- Appointment Details Box -->
          <table
            width="100%"
            cellpadding="0"
            cellspacing="0"
            style="
              background-color: #f9f9f9;
              border-radius: 6px;
              margin-bottom: 20px;
              border: 1px solid #eeeeee;
            "
          >
            <tr>
              <td style="padding: 15px">
                <table width="100%" cellpadding="8" cellspacing="0">
                  <tr>
                    <td style="font-weight: bold; width: 40%">Date:</td>
                    <td>{{ appointment.date }}</td>
                  </tr>
                  <tr>
                    <td style="font-weight: bold">Time:</td>
                    <td>{{ appointment.time }}</td>
                  </tr>
                  <tr>
                    <td style="font-weight: bold">Duration:</td>
                    <td>{{ appointment.total_duration }} minutes</td>
                  </tr>
                  <tr>
                    <td style="font-weight: bold">Location:</td>
                    <td>
                      {% if appointment.location == "A" %} Studio Al Warqa Mall
                      {% else %} Studio Al Mizhar Branch {% endif %}
                    </td>
                  </tr>
                  <tr>
                    <td style="font-weight: bold">Therapist:</td>
                    <td>
                      {{ appointment.therapist_obj.first_name }} {{ appointment.therapist_obj.last_name }}
                    </td>
                  </tr>
                </table>
              </td>
            </tr>
          </table>

          <!-- Services Section -->
          {% if appointment.appointment_services %}
          <div
            style="
              border: 1px solid #eeeeee;
              border-radius: 6px;
              overflow: hidden;
              margin-bottom: 20px;
            "
          >
            <div
              style="
                background-color: #f4f4f4;
                padding: 10px 15px;
                border-bottom: 1px solid #eeeeee;
              "
            >
              <p style="margin: 0; font-weight: bold; font-size: 15px">
                Services
              </p>
            </div>

            <div style="padding: 10px 15px">
              {% for service in appointment.appointment_services %}
              <div
                style="
                margin-bottom: 8px;
                padding-bottom: 8px;
                {% if not forloop.last %}border-bottom: 1px solid #eeeeee;{% endif %}
              "
              >
                <p style="margin: 0; font-size: 14px">
                  - {{ service.service_name }} ({{ service.duration }} min)
                </p>
              </div>
              {% endfor %}
            </div>
          </div>
          {% endif %}

          <!-- Package Information - Unified simple version -->
          {% if appointment.user_package_obj or appointment.shared_package_obj or appointment.unlimited_package_obj %}
          <div
            style="
              background-color: #f5f9ff;
              border-radius: 6px;
              padding: 15px;
              margin-bottom: 20px;
              border: 1px solid #e1e8f0;
            "
          >
            <p
              style="
                margin: 0 0 10px;
                font-weight: bold;
                font-size: 15px;
                color: #0066cc;
              "
            >
              Package Information:
            </p>

            <!-- User Package -->
            {% if appointment.user_package_obj %}
            <p style="margin: 0 0 5px; font-size: 14px">
              <strong>Package:</strong> {{ appointment.user_package_obj.package_option.package.name }}
            </p>
            <p style="margin: 0 0 5px; font-size: 14px">
              <strong>Remaining Time:</strong> {{ appointment.user_package_obj.remaining_time }} minutes
            </p>
            <p style="margin: 0; font-size: 14px">
              <strong>Expires:</strong> {{ appointment.user_package_obj.expiry_date }}
            </p>
            {% endif %}

            <!-- Shared Package -->
            {% if appointment.shared_package_obj %}
            <p style="margin: 0 0 5px; font-size: 14px">
              <strong>Shared Package:</strong> {{ appointment.shared_package_obj.package_option.package.name }}
            </p>
            <p style="margin: 0 0 5px; font-size: 14px">
              <strong>Remaining Time:</strong> {{ appointment.shared_package_obj.remaining_time }} minutes
            </p>
            <p style="margin: 0; font-size: 14px">
              <strong>Expires:</strong> {{ appointment.shared_package_obj.expiry_date }}
            </p>
            {% endif %}

            <!-- Unlimited Package -->
            {% if appointment.unlimited_package_obj %}
            <p style="margin: 0 0 5px; font-size: 14px">
              <strong>Unlimited Package:</strong> {{ appointment.unlimited_package_obj.package_option.package.name }}
            </p>
            <p style="margin: 0; font-size: 14px">
              <strong>Expires:</strong> {{ appointment.unlimited_package_obj.expiry_date }}
            </p>
            {% endif %}
          </div>
          {% endif %}

          <!-- Price information - Simple version -->
          {% if appointment.total_price > 0 %}
          <table
            width="100%"
            cellpadding="0"
            cellspacing="0"
            style="
              margin: 20px 0;
              border-top: 1px solid #eeeeee;
              padding-top: 15px;
            "
          >
            <tr>
              <td style="font-weight: bold; font-size: 18px; padding: 10px 0">
                Total:
              </td>
              <td
                style="
                  font-weight: bold;
                  font-size: 18px;
                  text-align: right;
                  padding: 10px 0;
                "
              >
                {{ appointment.total_price }} AED
              </td>
            </tr>
          </table>
          {% endif %}

          <!-- Notes (if any) -->
          {% if appointment.notes %}
          <div
            style="
              margin: 20px 0;
              padding: 15px;
              background-color: #f9f9f9;
              border-radius: 6px;
              border-left: 4px solid #cc9648;
            "
          >
            <p style="margin: 0 0 5px; font-weight: bold">Notes:</p>
            <p style="margin: 0; font-size: 14px; line-height: 1.5">
              {{ appointment.notes }}
            </p>
          </div>
          {% endif %}

          <!-- Cancellation Policy -->
          <div
            style="
              margin: 25px 0 20px;
              padding: 15px;
              border-radius: 6px;
              background-color: #fff8e6;
              border: 1px solid #ffe0b2;
            "
          >
            <p
              style="
                margin: 0 0 8px;
                font-size: 15px;
                font-weight: bold;
                color: #e65100;
              "
            >
              Cancellation Policy:
            </p>
            <ul
              style="
                margin: 0;
                padding: 0 0 0 20px;
                font-size: 14px;
                color: #e65100;
                line-height: 1.5;
              "
            >
              <li>
                Cancellations must be made at least 24 hours before your
                scheduled appointment.
              </li>
              <li>
                Late cancellations (less than 24 hours notice) will incur a 50%
                charge of the service value.
              </li>
              <li>No-shows will be charged the full service value.</li>
              <li>
                For package bookings, late cancellations or no-shows will result
                in the deduction of the scheduled time from your package.
              </li>
            </ul>
          </div>

          <!-- Contact Info -->
          <p
            style="
              font-size: 14px;
              line-height: 1.5;
              color: #666666;
              margin: 0 0 10px;
            "
          >
            If you need to reschedule or have any questions about your
            appointment, please
            <a
              href="mailto:<EMAIL>"
              style="color: #cc9648; text-decoration: none"
              >contact our support team</a
            >
            or call us at 0585800440.
          </p>

          <p
            style="
              font-size: 14px;
              line-height: 1.5;
              color: #666666;
              margin: 20px 0 0;
            "
          >
            We look forward to seeing you soon!
          </p>
          <p
            style="
              font-size: 14px;
              line-height: 1.5;
              color: #666666;
              margin: 10px 0 0;
            "
          >
            The StretchUp Team
          </p>
        </td>
      </tr>
      <!-- Footer -->
      <tr>
        <td
          style="
            background-color: #2a2828;
            padding: 15px;
            text-align: center;
            border-bottom-left-radius: 8px;
            border-bottom-right-radius: 8px;
          "
        >
          <p style="color: #ffffff; font-size: 12px; margin: 0 0 5px">
            © 2025 StretchUp. All rights reserved.
          </p>
          <p style="color: #cccccc; font-size: 11px; margin: 0">
            <a
              href="https://stretchup.ae"
              style="color: #cccccc; text-decoration: none"
              >Visit our website</a
            >
          </p>
        </td>
      </tr>
    </table>
  </body>
</html>
