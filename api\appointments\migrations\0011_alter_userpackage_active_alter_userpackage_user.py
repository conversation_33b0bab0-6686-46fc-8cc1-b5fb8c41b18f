# Generated by Django 4.0 on 2025-02-04 16:25

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('authentication', '0005_historicaluser_gender_user_gender'),
        ('appointments', '0010_remove_appointment_package_balance_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='userpackage',
            name='active',
            field=models.BooleanField(default=True, help_text='Indicates if the package is active (i.e. remaining_time > 0)'),
        ),
        migrations.AlterField(
            model_name='userpackage',
            name='user',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='user_packages', to='authentication.user'),
        ),
    ]
