from django.core.management.base import BaseCommand

from django.contrib.auth import get_user_model

from api.appointments import models as ap
from api.staff import models as st
from api.services import models as se


User = get_user_model()


class Command(BaseCommand):
    help = "Deletes all records from the specified models. Replace the model placeholders with your models."

    def handle(self, *args, **options):
        # Warning: this command is destructive.
        confirmation = input(
            "WARNING: This will delete ALL records from the specified models.\n"
            "Type 'yes' to confirm: "
        )
        if confirmation.lower() != "yes":
            self.stdout.write("Operation cancelled.")
            return

        # List of models to clear.
        models_to_clear = [
            User,
            ap.Activity,
            ap.Appointment,
            ap.AppointmentService,
            ap.UserPackage,
            st.TherapistProfile,
            st.WorkingHour,
            se.Service,
            se.ServiceDuration,
            se.ServicePackage,
            se.PackageOption,
        ]
        for model in models_to_clear:
            model_name = model.__name__
            record_count = model.objects.count()
            model.objects.all().delete()
            self.stdout.write(
                f"Deleted {record_count} records from model '{model_name}'."
            )

        user, _ = User.objects.get_or_create(
            email="<EMAIL>",
            defaults={
                "first_name": "Amaldar",
                "last_name": "Dubai",
                "role": "owner",
                "phone_number": f"+971561100533",
                "gender": "male",
            },
        )
        user.is_superuser = True
        user.is_active = True
        user.is_staff = True
        user.save()
        self.stdout.write(
            self.style.SUCCESS("All specified records have been deleted successfully.")
        )
