from django.contrib.auth.base_user import AbstractBaseUser, BaseUserManager
from django.db import models
from django.contrib.auth.models import PermissionsMixin, UserManager
from django.utils.timezone import now, timedelta
from django.conf import settings

from simple_history.models import HistoricalRecords

import logging
import traceback

# logger = logging.getLogger("django")


class CustomUserManager(BaseUserManager):
    def create_user(self, email, phone_number, password=None, **extra_fields):
        if not email:
            raise ValueError("The Email must be set")
        if not phone_number:
            raise ValueError("The Phone Number must be set")
        email = self.normalize_email(email)
        user = self.model(email=email, phone_number=phone_number, **extra_fields)
        if extra_fields.get("is_superuser") is True:
            user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(self, email, phone_number, password, **extra_fields):
        extra_fields.setdefault("is_staff", True)
        extra_fields.setdefault("is_superuser", True)

        if extra_fields.get("is_staff") is not True:
            raise ValueError("Superuser must have is_staff=True.")
        if extra_fields.get("is_superuser") is not True:
            raise ValueError("Superuser must have is_superuser=True.")

        return self.create_user(
            email=email, phone_number=phone_number, password=password, **extra_fields
        )


class User(AbstractBaseUser, PermissionsMixin):
    ROLE_CHOICES = [
        ("customer", "Customer"),
        ("therapist", "Therapist"),
        ("owner", "Business Owner"),
        ("receptionist", "Receptionist"),
    ]

    email = models.EmailField(unique=True)
    phone_number = models.CharField(max_length=15, unique=True)
    is_staff = models.BooleanField(default=False)
    is_active = models.BooleanField(default=True)
    first_name = models.CharField(max_length=80)
    last_name = models.CharField(max_length=80)
    gender = models.CharField(max_length=80, default="male")

    role = models.CharField(max_length=20, choices=ROLE_CHOICES, default="customer")

    otp_token = models.CharField(max_length=6, null=True, blank=True)
    otp_expiration = models.DateTimeField(null=True, blank=True)

    # NEW FIELD: Expo Push Token for mobile notifications
    expo_push_token = models.CharField(max_length=255, blank=True, null=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    history = HistoricalRecords(inherit=True)

    # Profile-related fields
    address = models.TextField(blank=True, null=True)
    date_of_birth = models.DateField(blank=True, null=True)
    profile_picture = models.ImageField(
        upload_to="profile_pictures/", blank=True, null=True
    )
    medical_issues = models.TextField(blank=True, null=True)
    goal = models.TextField(blank=True, null=True)

    objects = CustomUserManager()

    USERNAME_FIELD = "email"
    REQUIRED_FIELDS = ["phone_number", "first_name", "last_name"]

    def get_full_name(self):
        return f"{self.first_name} {self.last_name}"

    def generate_otp(self):
        import pyotp

        try:
            # Generate a 6-digit OTP
            otp = pyotp.TOTP(pyotp.random_base32()).now()

            # Save OTP and expiry time
            self.otp_token = otp
            self.otp_expiration = now() + timedelta(
                minutes=10
            )  # OTP valid for 10 minutes
            self.save(update_fields=["otp_token", "otp_expiration"])

            # logger.info(f"Generated OTP for {self.email}: {otp}")
            return otp
        except Exception as e:
            tb = traceback.format_exc()
            # logger.error(f"Error generating OTP for {self.email}: {str(e)}")
            print(tb)
            raise e

    def verify_otp(self, otp):
        if self.otp_expiration and self.otp_expiration >= now() and self.otp_token == otp:
            self.otp_token = None
            self.otp_expiration = None
            self.save()
            return True
        return False


class CacheTest(models.Model):
    name = models.CharField(max_length=255)
    price = models.DecimalField(max_digits=10, decimal_places=2)
    description = models.TextField()

    def __str__(self):
        return self.name
