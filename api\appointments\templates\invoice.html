<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tax Invoice</title>
    <style>
      @page {
        size: A4;
        margin: 0;
      }
      
      body {
        font-family: Arial, sans-serif;
        margin: 0;
        padding: 0;
        background-color: white;
      }

      .invoice-container {
        width: 100%;
        padding:140px 20px 20px 20px; /* Added more top padding */
        box-sizing: border-box;
      }

      .header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 30px;
        width: 100%;
      }

      .logo-container {
        flex-grow: 1;
      }

      .logo {
        margin-bottom: 15px;
      }
      
      .logo img {
        height: 60px;
        width: auto;
      }

      .company-details {
        text-align: right;
        font-size: 12px;
      }

      .company-details strong {
        font-size: 14px;
        margin-bottom: 5px;
        display: inline-block;
      }

      /* Fixed invoice info box */
      .invoice-info-column {
        margin-bottom: 30px;
      }

      .invoice-info-box {
        background-color: #f0f0f0;
        padding: 20px;
        width: 100%;
        box-sizing: border-box;
        margin-bottom: 20px;
      }

      .invoice-info-box h3 {
        margin-top: 0;
        margin-bottom: 15px;
        font-size: 18px;
        font-weight: bold;
      }

      .invoice-info-box div {
        margin-bottom: 8px;
        font-size: 12px;
        line-height: 1.5;
      }

      .bill-to {
        padding: 0 0 20px 0;
      }

      .bill-to h3 {
        margin-top: 0;
        margin-bottom: 10px;
        font-size: 14px;
        font-weight: bold;
      }

      /* Fixed table styling with borders */
      .main-table {
        width: 100%;
        border-collapse: collapse;
      }

      .main-table th, 
      .main-table td {
        padding: 10px;
        text-align: left;
        border: 1px solid #ddd;
      }

      .main-table th {
        background-color: #f0f0f0;
        font-weight: bold;
      }

      .main-table th.description-header {
        width: 70%;
      }

      .main-table th.total-header {
        width: 30%;
        text-align: right;
      }

      .main-table tbody tr td:last-child {
        text-align: right;
      }

      .main-table tfoot td {
        background-color: #f0f0f0;
        font-weight: bold;
      }

      .main-table tfoot td.summary-label {
        text-align: right;
      }

      .main-table tfoot td.summary-value {
        text-align: right;
      }

      .main-table tfoot tr.grand-total td {
        font-size: 17px;
      }

      /* Fixed PAID stamp styling to match the reference image */
     .paid-stamp {
  position: absolute;
  background-color: #8CD41E;
  color: white;
  font-weight: bold;
  font-family: Arial, sans-serif;
  text-transform: uppercase;
  text-align: center;
  z-index: 10;
  width: 150px;
  padding: 10px 0;
  font-size: 22px;
  line-height: 1;
  
  /* Adjusted positioning values */
  top: 60px;  /* Moved down from 0 to 20px */
  right: 0;
  transform-origin: top right;
  transform: rotate(45deg) translate(30px, -10px); /* Adjusted translation values */
}
    </style>
</head>
<body>
    <div class="invoice-container">
        {% if is_paid %}
        <div class="paid-stamp">PAID</div>
        {% endif %}

        <div class="header">
            <div class="logo-container">
                <div class="logo">
                    {% if Logo %}
                    <img src="{{ Logo }}" alt="STRETCH UP YOGA CENTER Logo">
                    {% else %}
                    STRETCH UP YOGA CENTER Logo
                    {% endif %}
                </div>
            </div>
            <div class="company-details">
                <strong>STRETCH UP YOGA CENTER</strong><br>
                Shop No. G4, Warqa Mall<br>
                Al Warqa 4<br>
                Dubai, United Arab Emirates<br>
                TRN: 100568102600003
            </div>
        </div>

        <div class="invoice-info-column">
            <div class="invoice-info-box">
                <h3>Tax Invoice #{{ invoice_number }}</h3>
                <div><strong>Invoice Date:</strong> {{ invoice_date }}</div>
                <div><strong>Due Date:</strong> {{ due_date }}</div>
		<div><strong>Paid with:</strong> {{ payment_method }}</div>
                {% if date_paid %}
                <div><strong>Date Paid:</strong> {{ date_paid }}</div>
                {% endif %}
                <div><strong>Location:</strong> {{location}}</div>
            </div>
            <div class="bill-to">
                <h3>Invoiced To</h3>
                {{ bill_to_name }}
            </div>
        </div>

        <table class="main-table">
            <thead>
                <tr>
                    <th class="description-header">Description</th>
                    <th class="total-header">Total</th>
                </tr>
            </thead>
            <tbody>
                {% for item in items %}
                <tr>
                    <td>{{ item.description }}</td>
                    <td>AED {{ item.total }}</td>
                </tr>
                {% endfor %}
            </tbody>
            <tfoot>
                <tr>
                    <td class="summary-label">Sub Total</td>
                    <td class="summary-value">AED {{ sub_total }}</td>
                </tr>
                <tr>
                    <td class="summary-label">5% VAT</td>
                    <td class="summary-value">AED {{ vat_amount }}</td>
                </tr>
                <tr class="grand-total">
                    <td class="summary-label">Total</td>
                    <td class="summary-value">AED {{ grand_total }}</td>
                </tr>
            </tfoot>
        </table>
    </div>
</body>
</html>