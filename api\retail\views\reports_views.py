from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters
from django.utils import timezone
from django.shortcuts import render
from django.http import HttpResponse
import logging
import traceback
from datetime import datetime

from api.retail.models import DailySalesReport
from api.retail.serializers import DailySalesReportSerializer
from api.core.permissions import IsOwnerOrReceptionist
from .helpers.daily_sales_simplified import generate_daily_sales_report

logger = logging.getLogger(__name__)


class DailySalesReportViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing daily sales reports.
    """

    queryset = DailySalesReport.objects.all().order_by("-date")
    serializer_class = DailySalesReportSerializer
    permission_classes = [IsAuthenticated, IsOwnerOrReceptionist]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ["location"]
    ordering_fields = ["date"]
    ordering = ["-date"]

    def perform_create(self, serializer):
        """Set the created_by to the current user."""
        serializer.save(created_by=self.request.user)

    @action(detail=True, methods=["get"])
    def test(self, request, pk=None):
        """Simple test endpoint to verify routing is working."""
        print(f"\n\nTEST ENDPOINT CALLED for report {pk}")
        logger.error(f"TEST ENDPOINT CALLED for report {pk}")
        return HttpResponse(f"Test successful for report {pk}")

    @action(detail=False, methods=["post"])
    def generate(self, request):
        """
        Generate or update a daily sales report for a specific date and location.
        """
        try:
            date_str = request.data.get("date")
            location = request.data.get("location")

            if not location:
                return Response(
                    {"detail": "Location parameter is required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            if date_str:
                try:
                    date = datetime.strptime(date_str, "%Y-%m-%d").date()
                except ValueError:
                    return Response(
                        {"detail": "Invalid date format. Use YYYY-MM-DD"},
                        status=status.HTTP_400_BAD_REQUEST,
                    )
            else:
                date = timezone.now().date()

            # Generate the report
            report_data = generate_daily_sales_report(request.user, date, location)

            # Serialize the report
            serialized_report = DailySalesReportSerializer(report_data["report"]).data

            # Prepare response
            response_data = {
                "id": report_data["report"].id,
                "report": serialized_report,
                "daily": report_data["daily"],
                "monthly": report_data["monthly"],
                "date": date.strftime("%Y-%m-%d"),
                "location": location,
                "location_display": report_data["report"].get_location_display(),
            }

            return Response(
                response_data,
                status=(
                    status.HTTP_201_CREATED
                    if report_data["created"]
                    else status.HTTP_200_OK
                ),
            )

        except Exception as e:
            logger.error(f"Error generating daily report: {str(e)}")
            logger.error(traceback.format_exc())
            return Response(
                {"detail": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=True, methods=["get"], permission_classes=[IsAuthenticated])
    def export_html(self, request, pk=None):
        """Render the report as a simplified HTML page."""
        try:
            # Get the report object
            report = self.get_object()

            # Generate the full report data
            report_data = generate_daily_sales_report(
                report.created_by, report.date, report.location
            )

            # Serialize the report
            serialized_report = DailySalesReportSerializer(report).data

            # Create context using the same structure as the generate endpoint
            context = {
                "id": report.id,
                "report": serialized_report,
                "daily": report_data["daily"],
                "monthly": report_data["monthly"],
                "date": report.date.strftime("%Y-%m-%d"),
                "location": report.location,
                "location_display": report.get_location_display(),
            }

            # Debug: Log sample data
            if report_data.get("daily", {}).get("items", {}).get("sales"):
                first_sale = report_data["daily"]["items"]["sales"][0]
                logger.error(
                    f"First sale data: id={first_sale.get('id')}, created_at_formatted={first_sale.get('created_at_formatted')}"
                )

            # Render the template
            return render(request, "reports/daily_sales_report.html", context)

        except Exception as e:
            logger.error(f"Error in export_html: {str(e)}")
            logger.error(f"Full traceback:\n{traceback.format_exc()}")
            return Response(
                {"detail": f"Error rendering report: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

            # Generate the full report data (same as generate endpoint)
            try:
                print(f"Generating report data for {report.date} at {report.location}")
                logger.error(
                    f"Generating report data for {report.date} at {report.location}"
                )
                report_data = generate_daily_sales_report(
                    report.created_by, report.date, report.location
                )
                print("Report data generated successfully")
                logger.error("Report data generated successfully")
            except Exception as e:
                print(f"ERROR generating report data: {str(e)}")
                print(traceback.format_exc())
                logger.error(f"Error generating report data: {str(e)}")
                logger.error(traceback.format_exc())
                raise

            # Serialize the report
            try:
                serialized_report = DailySalesReportSerializer(report).data
                print("Report serialized successfully")
                logger.error("Report serialized successfully")
            except Exception as e:
                print(f"ERROR serializing report: {str(e)}")
                print(traceback.format_exc())
                logger.error(f"Error serializing report: {str(e)}")
                logger.error(traceback.format_exc())
                raise

            # Create context using the same structure as the generate endpoint
            context = {
                "id": report.id,
                "report": serialized_report,
                "daily": report_data["daily"],
                "monthly": report_data["monthly"],
                "date": report.date.strftime("%Y-%m-%d"),
                "location": report.location,
                "location_display": report.get_location_display(),
            }

            print(f"Context created with keys: {list(context.keys())}")
            logger.error(f"Context created with keys: {list(context.keys())}")

            # Try to render the template
            template_path = "reports/daily_sales_report.html"
            print(f"Attempting to render template: {template_path}")
            logger.error(f"Attempting to render template: {template_path}")

            try:
                from django.template.loader import get_template

                # First check if template exists
                template = get_template(template_path)
                print(f"Template found: {template_path}")
                logger.error(f"Template found: {template_path}")
            except Exception as e:
                print(f"Template not found: {template_path}")
                print(f"Template error: {str(e)}")
                print(traceback.format_exc())
                logger.error(f"Template not found: {template_path}")
                logger.error(f"Template error: {str(e)}")
                logger.error(traceback.format_exc())
                # Try alternative template name
                template_path = "daily_sales_report_simplified.html"
                print(f"Trying alternative template: {template_path}")
                logger.error(f"Trying alternative template: {template_path}")

            # Render the template
            try:
                response = render(request, template_path, context)
                print(f"Template rendered successfully: {template_path}")
                logger.error(f"Template rendered successfully: {template_path}")
                return response
            except Exception as e:
                print(f"ERROR rendering template {template_path}: {str(e)}")
                print(traceback.format_exc())
                logger.error(f"Error rendering template {template_path}: {str(e)}")
                logger.error(traceback.format_exc())
                logger.error(f"Available context keys: {list(context.keys())}")
                if "daily" in context:
                    logger.error(f"Daily data keys: {list(context['daily'].keys())}")
                if "monthly" in context:
                    logger.error(f"Monthly data keys: {list(context['monthly'].keys())}")
                raise

        except Exception as e:
            print(f"\n\nFINAL ERROR in export_html: {str(e)}")
            print(f"Full traceback:\n{traceback.format_exc()}")
            logger.error(f"Error in export_html: {str(e)}")
            logger.error(f"Full traceback:\n{traceback.format_exc()}")
            return Response(
                {"detail": f"Error rendering report: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
