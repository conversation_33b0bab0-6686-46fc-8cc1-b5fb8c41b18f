<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>Daily Sales Report - {{ date }}</title>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/css/materialize.min.css" rel="stylesheet" />
  <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet" />
  <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.9.1/chart.min.js"></script>
  <style>
    body { padding: 10px; font-family: "Roboto", sans-serif; font-size: 0.9rem; }
    .card { margin-bottom: 15px; border-radius: 6px; box-shadow: 0 1px 5px rgba(0, 0, 0, 0.1); }
    .card-title { font-size: 1.2rem; font-weight: 500; color: #37474f; margin-bottom: 10px; }
    .collection .collection-item { padding: 8px 15px; font-size: 0.9rem; }
    .summary { font-weight: bold; color: #2196f3; }
    .report-header { background-color: #37474f; color: white; padding: 15px; border-radius: 6px; margin-bottom: 15px; }
    .report-header h3 { margin: 5px 0; font-size: 1.5rem; }
    .report-header h5 { margin: 5px 0; font-size: 1.1rem; }
    .stat-card { padding: 10px; margin-bottom: 10px; color: white; }
    .positive { color: #4caf50; }
    .negative { color: #f44336; }
    .chart-container { position: relative; height: 180px; width: 100%; }
    .payment-method-item { display: flex; justify-content: space-between; align-items: center; }
    .sales-breakdown-item { padding: 10px; margin-bottom: 8px; border-radius: 4px; color: white; }
    .insights-card { background-color: #eceff1; padding: 10px; border-radius: 4px; }
    .stats-box { text-align: center; padding: 8px; border-radius: 4px; background-color: #f5f5f5; margin-bottom: 10px; }
    .stats-box h5 { margin: 0; font-size: 0.9rem; }
    .stats-box h4 { margin: 5px 0; font-size: 1.2rem; }
    .compact-table th, .compact-table td { padding: 8px 5px; font-size: 0.85rem; }
    .section-divider { margin: 15px 0; border-top: 1px solid #eee; }
    .section-header { 
      background-color: #e3f2fd; 
      padding: 8px 15px; 
      margin-bottom: 15px; 
      border-radius: 4px; 
      font-weight: 500;
      color: #1565c0;
      border-left: 4px solid #1565c0;
    }
    @media print {
      body { background-color: white; font-size: 9pt; }
      .card { box-shadow: none; margin-bottom: 8px; page-break-inside: avoid; }
      .row { page-break-inside: avoid; }
      .hide-print { display: none; }
      .chart-container { height: 150px; }
      @page { margin: 0.5cm; }
    }
  </style>
</head>
<body>
  <div class="container">
    <!-- GENERAL INFO SECTION -->
    <div class="report-header center-align">
      <h3>Daily Sales Report</h3>
      <h5>{{ location_display }} - {{ date }}</h5>
    </div>

    <!-- REPORT DATA SECTION -->
    <div class="section-header">
      <i class="material-icons small left">assessment</i> REPORT SUMMARY
    </div>
    
    <!-- Summary Dashboard -->
    <div class="row">
      <div class="col s12">
        <div class="card">
          <div class="card-content">
            <span class="card-title">Sales Dashboard</span>
            <div class="row" style="margin-bottom: 0;">
              <div class="col s12">
                <div class="stats-box">
                  <i class="material-icons small">attach_money</i>
                  <h5>Gross Sales</h5>
                  <h4>{{ report.gross_sales_amount }}</h4>
                </div>
              </div>
              <div class="col s3">
                <div class="stats-box">
                  <i class="material-icons small">account_balance</i>
                  <h5>Net Sales</h5>
                  <h4>{{ report.net_sales_amount }}</h4>
                </div>
              </div>
              <div class="col s3">
                <div class="stats-box">
                  <i class="material-icons small">local_atm</i>
                  <h5>Cash Sales</h5>
                  <h4>{{ report.cash_sales_amount }}</h4>
                </div>
              </div>
              <div class="col s3">
                <div class="stats-box">
                  <i class="material-icons small">credit_card</i>
                  <h5>Card</h5>
                  <h4>{{ report.card_sales_amount }}</h4>
                </div>
              </div>
              <div class="col s3">
                <div class="stats-box">
                  <i class="material-icons small">link</i>
                  <h5>Link</h5>
                  <h4>{{ report.online_link_amount }}</h4>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Sales Analysis -->
    <div class="row">
      <div class="col s6">
        <div class="card">
          <div class="card-content">
            <span class="card-title">Sales by Type</span>
            <div class="chart-container">
              <canvas id="salesTypeChart"></canvas>
            </div>
            <ul class="collection" style="margin-top: 10px; margin-bottom: 0;">
              <li class="collection-item">Service: {{ report.service_sales_amount }}</li>
              <li class="collection-item">Product: {{ report.product_sales_amount }}</li>
              <li class="collection-item">Package: {{ report.package_sales_amount }}</li>
            </ul>
          </div>
        </div>
      </div>
      <div class="col s6">
        <div class="card">
          <div class="card-content">
            <span class="card-title">Payment Methods</span>
            <div class="chart-container">
              <canvas id="paymentMethodChart"></canvas>
            </div>
            <ul class="collection" style="margin-top: 10px; margin-bottom: 0;">
              <li class="collection-item payment-method-item">
                <span>Cash</span><span>{{ report.cash_sales_amount }}</span>
              </li>
              <li class="collection-item payment-method-item">
                <span>Card</span><span>{{ report.card_sales_amount }}</span>
              </li>
              <li class="collection-item payment-method-item">
                <span>Link</span><span>{{ report.online_link_amount }}</span>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <!-- Deductions & Cash Register -->
    <div class="row">
      <div class="col s6">
        <div class="card">
          <div class="card-content">
            <span class="card-title">Deductions & Net Sales</span>
            <ul class="collection" style="margin-bottom: 0;">
              <li class="collection-item">VAT (5%): {{ report.vat_amount }}</li>
              <li class="collection-item">Card Charges: {{ report.card_charges_amount }}</li>
              <li class="collection-item">Link Charges: {{ report.link_charges_amount }}</li>
              <li class="collection-item summary">Net Sales: {{ report.net_sales_amount }}</li>
            </ul>
          </div>
        </div>
      </div>
      <div class="col s6">
        <div class="card">
          <div class="card-content">
            <span class="card-title">Cash Register</span>
            <ul class="collection" style="margin-bottom: 0;">
              <li class="collection-item">Starting Balance: {{ report.starting_cash_balance }}</li>
              <li class="collection-item">Cash Sales: {{ report.cash_sales_amount }}</li>
              <li class="collection-item negative">Expenses: {{ report.expenses_total }}</li>
              <li class="collection-item negative">Withdrawals: {{ report.cash_withdrawals_total }}</li>
              <li class="collection-item summary">Ending Balance: {{ report.ending_cash_balance }}</li>
            </ul>
          </div>
        </div>
      </div>
    </div>

    <div class="section-divider"></div>

    <!-- DAILY INSIGHTS SECTION -->
    <div class="section-header">
      <i class="material-icons small left">bar_chart</i> DAILY INSIGHTS
    </div>

    <!-- Top Products & Package Sales -->
    <div class="row">
      <div class="col s6">
        <div class="card">
          <div class="card-content">
            <span class="card-title">Top Products</span>
            <table class="compact-table striped">
              <thead>
                <tr>
                  <th>Product</th>
                  <th style="text-align: center;">Qty</th>
                  <th style="text-align: right;">Revenue</th>
                </tr>
              </thead>
              <tbody>
                {% if daily_insights.top_products %}
                  {% for product in daily_insights.top_products %}
                    <tr>
                      <td>{{ product.product__name }}</td>
                      <td style="text-align: center;">{{ product.quantity }}</td>
                      <td style="text-align: right;">{{ product.revenue }}</td>
                    </tr>
                  {% endfor %}
                {% else %}
                  <tr><td colspan="3">No product sales data</td></tr>
                {% endif %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
      <div class="col s6">
        <div class="card">
          <div class="card-content">
            <span class="card-title">Package Sales</span>
            <table class="compact-table striped">
              <thead>
                <tr>
                  <th>Package</th>
                  <th style="text-align: center;">Count</th>
                  <th style="text-align: right;">Total</th>
                </tr>
              </thead>
              <tbody>
                {% if daily_insights.package_breakdown %}
                  {% for package in daily_insights.package_breakdown %}
                    <tr>
                      <td>{{ package.package_option__package__name }}</td>
                      <td style="text-align: center;">{{ package.count }}</td>
                      <td style="text-align: right;">{{ package.total }}</td>
                    </tr>
                  {% endfor %}
                {% else %}
                  <tr><td colspan="3">No package sales data</td></tr>
                {% endif %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <!-- Hourly Sales -->
    <div class="card">
      <div class="card-content">
        <span class="card-title">Hourly Sales Distribution</span>
        <div class="chart-container">
          <canvas id="hourlyChart"></canvas>
        </div>
        <table class="compact-table striped" style="margin-top: 10px;">
          <thead>
            <tr>
              <th>Hour</th>
              <th>Service</th>
              <th>Product</th>
              <th>Package</th>
              <th>Total</th>
            </tr>
          </thead>
          <tbody>
            {% if daily_insights.hourly_sales %}
              {% for hour in daily_insights.hourly_sales %}
                <tr>
                  <td>{{ hour.display }}</td>
                  <td>{{ hour.service_sales }}</td>
                  <td>{{ hour.product_sales }}</td>
                  <td>{{ hour.package_sales }}</td>
                  <td><strong>{{ hour.total }}</strong></td>
                </tr>
              {% endfor %}
            {% else %}
              <tr><td colspan="5" style="text-align: center;">No hourly data available</td></tr>
            {% endif %}
          </tbody>
        </table>
      </div>
    </div>

    <!-- Discount & Payment Breakdown -->
    <div class="row">
      <div class="col s6">
        <div class="card">
          <div class="card-content">
            <span class="card-title">Discount Statistics</span>
            <ul class="collection" style="margin-bottom: 0;">
              <li class="collection-item">Services: {{ daily_insights.discount_statistics.discounted_services|default:0 }}</li>
              <li class="collection-item">Packages: {{ daily_insights.discount_statistics.discounted_packages|default:0 }}</li>
              <li class="collection-item">Total Count: {{ daily_insights.discount_statistics.total_discounts|default:0 }}</li>
              <li class="collection-item summary">Amount: {{ daily_insights.discount_statistics.discount_amounts|default:0 }}</li>
            </ul>
          </div>
        </div>
      </div>
      <div class="col s6">
        <div class="card">
          <div class="card-content">
            <span class="card-title">Payment Breakdown</span>
            <table class="compact-table striped">
              <thead>
                <tr>
                  <th>Method</th>
                  <th>Sale Type</th>
                  <th style="text-align: center;">Count</th>
                  <th style="text-align: right;">Total</th>
                </tr>
              </thead>
              <tbody>
                {% if daily_insights.payment_breakdown %}
                  {% for payment in daily_insights.payment_breakdown %}
                    <tr>
                      <td>{{ payment.payment_method_display }}</td>
                      <td>{{ payment.sale_type|title }}</td>
                      <td style="text-align: center;">{{ payment.count }}</td>
                      <td style="text-align: right;">{{ payment.total }}</td>
                    </tr>
                  {% endfor %}
                {% else %}
                  <tr><td colspan="4" style="text-align: center;">No payment data available</td></tr>
                {% endif %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>

    <div class="section-divider"></div>

   <!-- MONTHLY CONTEXT SECTION -->
<div class="section-header">
  <i class="material-icons small left">timeline</i> MONTHLY CONTEXT
</div>

<div class="row">
  <!-- Monthly Overview -->
  <div class="col s12">
    <div class="card">
      <div class="card-content">
        <span class="card-title">Monthly Overview: {{ monthly_context.month_name }} {{ monthly_context.year }}</span>
        <div class="row" style="margin-bottom: 0;">
      
          <div class="col s4">
            <div class="stats-box">
              <i class="material-icons small">attach_money</i>
              <h5>Monthly Gross</h5>
              <h4>{{ monthly_context.totals.gross_sales }}</h4>
            </div>
          </div>
          <div class="col s4">
            <div class="stats-box">
              <i class="material-icons small">account_balance</i>
              <h5>Monthly Net</h5>
              <h4>{{ monthly_context.totals.net_sales }}</h4>
            </div>
          </div>
          <div class="col s4">
            <div class="stats-box">
              <i class="material-icons small">trending_up</i>
              <h5>Daily Average</h5>
              <h4>{{ monthly_context.averages.gross_sales|floatformat:2 }}</h4>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Growth Stats & Monthly Trends -->
<div class="row">
  <!-- Growth Stats -->
  <div class="col s5">
    <div class="card">
      <div class="card-content">
        <span class="card-title">Growth vs Previous Month</span>
        <table class="compact-table striped">
          <thead>
            <tr>
              <th>Category</th>
              <th style="text-align: right;">Change</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Gross Sales</td>
              <td style="text-align: right;" class="{% if monthly_context.growth.gross_sales > 0 %}positive{% elif monthly_context.growth.gross_sales < 0 %}negative{% endif %}">
                {{ monthly_context.growth.gross_sales|floatformat:1 }}%
              </td>
            </tr>
            <tr>
              <td>Service Sales</td>
              <td style="text-align: right;" class="{% if monthly_context.growth.service_sales > 0 %}positive{% elif monthly_context.growth.service_sales < 0 %}negative{% endif %}">
                {{ monthly_context.growth.service_sales|floatformat:1 }}%
              </td>
            </tr>
            <tr>
              <td>Product Sales</td>
              <td style="text-align: right;" class="{% if monthly_context.growth.product_sales > 0 %}positive{% elif monthly_context.growth.product_sales < 0 %}negative{% endif %}">
                {{ monthly_context.growth.product_sales|floatformat:1 }}%
              </td>
            </tr>
            <tr>
              <td>Package Sales</td>
              <td style="text-align: right;" class="{% if monthly_context.growth.package_sales > 0 %}positive{% elif monthly_context.growth.package_sales < 0 %}negative{% endif %}">
                {{ monthly_context.growth.package_sales|floatformat:1 }}%
              </td>
            </tr>
            <tr>
              <td>Net Sales</td>
              <td style="text-align: right;" class="{% if monthly_context.growth.net_sales > 0 %}positive{% elif monthly_context.growth.net_sales < 0 %}negative{% endif %}">
                {{ monthly_context.growth.net_sales|floatformat:1 }}%
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
  
  <!-- Monthly Trend Chart -->
  <div class="col s7">
    <div class="card">
      <div class="card-content">
        <span class="card-title">Daily Sales Trend</span>
        <div class="chart-container" style="height: 220px;">
          <canvas id="monthlyTrendChart"></canvas>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Distribution Charts -->
<div class="row">
  <!-- Sales Type Distribution -->
  <div class="col s6">
    <div class="card">
      <div class="card-content">
        <span class="card-title">Sales Distribution</span>
        <div class="chart-container">
          <canvas id="salesDistributionChart"></canvas>
        </div>
        <ul class="collection" style="margin-top: 10px; margin-bottom: 0;">
          {% for item in monthly_context.sales_distribution %}
          <li class="collection-item payment-method-item">
            <span>{{ item.type }}</span>
            <span>{{ item.amount }} ({{ item.percentage|floatformat:1 }}%)</span>
          </li>
          {% endfor %}
        </ul>
      </div>
    </div>
  </div>
  
  <!-- Payment Method Distribution -->
  <div class="col s6">
    <div class="card">
      <div class="card-content">
        <span class="card-title">Payment Methods</span>
        <div class="chart-container">
          <canvas id="monthlyPaymentChart"></canvas>
        </div>
        <ul class="collection" style="margin-top: 10px; margin-bottom: 0;">
          {% for item in monthly_context.payment_distribution %}
          <li class="collection-item payment-method-item">
            <span>{{ item.method }}</span>
            <span>{{ item.amount }} ({{ item.percentage|floatformat:1 }}%)</span>
          </li>
          {% endfor %}
        </ul>
      </div>
    </div>
  </div>
</div>

<!-- Monthly Totals -->
<div class="row">
  <div class="col s12">
    <div class="card">
      <div class="card-content">
        <span class="card-title">Monthly Detailed Totals</span>
        <table class="compact-table striped">
          <thead>
            <tr>
              <th>Category</th>
              <th style="text-align: right;">Total</th>
              <th style="text-align: right;">Daily Average</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>Service Sales</td>
              <td style="text-align: right;">{{ monthly_context.totals.service_sales }}</td>
              <td style="text-align: right;">{{ monthly_context.averages.service_sales|floatformat:2 }}</td>
            </tr>
            <tr>
              <td>Product Sales</td>
              <td style="text-align: right;">{{ monthly_context.totals.product_sales }}</td>
              <td style="text-align: right;">{{ monthly_context.averages.product_sales|floatformat:2 }}</td>
            </tr>
            <tr>
              <td>Package Sales</td>
              <td style="text-align: right;">{{ monthly_context.totals.package_sales }}</td>
              <td style="text-align: right;">{{ monthly_context.averages.package_sales|floatformat:2 }}</td>
            </tr>
            <tr>
              <td>Gross Sales</td>
              <td style="text-align: right;"><strong>{{ monthly_context.totals.gross_sales }}</strong></td>
              <td style="text-align: right;"><strong>{{ monthly_context.averages.gross_sales|floatformat:2 }}</strong></td>
            </tr>
            <tr>
              <td>VAT</td>
              <td style="text-align: right;" class="negative">{{ monthly_context.totals.vat }}</td>
              <td style="text-align: right;" class="negative">{{ monthly_context.averages.vat|floatformat:2 }}</td>
            </tr>
            <tr>
              <td>Card Charges</td>
              <td style="text-align: right;" class="negative">{{ monthly_context.totals.card_charges }}</td>
              <td style="text-align: right;" class="negative">{{ monthly_context.averages.card_charges|floatformat:2 }}</td>
            </tr>
            <tr>
              <td>Link Charges</td>
              <td style="text-align: right;" class="negative">{{ monthly_context.totals.link_charges }}</td>
              <td style="text-align: right;" class="negative">{{ monthly_context.averages.link_charges|floatformat:2 }}</td>
            </tr>
            <tr>
              <td>Expenses</td>
              <td style="text-align: right;" class="negative">{{ monthly_context.totals.expenses }}</td>
              <td style="text-align: right;" class="negative">{{ monthly_context.averages.expenses|floatformat:2 }}</td>
            </tr>
            <tr>
              <td>Withdrawals</td>
              <td style="text-align: right;" class="negative">{{ monthly_context.totals.withdrawals }}</td>
              <td style="text-align: right;" class="negative">{{ monthly_context.averages.withdrawals|floatformat:2 }}</td>
            </tr>
            <tr>
              <td>Net Sales</td>
              <td style="text-align: right;"><strong>{{ monthly_context.totals.net_sales }}</strong></td>
              <td style="text-align: right;"><strong>{{ monthly_context.averages.net_sales|floatformat:2 }}</strong></td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>

    <div class="section-divider"></div>

<!-- SALES TRENDS SECTION -->
<div class="section-header">
  <i class="material-icons small left">trending_up</i> SALES TRENDS
  <span class="right" style="font-size: 0.85rem; font-weight: normal; margin-right: 10px;">
    Period: {{ date|date:"M d, Y" }} (Data from {{ first_day_of_month|date:"M d, Y" }} to {{ date|date:"M d, Y" }})
  </span>
</div>

<div class="row">
  <!-- Performance KPIs -->
  <div class="col s12">
    <div class="card">
      <div class="card-content">
        <span class="card-title">Performance Indicators</span>
        <div class="row" style="margin-bottom: 0;">
          <div class="col s3">
            <div class="stats-box">
              <i class="material-icons small">trending_up</i>
              <h5>Selected Day vs. Month Avg</h5>
              <h4 class="{% if sales_trends.day_vs_month_avg > 0 %}positive{% elif sales_trends.day_vs_month_avg < 0 %}negative{% endif %}">
                {{ sales_trends.day_vs_month_avg|floatformat:1 }}%
              </h4>
            </div>
          </div>
          <div class="col s3">
            <div class="stats-box">
              <i class="material-icons small">swap_horiz</i>
              <h5>vs. Same Day Last Week</h5>
              <h4 class="{% if sales_trends.day_vs_prev_week > 0 %}positive{% elif sales_trends.day_vs_prev_week < 0 %}negative{% endif %}">
                {{ sales_trends.day_vs_prev_week|floatformat:1 }}%
              </h4>
            </div>
          </div>
          <div class="col s3">
            <div class="stats-box">
              <i class="material-icons small">assessment</i>
              <h5>Week-over-Week</h5>
              <h4 class="{% if sales_trends.week_over_week > 0 %}positive{% elif sales_trends.week_over_week < 0 %}negative{% endif %}">
                {{ sales_trends.week_over_week|floatformat:1 }}%
              </h4>
            </div>
          </div>
          <div class="col s3">
            <div class="stats-box">
              <i class="material-icons small">insert_chart</i>
              <h5>Month-over-Month</h5>
              <h4 class="{% if sales_trends.month_over_month > 0 %}positive{% elif sales_trends.month_over_month < 0 %}negative{% endif %}">
                {{ sales_trends.month_over_month|floatformat:1 }}%
              </h4>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <!-- Monthly Progress -->
  <div class="col s6">
    <div class="card">
      <div class="card-content">
        <span class="card-title">Month-to-Date Progress</span>
        <div class="progress" style="height: 15px; margin-bottom: 20px;">
          <div class="determinate" style="width: {{ sales_trends.month_progress|floatformat:0 }}%; background-color: #4CAF50;"></div>
        </div>
        <div class="row" style="margin-bottom: 0;">
          <div class="col s6">
            <div class="stats-box">
              <h5>Current Progress</h5>
              <h4>{{ sales_trends.month_progress|floatformat:1 }}%</h4>
              <p class="grey-text">Day {{ date.day }} of {{ days_in_month }}</p>
            </div>
          </div>
          <div class="col s6">
            <div class="stats-box">
              <h5>Month-to-Date Sales</h5>
              <h4>{{ sales_trends.current_total|floatformat:2 }}</h4>
              <p class="grey-text">Target: {{ sales_trends.monthly_target|floatformat:0 }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Weekly Pattern -->
  <div class="col s6">
    <div class="card">
      <div class="card-content">
        <span class="card-title">Weekly Sales Pattern</span>
        <div class="chart-container">
          <canvas id="weeklyPatternChart"></canvas>
        </div>
      </div>
    </div>
  </div>
</div>

<div class="row">
  <!-- Monthly Projection -->
  <div class="col s6">
    <div class="card">
      <div class="card-content">
        <span class="card-title">Monthly Projection</span>
        <div class="chart-container">
          <canvas id="forecastChart"></canvas>
        </div>
        <ul class="collection" style="margin-top: 15px; margin-bottom: 0;">
          <li class="collection-item">Current: {{ sales_trends.current_total|floatformat:2 }} ({{ sales_trends.current_achievement|floatformat:1 }}% of target)</li>
          <li class="collection-item">Projected: {{ sales_trends.projected_total|floatformat:2 }}</li>
          <li class="collection-item" 
              class="{% if sales_trends.target_achievement > 100 %}positive{% elif sales_trends.target_achievement < 90 %}negative{% endif %}">
            Target Achievement: {{ sales_trends.target_achievement|floatformat:1 }}%
          </li>
        </ul>
      </div>
    </div>
  </div>
  
  <!-- Month Comparison -->
  <div class="col s6">
    <div class="card">
      <div class="card-content">
        <span class="card-title">4-Week Comparison</span>
        <div class="chart-container">
          <canvas id="fourWeekComparisonChart"></canvas>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Sales Category Trends -->
<div class="card">
  <div class="card-content">
    <span class="card-title">
      Sales Category Trends
      <span class="grey-text" style="font-size: 0.85rem; font-weight: normal; margin-left: 10px;">
        ({{ first_day_of_month|date:"M d" }} to {{ date|date:"M d, Y" }})
      </span>
    </span>
    <div class="chart-container" style="height: 250px;">
      <canvas id="categoryTrendsChart"></canvas>
    </div>
    <div class="row" style="margin-top: 15px;">
      <div class="col s4">
        <div class="stats-box">
          <h5>Service Sales Trend</h5>
          <h4 class="{% if sales_trends.category_trends.service > 0 %}positive{% elif sales_trends.category_trends.service < 0 %}negative{% endif %}">
            {{ sales_trends.category_trends.service|floatformat:1 }}%
          </h4>
        </div>
      </div>
      <div class="col s4">
        <div class="stats-box">
          <h5>Product Sales Trend</h5>
          <h4 class="{% if sales_trends.category_trends.product > 0 %}positive{% elif sales_trends.category_trends.product < 0 %}negative{% endif %}">
            {{ sales_trends.category_trends.product|floatformat:1 }}%
          </h4>
        </div>
      </div>
      <div class="col s4">
        <div class="stats-box">
          <h5>Package Sales Trend</h5>
          <h4 class="{% if sales_trends.category_trends.package > 0 %}positive{% elif sales_trends.category_trends.package < 0 %}negative{% endif %}">
            {{ sales_trends.category_trends.package|floatformat:1 }}%
          </h4>
        </div>
      </div>
    </div>
  </div>
</div>

    <!-- Report Footer -->
    <div class="card">
      <div class="card-content">
        <div class="row" style="margin-bottom: 0;">
          <div class="col s4">
            <p><strong>Created By:</strong> {{ report.created_by_name }}</p>
          </div>
          <div class="col s4">
            <p><strong>Email Sent:</strong> {% if report.email_sent %}Yes{% else %}No{% endif %}</p>
          </div>
          <div class="col s4">
            <p><strong>Notes:</strong> {{ notes }}</p>
          </div>
        </div>
      </div>
    </div>

    <div class="center-align hide-print" style="margin: 20px 0">
      <a class="waves-effect waves-light btn" onclick="window.print()">
        <i class="material-icons left">print</i>Print Report
      </a>
    </div>
  </div>

  <!-- Materialize JS CDN -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/materialize/1.0.0/js/materialize.min.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {

// Weekly Pattern Chart
var weeklyPatternCtx = document.getElementById('weeklyPatternChart').getContext('2d');
var weeklyPatternChart = new Chart(weeklyPatternCtx, {
  type: 'bar',
  data: {
    labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
    datasets: [{
      label: 'Average Daily Sales',
      data: [
        {{ sales_trends.weekly_pattern.mon|floatformat:2 }},
        {{ sales_trends.weekly_pattern.tue|floatformat:2 }},
        {{ sales_trends.weekly_pattern.wed|floatformat:2 }},
        {{ sales_trends.weekly_pattern.thu|floatformat:2 }},
        {{ sales_trends.weekly_pattern.fri|floatformat:2 }},
        {{ sales_trends.weekly_pattern.sat|floatformat:2 }},
        {{ sales_trends.weekly_pattern.sun|floatformat:2 }}
      ],
      backgroundColor: 'rgba(76, 175, 80, 0.7)'
    }]
  },
  options: {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      y: { 
        beginAtZero: true,
        ticks: {
          callback: function(value) {
            return value.toLocaleString();
          }
        }
      }
    },
    plugins: {
      legend: { display: false },
      tooltip: {
        callbacks: {
          label: function(context) {
            return context.parsed.y.toLocaleString();
          }
        }
      }
    }
  }
});

// 4-Week Comparison Chart
var fourWeekCtx = document.getElementById('fourWeekComparisonChart').getContext('2d');
var fourWeekChart = new Chart(fourWeekCtx, {
  type: 'line',
  data: {
    labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4'],
    datasets: [{
      label: 'This Month',
      data: [
        {{ sales_trends.four_week_comparison.current.week1|floatformat:2 }},
        {{ sales_trends.four_week_comparison.current.week2|floatformat:2 }},
        {{ sales_trends.four_week_comparison.current.week3|floatformat:2 }},
        {{ sales_trends.four_week_comparison.current.week4|floatformat:2 }}
      ],
      borderColor: '#2196F3',
      backgroundColor: 'rgba(33, 150, 243, 0.1)',
      tension: 0.1,
      fill: true
    },
    {
      label: 'Previous Month',
      data: [
        {{ sales_trends.four_week_comparison.previous.week1|floatformat:2 }},
        {{ sales_trends.four_week_comparison.previous.week2|floatformat:2 }},
        {{ sales_trends.four_week_comparison.previous.week3|floatformat:2 }},
        {{ sales_trends.four_week_comparison.previous.week4|floatformat:2 }}
      ],
      borderColor: '#9C27B0',
      backgroundColor: 'rgba(156, 39, 176, 0.1)',
      tension: 0.1,
      fill: true,
      borderDash: [5, 5]
    }]
  },
  options: {
    responsive: true,
    maintainAspectRatio: false,
    interaction: { mode: 'index', intersect: false },
    scales: {
      y: { 
        beginAtZero: true,
        ticks: {
          callback: function(value) {
            return value.toLocaleString();
          }
        }
      }
    },
    plugins: {
      tooltip: {
        callbacks: {
          label: function(context) {
            return context.dataset.label + ': ' + context.parsed.y.toLocaleString();
          }
        }
      }
    }
  }
});

// Category Trends Chart
var categoryTrendsCtx = document.getElementById('categoryTrendsChart').getContext('2d');
var categoryTrendsChart = new Chart(categoryTrendsCtx, {
  type: 'line',
  data: {
    labels: {{ sales_trends.category_trends.dates|safe }},
    datasets: [
      {
        label: 'Services',
        data: {{ sales_trends.category_trends.service_data|safe }},
        borderColor: '#4CAF50',
        backgroundColor: 'rgba(76, 175, 80, 0.1)',
        tension: 0.1,
        fill: true
      },
      {
        label: 'Products',
        data: {{ sales_trends.category_trends.product_data|safe }},
        borderColor: '#2196F3',
        backgroundColor: 'rgba(33, 150, 243, 0.1)',
        tension: 0.1,
        fill: true
      },
      {
        label: 'Packages',
        data: {{ sales_trends.category_trends.package_data|safe }},
        borderColor: '#FF9800',
        backgroundColor: 'rgba(255, 152, 0, 0.1)',
        tension: 0.1,
        fill: true
      }
    ]
  },
  options: {
    responsive: true,
    maintainAspectRatio: false,
    interaction: { mode: 'index', intersect: false },
    scales: {
      y: { 
        beginAtZero: true,
        ticks: {
          callback: function(value) {
            return value.toLocaleString();
          }
        }
      },
      x: { 
        title: {
          display: true,
          text: 'Day of Month'
        }
      }
    },
    plugins: {
      tooltip: {
        callbacks: {
          label: function(context) {
            return context.dataset.label + ': ' + context.parsed.y.toLocaleString();
          }
        }
      }
    }
  }
});

// Forecast Chart
var forecastCtx = document.getElementById('forecastChart').getContext('2d');
var forecastChart = new Chart(forecastCtx, {
  type: 'bar',
  data: {
    labels: ['Current', 'Projected', 'Target'],
    datasets: [{
      data: [
        {{ sales_trends.current_total|floatformat:2 }},
        {{ sales_trends.projected_total|floatformat:2 }},
        {{ sales_trends.monthly_target|floatformat:2 }}
      ],
      backgroundColor: [
        'rgba(76, 175, 80, 0.8)',
        'rgba(255, 152, 0, 0.8)',
        'rgba(33, 150, 243, 0.8)'
      ]
    }]
  },
  options: {
    responsive: true,
    maintainAspectRatio: false,
    scales: {
      y: { 
        beginAtZero: true,
        ticks: {
          callback: function(value) {
            return value.toLocaleString();
          }
        }
      }
    },
    plugins: {
      legend: { display: false },
      tooltip: {
        callbacks: {
          label: function(context) {
            return context.label + ': ' + context.parsed.y.toLocaleString();
          }
        }
      }
    }
  }
});

// Monthly Trend Chart
var monthlyTrendCtx = document.getElementById('monthlyTrendChart').getContext('2d');
var monthlyDates = [];
var monthlySalesData = [];
var monthlyNetData = [];

{% for day in monthly_context.daily_data %}
monthlyDates.push('{{ day.date|date:"d" }}');
monthlySalesData.push({{ day.gross_sales }});
monthlyNetData.push({{ day.net_sales }});
{% endfor %}

var monthlyTrendChart = new Chart(monthlyTrendCtx, {
  type: 'line',
  data: {
    labels: monthlyDates,
    datasets: [
      {
        label: 'Gross Sales',
        data: monthlySalesData,
        borderColor: '#4CAF50',
        backgroundColor: 'rgba(76, 175, 80, 0.1)',
        tension: 0.1,
        fill: true
      },
      {
        label: 'Net Sales',
        data: monthlyNetData,
        borderColor: '#2196F3',
        backgroundColor: 'rgba(33, 150, 243, 0.1)',
        tension: 0.1,
        fill: true
      }
    ]
  },
  options: {
    responsive: true,
    maintainAspectRatio: false,
    interaction: { mode: 'index', intersect: false },
    scales: {
      y: { beginAtZero: true },
      x: { ticks: { maxRotation: 0, minRotation: 0, font: { size: 9 } } }
    },
    plugins: {
      legend: { position: 'top', labels: { boxWidth: 12, font: { size: 10 } } },
      tooltip: {
        callbacks: {
          title: function(tooltipItem) {
            return 'Day ' + tooltipItem[0].label;
          }
        }
      }
    }
  }
});

// Sales Distribution Chart
var salesDistCtx = document.getElementById('salesDistributionChart').getContext('2d');
var salesDistLabels = [];
var salesDistValues = [];
var salesDistColors = ['#4CAF50', '#2196F3', '#FF9800'];

{% for item in monthly_context.sales_distribution %}
salesDistLabels.push('{{ item.type }}');
salesDistValues.push({{ item.percentage }});
{% endfor %}

var salesDistChart = new Chart(salesDistCtx, {
  type: 'pie',
  data: {
    labels: salesDistLabels,
    datasets: [{
      data: salesDistValues,
      backgroundColor: salesDistColors,
    }]
  },
  options: {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: { position: 'bottom', labels: { boxWidth: 12, font: { size: 11 } } },
      tooltip: {
        callbacks: {
          label: function(context) {
            return context.label + ': ' + context.parsed.toFixed(1) + '%';
          }
        }
      }
    }
  }
});

// Monthly Payment Method Chart
var monthlyPaymentCtx = document.getElementById('monthlyPaymentChart').getContext('2d');
var paymentLabels = [];
var paymentValues = [];
var paymentColors = ['#FFC107', '#9C27B0', '#3F51B5'];

{% for item in monthly_context.payment_distribution %}
paymentLabels.push('{{ item.method }}');
paymentValues.push({{ item.percentage }});
{% endfor %}

var monthlyPaymentChart = new Chart(monthlyPaymentCtx, {
  type: 'doughnut',
  data: {
    labels: paymentLabels,
    datasets: [{
      data: paymentValues,
      backgroundColor: paymentColors,
    }]
  },
  options: {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: { position: 'bottom', labels: { boxWidth: 12, font: { size: 11 } } },
      tooltip: {
        callbacks: {
          label: function(context) {
            return context.label + ': ' + context.parsed.toFixed(1) + '%';
          }
        }
      }
    }
  }
});

      // Sales Type Chart
      var salesTypeCtx = document.getElementById('salesTypeChart').getContext('2d');
      var salesTypeChart = new Chart(salesTypeCtx, {
        type: 'pie',
        data: {
          labels: ['Service', 'Product', 'Package'],
          datasets: [{
            data: [{{ report.service_sales_amount }}, {{ report.product_sales_amount }}, {{ report.package_sales_amount }}],
            backgroundColor: ['#4CAF50', '#2196F3', '#FF9800'],
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: { position: 'bottom', labels: { boxWidth: 12, font: { size: 11 } } },
          }
        }
      });

      // Payment Method Chart
      var paymentCtx = document.getElementById('paymentMethodChart').getContext('2d');
      var paymentChart = new Chart(paymentCtx, {
        type: 'doughnut',
        data: {
          labels: ['Cash', 'Card', 'Link'],
          datasets: [{
            data: [{{ report.cash_sales_amount }}, {{ report.card_sales_amount }}, {{ report.online_link_amount }}],
            backgroundColor: ['#FFC107', '#9C27B0', '#3F51B5'],
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: { position: 'bottom', labels: { boxWidth: 12, font: { size: 11 } } },
          }
        }
      });

      // Hourly Chart (if data available)
      {% if daily_insights.hourly_sales %}
      var hourLabels = [];
      var serviceData = [];
      var productData = [];
      var packageData = [];

      {% for hour in daily_insights.hourly_sales %}
      hourLabels.push('{{ hour.display }}');
      serviceData.push({{ hour.service_sales }});
      productData.push({{ hour.product_sales }});
      packageData.push({{ hour.package_sales }});
      {% endfor %}

      var hourlyCtx = document.getElementById('hourlyChart').getContext('2d');
      var hourlyChart = new Chart(hourlyCtx, {
        type: 'line',
        data: {
          labels: hourLabels,
          datasets: [
            {
              label: 'Service',
              data: serviceData,
              borderColor: '#4CAF50',
              backgroundColor: 'rgba(76, 175, 80, 0.1)',
              tension: 0.1,
              fill: true
            },
            {
              label: 'Product',
              data: productData,
              borderColor: '#2196F3',
              backgroundColor: 'rgba(33, 150, 243, 0.1)',
              tension: 0.1,
              fill: true
            },
            {
              label: 'Package',
              data: packageData,
              borderColor: '#FF9800',
              backgroundColor: 'rgba(255, 152, 0, 0.1)',
              tension: 0.1,
              fill: true
            }
          ]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          interaction: { mode: 'index', intersect: false },
          scales: {
            y: { beginAtZero: true },
            x: { ticks: { maxRotation: 45, minRotation: 45, font: { size: 9 } } }
          },
          plugins: {
            legend: { position: 'top', labels: { boxWidth: 12, font: { size: 11 } } },
          }
        }
      });
      {% endif %}

      // Helper function to get CSRF token
      function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
          const cookies = document.cookie.split(';');
          for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
              cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
              break;
            }
          }
        }
        return cookieValue;
      }
    });
  </script>
</body>
</html>