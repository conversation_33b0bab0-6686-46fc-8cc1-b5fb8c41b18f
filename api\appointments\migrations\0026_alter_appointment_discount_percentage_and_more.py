# Generated by Django 4.2.19 on 2025-04-11 20:47

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('services', '0002_servicepackage_shared_servicepackage_unlimited'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('appointments', '0025_alter_activity_activity_type_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='appointment',
            name='discount_percentage',
            field=models.DecimalField(decimal_places=5, default=0, max_digits=10),
        ),
        migrations.AlterField(
            model_name='discount',
            name='discount_percentage',
            field=models.DecimalField(decimal_places=5, max_digits=10),
        ),
        migrations.CreateModel(
            name='Sale',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('sale_type', models.CharField(choices=[('package', 'Package Sale'), ('service', 'Service Sale (Appointment)')], max_length=20)),
                ('total_price', models.DecimalField(decimal_places=2, max_digits=10)),
                ('discount_percentage', models.DecimalField(decimal_places=5, default=0, max_digits=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('appointment', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='sales', to='appointments.appointment')),
                ('discount', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='appointments.discount')),
                ('package_option', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='services.packageoption')),
                ('shared_package', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='sales', to='appointments.sharedpackage')),
                ('unlimited_package', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='sales', to='appointments.unlimitedpackage')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='sales', to=settings.AUTH_USER_MODEL)),
                ('user_package', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='sales', to='appointments.userpackage')),
            ],
        ),
    ]
