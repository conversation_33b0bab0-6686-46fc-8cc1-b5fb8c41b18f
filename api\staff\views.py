import calendar
from rest_framework import viewsets
from rest_framework.permissions import AllowAny
from rest_framework.decorators import action
from django.db.models import Count, Sum
from api.appointments.models import Activity, Appointment
from django.db.models.functions import ExtractMonth, ExtractYear
from api.authentication.models import User
from rest_framework.exceptions import APIException
from django.shortcuts import get_object_or_404
from .models import TherapistProfile, WorkingHour
from django.utils.timezone import now
from rest_framework import viewsets, status
from datetime import timedelta
from .serializers import (
    TherapistProfileSerializer,
    TherapistProfileCreateSerializer,
    TherapistProfileUpdateSerializer,
    WorkingHourSerializer,
    WorkingHourCreateUpdateSerializer,
    DashboardActivitySerializer,
    DashboardCustomerSerializer,
    TherapistProfileSerializer,
    WorkingHourSerializer,
    PublicTherapistProfileSerializer,
)
from api.core.permissions import IsOwner, IsReceptionist
from api.services.models import Service
from dateutil.relativedelta import relativedelta

from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework.exceptions import APIException
from rest_framework.filters import <PERSON>Filter
from django_filters.rest_framework import DjangoFilterBackend
from django.utils import timezone
from django.http import HttpResponse
from django.db.models import Sum, Q
from datetime import timedelta
import csv
import logging

from .models import TherapistProfile
from api.appointments.models import Appointment, Sale

from .serializers import TherapistProfileSerializer
from api.core.permissions import IsOwnerOrReceptionist


from rest_framework.decorators import action
from django.db.models import Count, Q, Sum, F
from dateutil.relativedelta import relativedelta
import calendar

from api.appointments.models import Appointment, Sale
from utils.sales_calculations import (
    calculate_service_sales,
    calculate_package_sales,
    calculate_product_sales,
    calculate_gross_sales,
    calculate_payment_method_totals,
)

# Set up logger
logger = logging.getLogger(__name__)


class TherapistManagementViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing therapist profiles with CRUD operations
    """

    queryset = TherapistProfile.objects.all()
    permission_classes = [IsAuthenticated, IsOwnerOrReceptionist]

    def get_serializer_class(self):
        if self.action == "create":
            return TherapistProfileCreateSerializer
        elif self.action in ["update", "partial_update"]:
            return TherapistProfileUpdateSerializer
        return TherapistProfileSerializer

    # In your view handling therapist updates
    def update(self, request, *args, **kwargs):
        therapist = self.get_object()

        # Extract user data
        user_data = {}
        for field in ["first_name", "last_name", "gender"]:
            if field in request.data:
                user_data[field] = request.data[field]

        # Update user if there's user data
        if user_data:
            user = therapist.user
            for attr, value in user_data.items():
                setattr(user, attr, value)
            user.save()

        # Continue with regular update
        return super().update(request, *args, **kwargs)

    def perform_destroy(self, instance):
        # This will cascade delete the user due to on_delete=CASCADE
        instance.user.delete()

    @action(detail=True, methods=["get"])
    def working_hours(self, request, pk=None):
        """
        Get all working hours for a specific therapist
        """
        therapist = self.get_object()
        hours = WorkingHour.objects.filter(therapist=therapist)
        serializer = WorkingHourSerializer(hours, many=True)
        return Response(serializer.data)


class WorkingHourManagementViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing therapist working hours
    """

    queryset = WorkingHour.objects.all()
    serializer_class = WorkingHourCreateUpdateSerializer
    permission_classes = [IsAuthenticated, IsOwnerOrReceptionist]

    def get_queryset(self):
        """
        Optionally filter working hours by therapist
        """
        queryset = WorkingHour.objects.all()
        therapist_id = self.request.query_params.get("therapist", None)
        if therapist_id:
            queryset = queryset.filter(therapist_id=therapist_id)
        return queryset

    @action(detail=False, methods=["post"], url_path="bulk-create")
    def bulk_create(self, request):
        """
        Create multiple working hours at once
        """
        serializer = self.get_serializer(data=request.data, many=True)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    @action(detail=False, methods=["delete"], url_path="bulk-delete")
    def bulk_delete(self, request):
        """
        Delete multiple working hours at once
        """
        ids = request.data.get("ids", [])
        if not ids:
            return Response(
                {"detail": "No IDs provided"}, status=status.HTTP_400_BAD_REQUEST
            )

        # Check permissions for each working hour
        for hour_id in ids:
            hour = get_object_or_404(WorkingHour, id=hour_id)
            self.check_object_permissions(request, hour)

        # Delete working hours
        WorkingHour.objects.filter(id__in=ids).delete()
        return Response(status=status.HTTP_204_NO_CONTENT)


class TherapistPublicProfileViewSet(viewsets.ModelViewSet):
    def get_queryset(self):
        """
        Return only active therapists for public views.
        Return all therapists for admin/staff users.
        """
        queryset = TherapistProfile.objects.all()

        # For anonymous users or regular users, only show active therapists
        if self.request.user.is_anonymous or not (
            self.request.user.is_staff or self.request.user.is_superuser
        ):
            queryset = queryset.filter(is_active=True)

        return queryset

    def get_serializer_class(self):
        if self.action == "list" and self.request.user.is_anonymous:
            return PublicTherapistProfileSerializer
        return TherapistProfileSerializer

    def get_permissions(self):
        if self.action in ["list", "retrieve"]:
            permission_classes = [AllowAny]
        else:
            permission_classes = [IsOwner | IsReceptionist]
        return [permission() for permission in permission_classes]


class TherapistProfileViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint that allows therapist profiles to be viewed.
    Includes filtering by date range for appointment
    """

    serializer_class = TherapistProfileSerializer
    permission_classes = [IsAuthenticated, IsOwnerOrReceptionist]
    filter_backends = [DjangoFilterBackend, SearchFilter]
    search_fields = [
        "user__first_name",
        "user__last_name",
        "user__email",
        "qualifications",
    ]

    def get_queryset(self):
        """
        Return all therapist profiles with appointment
        """
        try:
            queryset = TherapistProfile.objects.all().select_related("user")

            # Get date range from query parameters
            start_date = self.request.query_params.get("start_date")
            end_date = self.request.query_params.get("end_date")

            # Apply custom filtering based on date range
            if start_date or end_date:
                # We'll annotate the queryset with appointment stats in the list method
                pass

            return queryset

        except Exception as e:
            logger.error(
                f"Error in TherapistProfileViewSet get_queryset: {str(e)}", exc_info=True
            )
            raise APIException(f"Internal Server Error: {str(e)}")

    def list(self, request, *args, **kwargs):
        """
        Override list method to add appointment statistics.
        """
        try:
            # Get queryset
            queryset = self.filter_queryset(self.get_queryset())

            # Get date range parameters
            start_date = request.query_params.get("start_date")
            end_date = request.query_params.get("end_date")

            # Default to last 30 days if no dates provided
            if not start_date:
                start_date = (timezone.now() - timedelta(days=30)).date().isoformat()
            if not end_date:
                end_date = timezone.now().date().isoformat()

            # Handle export if requested
            export_format = request.query_params.get("export")
            if export_format == "csv":
                return self._export_to_csv(queryset, start_date, end_date)

            # Fetch data with statistics for each therapist
            therapists_data = []
            for therapist in queryset:
                # Get appointment statistics for the date range
                appointments = Appointment.objects.filter(
                    therapist=therapist, date__gte=start_date, date__lte=end_date
                )
                total_appointments = appointments.count()

                # Serialize the therapist
                serializer = self.get_serializer(therapist)
                therapist_data = serializer.data

                # Add statistics
                therapist_data["total_appointments"] = total_appointments

                therapists_data.append(therapist_data)

            return Response(therapists_data)

        except Exception as e:
            logger.error(
                f"Error in TherapistProfileViewSet list: {str(e)}", exc_info=True
            )
            return Response(
                {"detail": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def _export_to_csv(self, queryset, start_date, end_date):
        """
        Export therapists to CSV with appointment statistics
        """
        response = HttpResponse(content_type="text/csv")
        filename = f"therapists_{timezone.now().strftime('%Y%m%d')}.csv"
        response["Content-Disposition"] = f'attachment; filename="{filename}"'

        writer = csv.writer(response)

        # Write headers
        writer.writerow(
            [
                "ID",
                "First Name",
                "Last Name",
                "Email",
                "Phone",
                "Qualifications",
                "Gender Preference",
                "Location",
                "Total Appointments",
                "Services",
            ]
        )

        # Write data rows for each therapist
        for therapist in queryset:
            # Get appointment statistics for the date range
            appointments = Appointment.objects.filter(
                therapist=therapist, date__gte=start_date, date__lte=end_date
            )
            total_appointments = appointments.count()

            # Get services as comma-separated string
            services = ", ".join([service.name for service in therapist.services.all()])

            # Write the row
            writer.writerow(
                [
                    therapist.id,
                    therapist.user.first_name,
                    therapist.user.last_name,
                    therapist.user.email,
                    therapist.user.phone_number,
                    therapist.qualifications or "",
                    therapist.gender_preference or "",
                    therapist.location or "",
                    total_appointments,
                    services,
                ]
            )

        return response


class WorkingHourViewSet(viewsets.ModelViewSet):
    queryset = WorkingHour.objects.all()
    serializer_class = WorkingHourSerializer
    permission_classes = [IsOwner | IsReceptionist]


class DashboardActivityViewSet(viewsets.ModelViewSet):
    queryset = Activity.objects.all().order_by("-created_at")[:5]
    serializer_class = DashboardActivitySerializer
    permission_classes = [IsOwner | IsReceptionist]


class DashboardCustomerViewSet(viewsets.ModelViewSet):
    queryset = User.objects.filter(role="customer")
    serializer_class = DashboardCustomerSerializer
    permission_classes = [IsOwner | IsReceptionist]

    def list(self, request):
        today = now().date()
        start_of_month = today.replace(day=1)
        # Get 30 customers who have made a booking this month
        customers_with_bookings = (
            User.objects.filter(role="customer", appointments__date__gte=start_of_month)
            .annotate(booking_count=Count("appointments"))
            .order_by("-booking_count")[:30]
        )
        serializer = DashboardCustomerSerializer(customers_with_bookings, many=True)
        return Response(serializer.data, status=200)


class DashboardAppointmentsViewSet(viewsets.ViewSet):
    permission_classes = [IsOwner | IsReceptionist]

    def list(self, request):
        try:
            today = now().date()
            one_week_ago = now() - timedelta(days=7)

            # Calculate GROSS revenue for today (all locations combined)
            service_revenue_today = calculate_service_sales(date=today)
            package_revenue_today = calculate_package_sales(date=today)
            product_revenue_today = calculate_product_sales(date=today)
            gross_revenue_today = (
                service_revenue_today + package_revenue_today + product_revenue_today
            )

            # Calculate all-time GROSS revenue (all locations combined)
            all_service_revenue = calculate_service_sales()
            all_package_revenue = calculate_package_sales()
            all_product_revenue = calculate_product_sales()
            all_gross_revenue = (
                all_service_revenue + all_package_revenue + all_product_revenue
            )

            # Count appointments for today (all locations)
            appointments_today = Appointment.objects.filter(
                date=today, status="check_in"
            ).count()

            # All appointments (all locations)
            all_appointments = Appointment.objects.filter(status="check_in").count()

            # Customers
            today_customers = User.objects.filter(
                role="customer", created_at__date=today
            ).count()
            new_customers = User.objects.filter(
                role="customer", created_at__gte=one_week_ago
            ).count()
            all_customers = User.objects.filter(role="customer").count()

            # Count all services
            from api.appointments.models import Service

            all_services = Service.objects.all().count()

            return Response(
                {
                    "appointments_today": appointments_today,
                    "revenue_today": gross_revenue_today,  # GROSS revenue
                    "gross_revenue_today": gross_revenue_today,
                    "all_appointments": all_appointments,
                    "all_product_revenue": all_product_revenue,  # GROSS
                    "all_service_revenue": all_service_revenue,  # GROSS
                    "all_package_revenue": all_package_revenue,  # GROSS
                    "all_revenue": all_gross_revenue,  # GROSS
                    "today_customers": today_customers,
                    "new_customers": new_customers,
                    "all_customers": all_customers,
                    "all_services": all_services,
                },
                status=status.HTTP_200_OK,
            )
        except Exception as e:
            from rest_framework.exceptions import APIException

            raise APIException(f"Internal Server Error: {str(e)}")


class DashboardRevenueViewSet(viewsets.ViewSet):
    permission_classes = [IsOwner | IsReceptionist]

    def list(self, request):
        try:
            today = now().date()
            nine_months_ago = today - relativedelta(months=9)

            # Generate last 9 months
            last_9_months = [
                (today - relativedelta(months=i)).strftime("%b %Y") for i in range(9)
            ][::-1]

            # Prepare revenue data structure
            revenue_data = {
                month: {
                    "services": {"appointments": 0, "revenue": 0},
                    "packages": {"appointments": 0, "revenue": 0},
                    "product_sales": 0,
                    "total_appointments": 0,
                }
                for month in last_9_months
            }

            # Process each month
            for i in range(9):
                month_date = today - relativedelta(months=i)
                month_str = month_date.strftime("%b %Y")

                if month_str in revenue_data:
                    # Get first and last day of the month
                    first_day = month_date.replace(day=1)
                    if month_date.month == 12:
                        last_day = month_date.replace(
                            year=month_date.year + 1, month=1, day=1
                        ) - timedelta(days=1)
                    else:
                        last_day = month_date.replace(
                            month=month_date.month + 1, day=1
                        ) - timedelta(days=1)

                    # Don't go beyond today
                    last_day = min(last_day, today)

                    # Calculate revenues for the month (all locations)
                    service_revenue = calculate_service_sales(
                        start_date=first_day, end_date=last_day
                    )
                    package_revenue = calculate_package_sales(
                        start_date=first_day, end_date=last_day
                    )
                    product_revenue = calculate_product_sales(
                        start_date=first_day, end_date=last_day
                    )

                    # Count appointments/sales
                    service_count = Sale.objects.filter(
                        created_at__date__range=[first_day, last_day], sale_type="service"
                    ).count()

                    package_count = Sale.objects.filter(
                        created_at__date__range=[first_day, last_day], sale_type="package"
                    ).count()

                    # Update revenue data
                    revenue_data[month_str]["services"]["appointments"] = service_count
                    revenue_data[month_str]["services"]["revenue"] = float(
                        service_revenue
                    )
                    revenue_data[month_str]["packages"]["appointments"] = package_count
                    revenue_data[month_str]["packages"]["revenue"] = float(
                        package_revenue
                    )
                    revenue_data[month_str]["product_sales"] = float(product_revenue)
                    revenue_data[month_str]["total_appointments"] = (
                        service_count + package_count
                    )

            return Response(revenue_data, status=status.HTTP_200_OK)
        except Exception as e:
            from rest_framework.exceptions import APIException

            raise APIException(f"Internal Server Error: {str(e)}")
