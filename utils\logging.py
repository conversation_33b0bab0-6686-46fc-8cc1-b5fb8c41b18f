import logging
import json
import traceback
from datetime import datetime, timedelta
from decimal import Decimal, InvalidOperation

# Get appropriate loggers
logger = logging.getLogger("django")
api_logger = logging.getLogger("api")


# Helper functions for secure logging
def sanitize_data(data, sensitive_fields=None):
    """Sanitize potentially sensitive data before logging"""
    if sensitive_fields is None:
        sensitive_fields = ["password", "token", "secret", "key", "auth"]

    if not data:
        return {}

    if isinstance(data, dict):
        sanitized = {}
        for k, v in data.items():
            if any(field in k.lower() for field in sensitive_fields):
                sanitized[k] = "[REDACTED]"
            elif isinstance(v, (dict, list)):
                sanitized[k] = sanitize_data(v, sensitive_fields)
            else:
                sanitized[k] = v
        return sanitized
    elif isinstance(data, list):
        return [sanitize_data(item, sensitive_fields) for item in data]
    return data


def safe_json_serialize(obj):
    """Safely convert complex objects to JSON serializable format"""
    if isinstance(obj, (datetime, timedelta)):
        return obj.isoformat()
    elif isinstance(obj, Decimal):
        return float(obj)
    return str(obj)


def log_request_data(request, log_prefix="📥 Request"):
    """Log incoming request data safely"""
    try:
        # Get basic request info
        method = getattr(request, "method", "UNKNOWN")
        path = getattr(request, "path", "UNKNOWN")

        # Get user info safely
        user_info = "Anonymous"
        if hasattr(request, "user") and request.user:
            if request.user.is_authenticated:
                user_info = f"ID:{request.user.id} Role:{getattr(request.user, 'role', 'unknown')}"

        # Log basic request info first
        api_logger.info(f"{log_prefix}: {method} {path} | User: {user_info}")

        # Try to safely log body data
        if hasattr(request, "data") and request.data:
            try:
                sanitized_data = sanitize_data(request.data)
                api_logger.debug(
                    f"{log_prefix} Body: {json.dumps(sanitized_data, default=safe_json_serialize)[:1000]}"
                )
            except Exception as e:
                api_logger.warning(f"Failed to log request body: {str(e)[:200]}")
    except Exception as e:
        logger.error(f"Error in log_request_data: {str(e)[:200]}")


def log_response_data(response, log_prefix="📤 Response"):
    """Log outgoing response data safely"""
    try:
        status_code = getattr(response, "status_code", 0)

        # Log basic response info
        api_logger.info(f"{log_prefix}: Status {status_code}")

        # Try to safely log response data for non-file responses
        if hasattr(response, "data") and response.data:
            try:
                sanitized_data = sanitize_data(response.data)
                api_logger.debug(
                    f"{log_prefix} Data: {json.dumps(sanitized_data, default=safe_json_serialize)[:1000]}"
                )
            except Exception as e:
                api_logger.warning(f"Failed to log response data: {str(e)[:200]}")
    except Exception as e:
        logger.error(f"Error in log_response_data: {str(e)[:200]}")


def log_error(e, context="", log_full_trace=True):
    """Log exceptions with proper context and optional full trace"""
    try:
        error_type = type(e).__name__
        error_msg = str(e)[:500]  # Limit potentially large error messages

        logger.error(f"❌ ERROR: {context} - {error_type}: {error_msg}")

        if log_full_trace:
            tb_str = traceback.format_exc()
            logger.error(f"Traceback: {tb_str}")
    except Exception as trace_error:
        # Last resort error logging
        logger.error(f"Failed to log error properly: {str(trace_error)}")
