# Generated by Django 4.0 on 2024-12-13 15:09

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('authentication', '0003_remove_historicaluser_otp_secret_and_more'),
        ('services', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Appointment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('location', models.CharField(choices=[('A', 'Location A'), ('B', 'Location B')], max_length=1)),
                ('date', models.DateField()),
                ('time', models.TimeField()),
                ('total_duration', models.PositiveIntegerField(help_text='Total duration of the appointment in minutes')),
                ('notes', models.TextField(blank=True, null=True)),
                ('status', models.CharField(choices=[('booked', 'Booked'), ('confirmed', 'Confirmed'), ('cancelled', 'Cancelled'), ('no_show', 'No-Show')], default='booked', max_length=10)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='appointments', to='authentication.user')),
                ('service_package', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='appointments', to='services.servicepackage')),
            ],
        ),
        migrations.CreateModel(
            name='PackageBalance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('remaining_time', models.PositiveIntegerField(help_text='Remaining time in minutes')),
                ('customer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='package_balances', to='authentication.user')),
                ('service_package', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='balances', to='services.servicepackage')),
            ],
        ),
        migrations.CreateModel(
            name='AppointmentService',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('duration', models.PositiveIntegerField(help_text='Duration of the service in minutes')),
                ('appointment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='appointment_services', to='appointments.appointment')),
                ('service', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='appointments', to='services.service')),
            ],
        ),
    ]
