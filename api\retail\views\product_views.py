from rest_framework import viewsets, status, filters
from rest_framework.response import Response
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q
from django.db import transaction
from django.utils import timezone
from django.http import HttpResponse
import csv
import logging

from api.retail.models import Product
from api.retail.serializers import ProductSerializer
from api.core.permissions import IsOwnerOrReceptionist

logger = logging.getLogger(__name__)


class ProductViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing products.
    Staff users can create, update, and delete products.
    """

    queryset = Product.objects.all()
    serializer_class = ProductSerializer
    permission_classes = [IsAuthenticated, IsOwnerOrReceptionist]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ["category", "location", "is_active"]
    search_fields = ["name", "description"]
    ordering_fields = ["name", "price", "quantity_in_stock", "created_at"]
    ordering = ["name"]

    def get_queryset(self):
        """Filter products by location if specified."""
        queryset = super().get_queryset()
        location = self.request.query_params.get("location")

        if location:
            queryset = queryset.filter(Q(location=location) | Q(location="BOTH"))

        # Filter for low stock products
        low_stock = self.request.query_params.get("low_stock")
        if low_stock:
            try:
                threshold = int(low_stock)
                queryset = queryset.filter(quantity_in_stock__lte=threshold)
            except ValueError:
                # If not a valid integer, just filter for any low stock
                queryset = queryset.filter(quantity_in_stock__lt=10)

        return queryset

    @action(detail=False, methods=["post"])
    def update_stock(self, request):
        """
        Update stock quantity for multiple products at once.

        Expected payload:
        {
            "product_updates": [
                {"id": 1, "quantity_change": 10},
                {"id": 2, "quantity_change": -5}
            ]
        }
        """
        try:
            product_updates = request.data.get("product_updates", [])

            if not product_updates:
                return Response(
                    {"detail": "No product updates provided"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            updated_products = []
            with transaction.atomic():
                for update in product_updates:
                    product_id = update.get("id")
                    quantity_change = update.get("quantity_change", 0)

                    try:
                        product = Product.objects.get(id=product_id)

                        # Prevent negative stock
                        if product.quantity_in_stock + quantity_change < 0:
                            return Response(
                                {
                                    "detail": f"Cannot reduce {product.name} stock below zero. Current stock: {product.quantity_in_stock}"
                                },
                                status=status.HTTP_400_BAD_REQUEST,
                            )

                        product.quantity_in_stock += quantity_change
                        product.save()
                        updated_products.append(
                            {
                                "id": product.id,
                                "name": product.name,
                                "new_quantity": product.quantity_in_stock,
                            }
                        )
                    except Product.DoesNotExist:
                        return Response(
                            {"detail": f"Product with id {product_id} not found"},
                            status=status.HTTP_404_NOT_FOUND,
                        )

            return Response(
                {
                    "detail": f"Updated stock for {len(updated_products)} products",
                    "products": updated_products,
                }
            )
        except Exception as e:
            logger.error(f"Error updating product stock: {str(e)}", exc_info=True)
            return Response(
                {"detail": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=["get"])
    def export_csv(self, request):
        """Export all products to a CSV file."""
        try:
            # Get filtered queryset
            queryset = self.filter_queryset(self.get_queryset())

            response = HttpResponse(content_type="text/csv")
            filename = f"products_export_{timezone.now().strftime('%Y%m%d_%H%M%S')}.csv"
            response["Content-Disposition"] = f'attachment; filename="{filename}"'

            writer = csv.writer(response)
            writer.writerow(
                [
                    "ID",
                    "Name",
                    "Category",
                    "Description",
                    "Price",
                    "VAT %",
                    "Stock Quantity",
                    "Location",
                    "Active",
                    "Created At",
                ]
            )

            for product in queryset:
                writer.writerow(
                    [
                        product.id,
                        product.name,
                        product.get_category_display(),
                        product.description or "",
                        product.price,
                        product.vat_percentage,
                        product.quantity_in_stock,
                        product.get_location_display(),
                        "Yes" if product.is_active else "No",
                        product.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                    ]
                )

            return response
        except Exception as e:
            logger.error(f"Error exporting products to CSV: {str(e)}", exc_info=True)
            return Response(
                {"detail": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class ProductManagementViewSet(viewsets.ModelViewSet):
    """
    CRUD operations for Product management
    """

    queryset = Product.objects.all()
    serializer_class = ProductSerializer
    permission_classes = [IsAuthenticated, IsOwnerOrReceptionist]

    def get_queryset(self):
        """
        Optionally filter by category, location, or active status
        """
        queryset = Product.objects.all()

        # Filter by category if provided
        category = self.request.query_params.get("category", None)
        if category:
            queryset = queryset.filter(category=category)

        # Filter by location if provided
        location = self.request.query_params.get("location", None)
        if location:
            queryset = queryset.filter(location=location)

        return queryset.order_by("name")
