from django.db import models
from django.utils import timezone
from decimal import Decimal
from django.conf import settings
from dateutil.relativedelta import relativedelta

from api.staff.models import TherapistProfile
from api.services.models import Service, ServicePackage
from api.appointments.models import Sale


class CommissionProfile(models.Model):
    """
    Commission profile for a therapist.
    A therapist can have multiple profiles, but only one active default profile.
    """
    therapist = models.ForeignKey(
        TherapistProfile,
        on_delete=models.CASCADE,
        related_name='commission_profiles'
    )
    name = models.CharField(max_length=100)
    base_percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=0,
        help_text="Base commission percentage for all sales"
    )
    sessions_threshold = models.PositiveIntegerField(
        default=0,
        help_text="Minimum number of sessions before commission is applied"
    )
    is_active = models.BooleanField(default=True)
    is_default = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-is_default', '-is_active', 'name']

    def __str__(self):
        return f"{self.name} - {self.therapist.user.get_full_name()}"

    def save(self, *args, **kwargs):
        # If this is the default profile, make sure no other profile is default
        if self.is_default:
            CommissionProfile.objects.filter(
                therapist=self.therapist,
                is_default=True
            ).exclude(pk=self.pk).update(is_default=False)
        super().save(*args, **kwargs)


class CommissionRule(models.Model):
    """
    Commission rule for calculating commissions.
    Rules can be specific to a profile or global.
    """
    RULE_TYPES = (
        ('global', 'Global Rule'),
        ('service', 'Service-specific Rule'),
        ('package', 'Package-specific Rule'),
    )

    profile = models.ForeignKey(
        CommissionProfile,
        on_delete=models.CASCADE,
        related_name='commission_rules',
        null=True,
        blank=True,
        help_text="If null, this is a global rule"
    )
    name = models.CharField(max_length=100)
    rule_type = models.CharField(max_length=20, choices=RULE_TYPES, default='global')
    service = models.ForeignKey(
        Service,
        on_delete=models.CASCADE,
        related_name='commission_rules',
        null=True,
        blank=True,
        help_text="Only for service-specific rules"
    )
    package = models.ForeignKey(
        ServicePackage,
        on_delete=models.CASCADE,
        related_name='commission_rules',
        null=True,
        blank=True,
        help_text="Only for package-specific rules"
    )
    percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Commission percentage for this rule"
    )
    fixed_amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Fixed commission amount for this rule"
    )
    min_sessions = models.PositiveIntegerField(
        default=0,
        help_text="Minimum number of sessions before this rule applies"
    )
    priority = models.PositiveIntegerField(
        default=0,
        help_text="Higher priority rules are applied first"
    )
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-priority', '-is_active', 'name']

    def __str__(self):
        return self.name


class ManualCommission(models.Model):
    """
    Manual commission entry for a sale.
    """
    sale = models.ForeignKey(
        Sale,
        on_delete=models.CASCADE,
        related_name='manual_commissions'
    )
    therapist = models.ForeignKey(
        TherapistProfile,
        on_delete=models.CASCADE,
        related_name='manual_commissions'
    )
    amount = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Fixed commission amount"
    )
    percentage = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Commission percentage of sale price"
    )
    notes = models.TextField(blank=True)
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        related_name='created_manual_commissions'
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created_at']

    def __str__(self):
        return f"Manual Commission for {self.therapist.user.get_full_name()} - {self.sale.id}"


class CommissionEarning(models.Model):
    """
    Commission earning record.
    """
    therapist = models.ForeignKey(
        TherapistProfile,
        on_delete=models.CASCADE,
        related_name='commission_earnings'
    )
    sale = models.ForeignKey(
        Sale,
        on_delete=models.CASCADE,
        related_name='commission_earnings'
    )
    amount = models.DecimalField(max_digits=10, decimal_places=2)
    percentage_used = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        null=True,
        blank=True,
        help_text="Percentage used to calculate this commission"
    )
    commission_rule = models.ForeignKey(
        CommissionRule,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='earnings'
    )
    manual_commission = models.ForeignKey(
        ManualCommission,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='earnings'
    )
    date_earned = models.DateField(default=timezone.now)
    is_paid = models.BooleanField(default=False)
    payment_date = models.DateField(null=True, blank=True)
    is_eligible = models.BooleanField(
        default=True,
        help_text="Whether this commission is eligible based on business rules"
    )
    month_stat = models.ForeignKey(
        'TherapistMonthStats',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='commissions'
    )
    notes = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_primary = models.BooleanField(default=True, 
        help_text="Whether this is the primary commission for the sale")
    commission_type = models.CharField(
        max_length=20,
        choices=[
            ('standard', 'Standard Percentage'),
            ('expert_session', 'Expert Session Flat Rate'),
            ('expert_package', 'Expert Package Flat Rate'),
            ('home_service', 'Home Service'),
            ('freelancer', 'Freelancer Rate')
        ],
        default='standard'
    )

    class Meta:
        ordering = ['-date_earned', '-created_at']

    def __str__(self):
        return f"Commission for {self.therapist.user.get_full_name()} - {self.amount}"


class TherapistStats(models.Model):
    """
    Overall statistics for a therapist.
    """
    therapist = models.OneToOneField(
        TherapistProfile,
        on_delete=models.CASCADE,
        related_name='stats'
    )
    total_sessions = models.PositiveIntegerField(default=0)
    total_earnings = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00')
    )
    last_updated = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = "Therapist Stats"

    def __str__(self):
        return f"Stats for {self.therapist.user.get_full_name()}"


class TherapistYearStats(models.Model):
    """
    Yearly statistics for a therapist.
    """
    therapist = models.ForeignKey(
        TherapistProfile,
        on_delete=models.CASCADE,
        related_name='year_stats'
    )
    year = models.PositiveIntegerField()
    total_sessions = models.PositiveIntegerField(default=0)
    total_earnings = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00')
    )
    last_updated = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = "Therapist Year Stats"
        unique_together = ('therapist', 'year')
        ordering = ['-year']

    def __str__(self):
        return f"Year {self.year} Stats for {self.therapist.user.get_full_name()}"


class TherapistMonthStats(models.Model):
    """
    Monthly statistics for a therapist.
    Used for tracking monthly commissions and payouts.
    """
    therapist = models.ForeignKey(
        TherapistProfile,
        on_delete=models.CASCADE,
        related_name='month_stats'
    )
    year = models.PositiveIntegerField()
    month = models.PositiveIntegerField()  # 1-12
    total_sessions = models.PositiveIntegerField(default=0)
    total_earnings = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        default=Decimal('0.00')
    )
    is_paid = models.BooleanField(default=False)
    payment_date = models.DateField(null=True, blank=True)
    last_updated = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name_plural = "Therapist Month Stats"
        unique_together = ('therapist', 'year', 'month')
        ordering = ['-year', '-month']

    def __str__(self):
        month_name = timezone.datetime(self.year, self.month, 1).strftime('%B')
        return f"{month_name} {self.year} Stats for {self.therapist.user.get_full_name()}"

    @property
    def month_name(self):
        return timezone.datetime(self.year, self.month, 1).strftime('%B')

    @property
    def start_date(self):
        return timezone.datetime(self.year, self.month, 1).date()

    @property
    def end_date(self):
        next_month = timezone.datetime(self.year, self.month, 1) + relativedelta(months=1)
        return (next_month - relativedelta(days=1)).date()
