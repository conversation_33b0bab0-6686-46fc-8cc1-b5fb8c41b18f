# helpers/sales_analytics.py
from decimal import Decimal
from datetime import datetime, timedelta
import json
from django.db.models import Q, Sum, Count, F
from django.db.models.functions import TruncDate
from django.utils import timezone

from api.retail.models import ProductSale, DailySalesReport
from api.appointments.models import Sale, Appointment


def get_sales_analytics_legacy(period, location, reference_date=None, start_date=None):
    """
    Provide comprehensive analytics on sales trends.

    Parameters:
    - period: 'week', 'month', 'quarter', 'year', or 'custom'
    - location: Location identifier
    - reference_date: End date for the analysis (defaults to today)
    - start_date: Start date for the analysis (used with custom period)
    """

    # Determine date range based on period
    if reference_date is None:
        today = timezone.now().date()
    else:
        today = reference_date

    if period == "custom" and start_date is not None:
        # Use the provided start_date for custom periods
        start_date = start_date
    elif period == "week":
        start_date = today - timedelta(days=7)
    elif period == "month":
        start_date = today - timedelta(days=30)
    elif period == "quarter":
        start_date = today - timedelta(days=90)
    elif period == "year":
        start_date = today - timedelta(days=365)
    else:
        # Default to month if invalid period
        start_date = today - timedelta(days=30)

    # Get appointment IDs at the specified location in the date range
    appointment_filter = Q(
        date__range=[start_date, today], location=location, status="check_in"
    )
    appointment_ids = Appointment.objects.filter(appointment_filter).values_list(
        "id", flat=True
    )

    # Service sales from the Sale model
    service_sales = Sale.objects.filter(
        created_at__date__range=[start_date, today],
        sale_type="service",
        appointment_id__in=appointment_ids,
    )

    # Package sales from the Sale model
    package_sales = Sale.objects.filter(
        created_at__date__range=[start_date, today], sale_type="package"
    )

    # Product sales
    product_sales = ProductSale.objects.filter(
        created_at__date__range=[start_date, today],
        location=location,
        status="COMPLETED",
    )

    # Calculate trends over time (daily aggregation)
    service_trend = (
        service_sales.annotate(date=TruncDate("created_at"))
        .values("date")
        .annotate(total=Sum("total_price"), count=Count("id"))
        .order_by("date")
    )

    package_trend = (
        package_sales.annotate(date=TruncDate("created_at"))
        .values("date")
        .annotate(total=Sum("total_price"), count=Count("id"))
        .order_by("date")
    )

    product_trend = (
        product_sales.annotate(date=TruncDate("created_at"))
        .values("date")
        .annotate(total=Sum("total_amount"), count=Count("id"))
        .order_by("date")
    )

    # Calculate overall totals
    service_sales_total = service_sales.aggregate(total=Sum("total_price"))["total"] or 0
    package_sales_total = package_sales.aggregate(total=Sum("total_price"))["total"] or 0
    product_sales_total = product_sales.aggregate(total=Sum("total_amount"))["total"] or 0

    total_revenue = service_sales_total + package_sales_total + product_sales_total

    # Most popular packages
    popular_packages = []
    if package_sales.exists():
        popular_packages = (
            package_sales.values("package_option__package__name", "package_option__time")
            .annotate(count=Count("id"), total=Sum("total_price"))
            .order_by("-count")[:10]
        )
    total_sales_count = service_sales.count() + package_sales.count()

    # Calculate discount statistics
    discounted_services = service_sales.filter(
        Q(discount__isnull=False) | Q(discount_percentage__gt=0)
    ).count()

    discounted_packages = package_sales.filter(
        Q(discount__isnull=False) | Q(discount_percentage__gt=0)
    ).count()

    total_discounts = discounted_services + discounted_packages

    # Calculate discount amounts
    service_discount_amounts = service_sales.filter(
        Q(discount__isnull=False) | Q(discount_percentage__gt=0)
    ).aggregate(total=Sum(F("total_price") * F("discount_percentage") / 100))[
        "total"
    ] or Decimal(
        "0.00"
    )

    package_discount_amounts = package_sales.filter(
        Q(discount__isnull=False) | Q(discount_percentage__gt=0)
    ).aggregate(total=Sum(F("total_price") * F("discount_percentage") / 100))[
        "total"
    ] or Decimal(
        "0.00"
    )

    discount_amounts = service_discount_amounts + package_discount_amounts

    # Calculate payment method distribution
    payment_methods = {}

    # For service sales
    for method, display in Appointment.PAYMENT_METHOD_CHOICES:
        method_sales = service_sales.filter(appointment__payment_method=method)
        method_total = method_sales.aggregate(total=Sum("total_price"))["total"] or 0
        method_count = method_sales.count()

        if method in payment_methods:
            payment_methods[method]["total"] += method_total
            payment_methods[method]["count"] += method_count
        else:
            payment_methods[method] = {
                "method": method,
                "display": display,
                "total": method_total,
                "count": method_count,
            }

    # For product sales
    for method_tuple in ProductSale.PAYMENT_METHOD_CHOICES:
        product_method, product_display = method_tuple
        method_sales = product_sales.filter(payment_method=product_method)
        method_total = method_sales.aggregate(total=Sum("total_amount"))["total"] or 0
        method_count = method_sales.count()

        # Map product sale payment methods to a common format
        if product_method == "CASH":
            method = "cash"
        elif product_method == "CARD":
            method = "card"
        elif product_method == "ONLINE_LINK":
            method = "link"
        else:
            method = product_method.lower()

        if method in payment_methods:
            payment_methods[method]["total"] += method_total
            payment_methods[method]["count"] += method_count
        else:
            payment_methods[method] = {
                "method": method,
                "display": product_display,
                "total": method_total,
                "count": method_count,
            }

    payment_distribution = list(payment_methods.values())

    # Create unified day-by-day trend combining all sales types
    unified_trend = []
    date_set = set()

    # Collect all dates from the three trends
    for trend in [service_trend, package_trend, product_trend]:
        for item in trend:
            date_set.add(item["date"])

    # Sort dates
    all_dates = sorted(date_set)

    # Create a lookup dictionary for each trend
    service_dict = {item["date"]: item for item in service_trend}
    package_dict = {item["date"]: item for item in package_trend}
    product_dict = {item["date"]: item for item in product_trend}

    # Build unified trend
    for date in all_dates:
        service_total = service_dict.get(date, {}).get("total", 0) or 0
        package_total = package_dict.get(date, {}).get("total", 0) or 0
        product_total = product_dict.get(date, {}).get("total", 0) or 0

        unified_trend.append(
            {
                "date": date.isoformat(),
                "service_sales": service_total,
                "package_sales": package_total,
                "product_sales": product_total,
                "total_sales": service_total + package_total + product_total,
            }
        )

    return {
        "period": period,
        "date_range": {
            "start": start_date.isoformat(),
            "end": today.isoformat(),
        },
        "location": location,
        "totals": {
            "service_sales": service_sales_total,
            "package_sales": package_sales_total,
            "product_sales": product_sales_total,
            "total_revenue": total_revenue,
            "vat_amount": total_revenue * Decimal("0.05"),
            "sales_count": service_sales.count()
            + package_sales.count()
            + product_sales.count(),
        },
        "payment_distribution": payment_distribution,
        "popular_packages": popular_packages,
        "discount_metrics": {
            "discounted_services": discounted_services,
            "discounted_packages": discounted_packages,
            "total_discounts": total_discounts,
            "discount_amounts": discount_amounts,
            "discount_percentage": (
                (total_discounts / total_sales_count * 100)
                if total_sales_count > 0
                else 0
            ),
        },
        "trend": unified_trend,
    }


def get_sales_analytics(date, location):
    """
    Generate sales trends and analytics for a specific date and location.
    Uses the date's month (from the 1st day to the selected date) as the period.
    """
    # Selected date
    selected_date = date

    # Get current month's first day and selected date
    first_day_of_month = datetime(selected_date.year, selected_date.month, 1).date()

    # Get report for the selected date
    try:
        selected_date_report = DailySalesReport.objects.get(
            date=selected_date, location=location
        )
        selected_date_gross = selected_date_report.gross_sales_amount
    except DailySalesReport.DoesNotExist:
        selected_date_gross = Decimal("0.00")

    # Calculate days passed in the month and total days in month
    days_passed = (selected_date - first_day_of_month).days + 1

    # Get last day of month
    if selected_date.month == 12:
        last_day_of_month = datetime(selected_date.year + 1, 1, 1).date() - timedelta(
            days=1
        )
    else:
        last_day_of_month = datetime(
            selected_date.year, selected_date.month + 1, 1
        ).date() - timedelta(days=1)

    total_days_in_month = (last_day_of_month - first_day_of_month).days + 1
    month_progress = (days_passed / total_days_in_month) * 100

    # Get all reports for this month up to the selected date
    month_reports = DailySalesReport.objects.filter(
        date__range=[first_day_of_month, selected_date], location=location
    )

    month_total = month_reports.aggregate(total=Sum("gross_sales_amount"))[
        "total"
    ] or Decimal("0.00")

    # Calculate average daily sales for the month so far
    if days_passed > 0:
        month_avg_daily = month_total / days_passed
    else:
        month_avg_daily = Decimal("0.00")

    # Calculate selected date vs month average percentage
    if month_avg_daily > 0:
        date_vs_month_avg = (
            (selected_date_gross - month_avg_daily) / month_avg_daily
        ) * 100
    else:
        date_vs_month_avg = 0

    # Get same day last week
    last_week_date = selected_date - timedelta(days=7)
    try:
        last_week_report = DailySalesReport.objects.get(
            date=last_week_date, location=location
        )
        last_week_gross = last_week_report.gross_sales_amount
    except DailySalesReport.DoesNotExist:
        last_week_gross = Decimal("0.00")

    # Calculate day vs same day last week percentage
    if last_week_gross > 0:
        date_vs_prev_week = (
            (selected_date_gross - last_week_gross) / last_week_gross
        ) * 100
    else:
        date_vs_prev_week = 0 if selected_date_gross == 0 else 100

    # Use legacy sales analytics function to get detailed sales data for the month
    analytics_data = get_sales_analytics_legacy(
        period="custom",
        location=location,
        reference_date=selected_date,
        start_date=first_day_of_month,
    )

    # Extract trend data from analytics
    trend_data = analytics_data["trend"]

    # Prepare data for charts
    dates = []
    service_data = []
    product_data = []
    package_data = []

    for day in trend_data:
        # Extract the day part from ISO date string
        date_obj = datetime.fromisoformat(day["date"])
        dates.append(date_obj.strftime("%d"))

        service_data.append(float(day["service_sales"]))
        product_data.append(float(day["product_sales"]))
        package_data.append(float(day["package_sales"]))

    # Calculate category trends - if we have enough data
    service_trend = 0
    product_trend = 0
    package_trend = 0

    if len(service_data) >= 14:  # At least 2 weeks of data
        first_half_service = sum(service_data[: len(service_data) // 2])
        second_half_service = sum(service_data[len(service_data) // 2 :])

        first_half_product = sum(product_data[: len(product_data) // 2])
        second_half_product = sum(product_data[len(product_data) // 2 :])

        first_half_package = sum(package_data[: len(package_data) // 2])
        second_half_package = sum(package_data[len(package_data) // 2 :])

        # Calculate percentage changes
        if first_half_service > 0:
            service_trend = (
                (second_half_service - first_half_service) / first_half_service
            ) * 100

        if first_half_product > 0:
            product_trend = (
                (second_half_product - first_half_product) / first_half_product
            ) * 100

        if first_half_package > 0:
            package_trend = (
                (second_half_package - first_half_package) / first_half_package
            ) * 100

    # Get weekly pattern data (average sales by day of week)
    weekly_pattern = {
        "mon": 0,
        "tue": 0,
        "wed": 0,
        "thu": 0,
        "fri": 0,
        "sat": 0,
        "sun": 0,
    }

    # Collect totals and counts for each day of the week
    weekday_totals = {
        "mon": 0,
        "tue": 0,
        "wed": 0,
        "thu": 0,
        "fri": 0,
        "sat": 0,
        "sun": 0,
    }
    weekday_counts = {
        "mon": 0,
        "tue": 0,
        "wed": 0,
        "thu": 0,
        "fri": 0,
        "sat": 0,
        "sun": 0,
    }

    # Get reports for this month and previous month to have enough data for pattern
    previous_month_start = first_day_of_month - timedelta(days=1)
    while previous_month_start.month == first_day_of_month.month:
        previous_month_start -= timedelta(days=1)
    previous_month_start = datetime(
        previous_month_start.year, previous_month_start.month, 1
    ).date()

    pattern_reports = DailySalesReport.objects.filter(
        date__range=[previous_month_start, selected_date], location=location
    )

    # Calculate totals by day of week
    for report in pattern_reports:
        weekday = report.date.strftime("%a").lower()
        if weekday in weekday_totals:
            weekday_totals[weekday] += float(report.gross_sales_amount)
            weekday_counts[weekday] += 1

    # Calculate averages
    for day in weekly_pattern:
        if weekday_counts[day] > 0:
            weekly_pattern[day] = weekday_totals[day] / weekday_counts[day]

    # Set up weeks comparison data structure
    four_week_comparison = {
        "current": {"week1": 0, "week2": 0, "week3": 0, "week4": 0},
        "previous": {"week1": 0, "week2": 0, "week3": 0, "week4": 0},
    }

    # Calculate this month's weekly totals (up to selected date)
    current_month_reports = month_reports

    for report in current_month_reports:
        day_of_month = report.date.day
        week_num = ((day_of_month - 1) // 7) + 1
        if 1 <= week_num <= 4:
            four_week_comparison["current"][f"week{week_num}"] += float(
                report.gross_sales_amount
            )

    # Previous month's reports
    prev_month_end = first_day_of_month - timedelta(days=1)
    prev_month_start = datetime(prev_month_end.year, prev_month_end.month, 1).date()

    prev_month_reports = DailySalesReport.objects.filter(
        date__range=[prev_month_start, prev_month_end], location=location
    )

    # Calculate weekly totals for previous month
    for report in prev_month_reports:
        day_of_month = report.date.day
        week_num = ((day_of_month - 1) // 7) + 1
        if 1 <= week_num <= 4:
            four_week_comparison["previous"][f"week{week_num}"] += float(
                report.gross_sales_amount
            )

    # Calculate week-over-week and month-over-month trends
    # Week over week (selected date's week vs previous week)
    current_week_start = selected_date - timedelta(days=selected_date.weekday())
    prev_week_start = current_week_start - timedelta(days=7)

    current_week_data = DailySalesReport.objects.filter(
        date__range=[current_week_start, selected_date], location=location
    )

    prev_week_end = current_week_start - timedelta(days=1)
    prev_week_data = DailySalesReport.objects.filter(
        date__range=[prev_week_start, prev_week_end], location=location
    )

    current_week_total = current_week_data.aggregate(total=Sum("gross_sales_amount"))[
        "total"
    ] or Decimal("0.00")
    prev_week_total = prev_week_data.aggregate(total=Sum("gross_sales_amount"))[
        "total"
    ] or Decimal("0.00")

    if prev_week_total > 0:
        week_over_week = ((current_week_total - prev_week_total) / prev_week_total) * 100
    else:
        week_over_week = 0 if current_week_total == 0 else 100

    # Month over month (this month to date vs same period last month)
    # Calculate same period last month
    if selected_date.month == 1:
        prev_month_year = selected_date.year - 1
        prev_month_num = 12
    else:
        prev_month_year = selected_date.year
        prev_month_num = selected_date.month - 1

    prev_month_start = datetime(prev_month_year, prev_month_num, 1).date()
    # Get same day number or last day of month if shorter
    try:
        prev_month_end = datetime(
            prev_month_year, prev_month_num, selected_date.day
        ).date()
    except ValueError:
        # If the previous month doesn't have this day (e.g., trying to get Feb 30)
        if prev_month_num == 12:
            next_month = datetime(prev_month_year + 1, 1, 1).date()
        else:
            next_month = datetime(prev_month_year, prev_month_num + 1, 1).date()
        prev_month_end = next_month - timedelta(days=1)

    prev_month_period_data = DailySalesReport.objects.filter(
        date__range=[prev_month_start, prev_month_end], location=location
    )

    prev_month_period_total = prev_month_period_data.aggregate(
        total=Sum("gross_sales_amount")
    )["total"] or Decimal("0.00")

    if prev_month_period_total > 0:
        month_over_month = (
            (month_total - prev_month_period_total) / prev_month_period_total
        ) * 100
    else:
        month_over_month = 0 if month_total == 0 else 100

    # Monthly target and projection
    monthly_target = Decimal("200000")  # This should come from settings/configuration

    if days_passed > 0:
        projected_total = (month_total / days_passed) * total_days_in_month
        target_achievement = (
            (projected_total / monthly_target) * 100 if monthly_target > 0 else 0
        )
    else:
        projected_total = Decimal("0.00")
        target_achievement = 0

    return {
        "first_day_of_month": first_day_of_month,
        "days_in_month": total_days_in_month,
        "day_vs_month_avg": float(date_vs_month_avg),
        "day_vs_prev_week": float(date_vs_prev_week),
        "week_over_week": float(week_over_week),
        "month_over_month": float(month_over_month),
        "month_progress": float(month_progress),
        "monthly_target": float(monthly_target),
        "current_total": float(month_total),
        "current_achievement": (
            float((month_total / monthly_target) * 100) if monthly_target > 0 else 0
        ),
        "projected_total": float(projected_total),
        "target_achievement": float(target_achievement),
        "category_trends": {
            "dates": json.dumps(dates),
            "service_data": json.dumps(service_data),
            "product_data": json.dumps(product_data),
            "package_data": json.dumps(package_data),
            "service": float(service_trend),
            "product": float(product_trend),
            "package": float(package_trend),
        },
        "weekly_pattern": weekly_pattern,
        "four_week_comparison": four_week_comparison,
    }
