#!/bin/bash

# Commission Recalculation Helper Script
# This script provides easy access to the commission recalculation functionality

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to show usage
show_usage() {
    echo -e "${GREEN}🤖 Commission Recalculation Helper${NC}"
    echo ""
    echo "Usage: $0 <start-date> <end-date> [options]"
    echo ""
    echo "Examples:"
    echo "  $0 2025-05-01 2025-05-31                    # Auto mode (recommended)"
    echo "  $0 2025-05-01 2025-05-31 --dry-run         # Preview changes"
    echo "  $0 2025-05-01 2025-05-31 --missing-only    # Only missing commissions"
    echo "  $0 2025-05-01 2025-05-31 --force           # Force recalculate all"
    echo ""
    echo "Options:"
    echo "  --dry-run       Preview changes without making them"
    echo "  --missing-only  Only calculate missing commissions"
    echo "  --force         Force recalculate all commissions"
    echo "  --verbose       Show detailed progress"
    echo "  --package-details Show detailed package commission breakdown"
    echo "  --help          Show this help message"
    echo ""
    echo "Date format: YYYY-MM-DD"
}

# Check if help is requested
if [[ "$1" == "--help" ]] || [[ "$1" == "-h" ]] || [[ $# -eq 0 ]]; then
    show_usage
    exit 0
fi

# Check if we have at least start and end dates
if [[ $# -lt 2 ]]; then
    print_error "Missing required arguments: start-date and end-date"
    show_usage
    exit 1
fi

START_DATE="$1"
END_DATE="$2"
shift 2

# Validate date format (basic check)
if ! [[ "$START_DATE" =~ ^[0-9]{4}-[0-9]{2}-[0-9]{2}$ ]] || ! [[ "$END_DATE" =~ ^[0-9]{4}-[0-9]{2}-[0-9]{2}$ ]]; then
    print_error "Invalid date format. Use YYYY-MM-DD"
    exit 1
fi

# Parse additional options
DRY_RUN=""
VERBOSE=""
PACKAGE_DETAILS=""
MODE="--auto"  # Default to auto mode

while [[ $# -gt 0 ]]; do
    case $1 in
        --dry-run)
            DRY_RUN="--dry-run"
            shift
            ;;
        --verbose)
            VERBOSE="--verbose"
            shift
            ;;
        --package-details)
            PACKAGE_DETAILS="--package-details"
            shift
            ;;
        --missing-only)
            MODE="--missing-only"
            shift
            ;;
        --force)
            MODE="--force-recalculate"
            shift
            ;;
        *)
            print_warning "Unknown option: $1"
            shift
            ;;
    esac
done

# Build the command
CMD="docker exec -it stretchup-api-web-1 python manage.py recalculate_commissions --start-date $START_DATE --end-date $END_DATE $MODE $DRY_RUN $VERBOSE $PACKAGE_DETAILS"

# Show what we're about to do
print_info "Commission Recalculation"
echo "📅 Date Range: $START_DATE to $END_DATE"
echo "🔧 Mode: $MODE"
if [[ -n "$DRY_RUN" ]]; then
    echo "👀 Dry Run: Yes (no changes will be made)"
else
    echo "💾 Live Mode: Changes will be made to the database"
fi
if [[ -n "$VERBOSE" ]]; then
    echo "📝 Verbose: Yes"
fi
if [[ -n "$PACKAGE_DETAILS" ]]; then
    echo "📦 Package Details: Yes (detailed package commission breakdown)"
fi
echo ""

# Confirm if not dry run
if [[ -z "$DRY_RUN" ]]; then
    print_warning "This will modify the database. Are you sure? (y/N)"
    read -r response
    if [[ ! "$response" =~ ^[Yy]$ ]]; then
        print_info "Operation cancelled"
        exit 0
    fi
fi

# Execute the command
print_info "Executing command..."
echo "Command: $CMD"
echo ""

if eval "$CMD"; then
    print_success "Commission recalculation completed successfully!"
else
    print_error "Commission recalculation failed!"
    exit 1
fi
