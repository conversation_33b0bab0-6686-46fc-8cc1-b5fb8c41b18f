from rest_framework.permissions import BasePermission


class IsSelf(BasePermission):
    """
    Custom permission to only allow users to access their own data.
    """

    def has_permission(self, request, view):
        # Ensure the user is authenticated
        return request.user and request.user.is_authenticated

    def has_object_permission(self, request, view, obj):
        # Object-level permission to only allow users to access their own data
        return obj == request.user


class IsStaffUser(BasePermission):
    """
    Allows access only to staff users.
    """

    def has_permission(self, request, view):
        return bool(request.user and request.user.is_staff)


class IsCustomer(BasePermission):
    """
    Allows access only to customers.
    """

    def has_permission(self, request, view):
        return request.user and request.user.role == "customer"


class IsTherapist(BasePermission):
    """
    Allows access only to therapists.
    """

    def has_permission(self, request, view):
        return request.user and request.user.role == "therapist"


class IsReceptionist(BasePermission):
    """
    Allows access only to receptionists.
    """

    def has_permission(self, request, view):
        return request.user and request.user.role == "receptionist"


class IsOwner(BasePermission):
    """
    Allows access only to owners.
    """

    def has_permission(self, request, view):
        return request.user and request.user.role == "owner"


class IsOwnerOrReceptionist(BasePermission):
    def has_permission(self, request, view):
        # Must be authenticated first
        if not request.user.is_authenticated:
            return False

        # Check if user is owner OR user is receptionist
        return request.user.role in ["owner", "receptionist"]
