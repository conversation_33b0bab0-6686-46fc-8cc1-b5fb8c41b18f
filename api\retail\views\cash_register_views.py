from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters
from django.db.models import Sum
import logging
from datetime import datetime

from api.retail.models import (
    CashRegister,
    CashWithdrawal,
    CashDeposit,
    ProductSale,
    Expense,
)
from api.retail.serializers import (
    CashRegisterSerializer,
    CashWithdrawalSerializer,
    CashDepositSerializer,
    ExpenseSerializer,
)
from api.core.permissions import IsOwnerOrReceptionist

logger = logging.getLogger(__name__)


class CashRegisterViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing cash registers.
    Staff users can create and manage cash registers.
    """

    queryset = CashRegister.objects.all().order_by("-date")
    serializer_class = CashRegisterSerializer
    permission_classes = [IsAuthenticated, IsOwnerOrReceptionist]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ["location", "date"]  # Ensure date is included in filter fields
    ordering_fields = ["date"]
    ordering = ["-date"]

    def list(self, request, *args, **kwargs):
        """
        Override list to add transactions to response when date and location are specified.
        """
        # Get query parameters
        date_str = request.query_params.get("date")
        location = request.query_params.get("location")

        # If both date and location are provided, get detailed info for that specific register
        if date_str and location:
            try:
                date = datetime.strptime(date_str, "%Y-%m-%d").date()

                try:
                    register = CashRegister.objects.get(date=date, location=location)
                    from api.appointments.models import Appointment

                    # Get sales for this date and location
                    sales = ProductSale.objects.filter(
                        created_at__date=date,
                        location=location,
                        status="COMPLETED",
                        payment_method="CASH",
                    )
                    cash_sales_total = sales.aggregate(total=Sum("total_amount"))[
                        "total"
                    ] or 0 + (
                        Appointment.objects.filter(
                            date=date,
                            location=location,
                            status="check_in",
                            payment_method="cash",
                        ).aggregate(total=Sum("total_price"))["total"]
                        or 0
                    )

                    # Get withdrawals, deposits, and expenses
                    withdrawals = CashWithdrawal.objects.filter(cash_register=register)
                    withdrawals_total = (
                        withdrawals.aggregate(total=Sum("amount"))["total"] or 0
                    )

                    deposits = CashDeposit.objects.filter(cash_register=register)
                    deposits_total = deposits.aggregate(total=Sum("amount"))["total"] or 0

                    expenses = Expense.objects.filter(cash_register=register)
                    expenses_total = expenses.aggregate(total=Sum("amount"))["total"] or 0

                    # Include additional info in the response
                    data = CashRegisterSerializer(register).data
                    data.update(
                        {
                            "cash_sales_total": cash_sales_total,
                            "withdrawals_total": withdrawals_total,
                            "deposits_total": deposits_total,
                            "expenses_total": expenses_total,
                            "transactions": {
                                "withdrawals": CashWithdrawalSerializer(
                                    withdrawals, many=True
                                ).data,
                                "deposits": CashDepositSerializer(
                                    deposits, many=True
                                ).data,
                                "expenses": ExpenseSerializer(expenses, many=True).data,
                            },
                        }
                    )

                    return Response(data)

                except CashRegister.DoesNotExist:
                    return Response(
                        {
                            "detail": f"No cash register found for {location} on {date_str}"
                        },
                        status=status.HTTP_404_NOT_FOUND,
                    )

            except ValueError:
                return Response(
                    {"detail": "Invalid date format. Use YYYY-MM-DD"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

        # If not filtering by both date and location, use the standard list method
        return super().list(request, *args, **kwargs)
