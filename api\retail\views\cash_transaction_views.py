from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.decorators import action
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters
from django.db.models import Sum
import logging
from datetime import datetime

from api.retail.models import CashRegister, CashWithdrawal, CashDeposit, Expense
from api.retail.serializers import (
    CashWithdrawalSerializer,
    CashDepositSerializer,
    ExpenseSerializer,
)
from api.core.permissions import IsOwnerOrReceptionist

logger = logging.getLogger(__name__)


class CashWithdrawalViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing cash withdrawals.
    """

    queryset = CashWithdrawal.objects.all().order_by("-created_at")
    serializer_class = CashWithdrawalSerializer
    permission_classes = [IsAuthenticated, IsOwnerOrReceptionist]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ["cash_register__location"]
    ordering_fields = ["created_at", "amount"]
    ordering = ["-created_at"]

    def perform_create(self, serializer):
        """Set the staff to the current user."""
        serializer.save(staff=self.request.user)

    @action(detail=False, methods=["get"])
    def by_date(self, request):
        """Get withdrawals for a specific date and location."""
        try:
            date_str = request.query_params.get("date")
            location = request.query_params.get("location")

            if not date_str or not location:
                return Response(
                    {"detail": "Both date and location parameters are required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            try:
                date = datetime.strptime(date_str, "%Y-%m-%d").date()
            except ValueError:
                return Response(
                    {"detail": "Invalid date format. Use YYYY-MM-DD"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Get the cash register for this date and location
            try:
                register = CashRegister.objects.get(date=date, location=location)
            except CashRegister.DoesNotExist:
                return Response(
                    {"detail": f"No cash register found for {location} on {date_str}"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            # Get withdrawals for this register
            withdrawals = CashWithdrawal.objects.filter(cash_register=register).order_by(
                "-created_at"
            )

            return Response(
                {
                    "date": date_str,
                    "location": location,
                    "location_display": dict(CashRegister.LOCATION_CHOICES).get(
                        location, location
                    ),
                    "total": withdrawals.aggregate(total=Sum("amount"))["total"] or 0,
                    "withdrawals": CashWithdrawalSerializer(withdrawals, many=True).data,
                }
            )
        except Exception as e:
            logger.error(f"Error retrieving withdrawals by date: {str(e)}", exc_info=True)
            return Response(
                {"detail": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class CashDepositViewSet(viewsets.ModelViewSet):
    """
    ViewSet for managing cash deposits.
    """

    queryset = CashDeposit.objects.all().order_by("-created_at")
    serializer_class = CashDepositSerializer
    permission_classes = [IsAuthenticated, IsOwnerOrReceptionist]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ["cash_register__location"]
    ordering_fields = ["created_at", "amount"]
    ordering = ["-created_at"]

    def perform_create(self, serializer):
        """Set the staff to the current user."""
        serializer.save(staff=self.request.user)

    @action(detail=False, methods=["get"])
    def by_date(self, request):
        """Get deposits for a specific date and location."""
        try:
            date_str = request.query_params.get("date")
            location = request.query_params.get("location")

            if not date_str or not location:
                return Response(
                    {"detail": "Both date and location parameters are required"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            try:
                date = datetime.strptime(date_str, "%Y-%m-%d").date()
            except ValueError:
                return Response(
                    {"detail": "Invalid date format. Use YYYY-MM-DD"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # Get the cash register for this date and location
            try:
                register = CashRegister.objects.get(date=date, location=location)
            except CashRegister.DoesNotExist:
                return Response(
                    {"detail": f"No cash register found for {location} on {date_str}"},
                    status=status.HTTP_404_NOT_FOUND,
                )

            # Get deposits for this register
            deposits = CashDeposit.objects.filter(cash_register=register).order_by(
                "-created_at"
            )

            return Response(
                {
                    "date": date_str,
                    "location": location,
                    "location_display": dict(CashRegister.LOCATION_CHOICES).get(
                        location, location
                    ),
                    "total": deposits.aggregate(total=Sum("amount"))["total"] or 0,
                    "deposits": CashDepositSerializer(deposits, many=True).data,
                }
            )
        except Exception as e:
            logger.error(f"Error retrieving deposits by date: {str(e)}", exc_info=True)
            return Response(
                {"detail": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
