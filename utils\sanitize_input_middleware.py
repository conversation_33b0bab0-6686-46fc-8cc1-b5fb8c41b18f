from django.utils.deprecation import MiddlewareMixin
from django.conf import settings
from rest_framework.request import Request
from .sanitize import sanitize_input
from utils.email import send_email
import logging


class SanitizeInputMiddleware(MiddlewareMixin):
    def process_request(self, request):
        # logger = logging.getLogger("info_logger")
        # logger.info(f"SanitizeInputMiddleware is processing the request.")
        # logger.info(f"Request method: {request.method}")

        # logger.info(f"Request headers before processing: {request.headers}")

        xss_detected = False
        original_data = None
        sanitized_data = None

        if request.method in ["POST", "PUT", "PATCH"]:
            # Handle DRF request
            if isinstance(request, Request):
                original_data = request.data
                # logger.info(f"DRF request.data before sanitization: {original_data}")

                # Separate files and non-files
                non_file_data = {
                    k: v for k, v in original_data.items() if not hasattr(v, "read")
                }
                file_data = {k: v for k, v in original_data.items() if hasattr(v, "read")}

                # Sanitize non-file data only
                sanitized_non_file_data = sanitize_input(non_file_data)
                sanitized_data = {**sanitized_non_file_data, **file_data}
                request._data = sanitized_data

                # logger.info(f"DRF request.data after sanitization: {request._data}")
            else:
                # Handle Django request (Form data and Files)
                form_data = request.POST.dict()  # Convert QueryDict to a regular dict
                # logger.info(f"Django request.POST before sanitization: {form_data}")
                # logger.info(f"Django request.FILES: {request.FILES}")

                # Sanitize form data but leave file data untouched
                sanitized_form_data = sanitize_input(form_data)

                # Instead of reassigning request.POST or request.FILES, update request.POST directly
                request.POST = request.POST.copy()  # Make request.POST mutable
                for key, value in sanitized_form_data.items():
                    request.POST[key] = value  # Update POST data in place

                # logger.info(f"Django request.POST after sanitization: {request.POST}")
                # logger.info(f"Django request.FILES remains unchanged: {request.FILES}")

        # Handle GET/DELETE/HEAD/OPTIONS requests
        if request.method in ["GET", "DELETE", "HEAD", "OPTIONS"]:
            original_get = request.GET
            sanitized_get = sanitize_input(request.GET)
            request.GET = sanitized_get
            # logger.info(f"Sanitized GET parameters: {request.GET}")

            # Detect XSS attempts in GET parameters
            if original_get != sanitized_get:
                xss_detected = True
                # logger.info("XSS detected in GET parameters")

            sanitized_data = sanitized_get

        # logger.info(f"Request headers after processing: {request.headers}")

        # Send email notification if XSS is detected
        if xss_detected and sanitized_data is not None:
            subject = "XSS Attempt Detected"
            # logger.info(subject)
            message = f"An XSS attempt was detected.\n\nOriginal Data:\n{original_data}\n\nSanitized Data:\n{sanitized_data}"
            recipient_list = [settings.DEFAULT_FROM_EMAIL]
            # logger.info("Sending email notification")
            # send_email(subject, message, recipient_list)

        return None
