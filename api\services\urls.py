from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from .views import (
    # Public views
    ServiceListView,
    ServicePackageListView,
    SharedPackageListView,
    UnlimitedPackageListView,
    # Management views
    ServiceViewSet,
    ServicePackageViewSet,
)

# Configure DRF Router for ViewSets
router = DefaultRouter()
router.register(r"services/management", ServiceViewSet)
router.register(r"packages/management", ServicePackageViewSet)

urlpatterns = [
    # Public endpoints - keeping original URLs
    path("services/", ServiceListView.as_view(), name="service_list"),
    path("packages/", ServicePackageListView.as_view(), name="package_list"),
    path("shared_package/", SharedPackageListView.as_view(), name="shared_package_list"),
    path(
        "unlimited_package/",
        UnlimitedPackageListView.as_view(),
        name="unlimited_package_list",
    ),
    # Include the router URLs
    path("", include(router.urls)),
]
