# Generated by Django 4.0 on 2024-12-24 03:07

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('services', '0001_initial'),
        ('appointments', '0005_appointment_total_price_alter_appointment_status'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='packagebalance',
            name='service_package',
        ),
        migrations.AddField(
            model_name='packagebalance',
            name='package_option',
            field=models.ForeignKey(default=2, on_delete=django.db.models.deletion.CASCADE, related_name='balances', to='services.packageoption'),
            preserve_default=False,
        ),
    ]
