# Generated by Django 4.0 on 2024-12-24 04:13

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('appointments', '0008_remove_appointment_service_package_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='packagebalance',
            name='appointment',
        ),
        migrations.RemoveField(
            model_name='packagebalance',
            name='package_option',
        ),
        migrations.AddField(
            model_name='appointment',
            name='package_balance',
            field=models.OneToOneField(null=True, on_delete=django.db.models.deletion.CASCADE, to='appointments.packagebalance'),
        ),
    ]
