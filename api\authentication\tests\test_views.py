from django.test.utils import override_settings
from django.urls import reverse
from django.contrib.auth import get_user_model
from django.core.cache import cache


from rest_framework import status
from rest_framework.test import APIClient, APITestCase
from rest_framework_simplejwt.tokens import RefreshToken

import pytest
from unittest import mock

from unittest.mock import patch


User = get_user_model()


@pytest.fixture
def api_client():
    return APIClient()


@pytest.fixture
def token_url():
    return reverse("token_obtain_pair")


@pytest.fixture
def generate_otp_url():
    return reverse("token_generate_otp")


@pytest.fixture
def verify_otp_url():
    return reverse("token_verify_otp")


@pytest.fixture
def refresh_url():
    return reverse("token_refresh")


@pytest.fixture
def verify_url():
    return reverse("token_verify")


@pytest.fixture
def user_url():
    return reverse("manage_user")


@pytest.fixture(autouse=True)
def clear_cache():
    cache.clear()


@pytest.fixture
def token_pair(non_staff_user):
    refresh = RefreshToken.for_user(non_staff_user)
    return {"refresh": str(refresh), "access": str(refresh.access_token)}


ERROR_MSG = "Something went wrong"


@pytest.mark.django_db
def test_register_user(api_client):
    """
    Ensure a new user can be registered successfully.
    """
    url = reverse("token_register")
    data = {
        "email": "<EMAIL>",
        "phone_number": "1234567890",
        "role": "customer",
    }
    response = api_client.post(url, data, format="json")
    assert response.status_code == status.HTTP_201_CREATED
    assert response.json() == {"message": "User registered successfully"}


@pytest.mark.django_db
@patch("api.authentication.views.send_email")
def test_generate_otp(mock_send_email, api_client, non_staff_user):
    """
    Ensure OTP is sent successfully for an existing user.
    """
    mock_send_email.return_value = None  # Mock the email sending process
    url = reverse("token_generate_otp")
    response = api_client.post(url, {"email": non_staff_user.email}, format="json")
    assert response.status_code == status.HTTP_200_OK
    assert response.json() == {"message": "OTP sent successfully"}
    mock_send_email.assert_called_once()  # Ensure the email was attempted to be sent


@pytest.mark.django_db
def test_generate_otp_invalid_user(api_client):
    """
    Ensure OTP generation fails for a non-existent user.
    """
    url = reverse("token_generate_otp")
    data = {"email": "<EMAIL>"}
    response = api_client.post(url, data, format="json")
    assert response.status_code == status.HTTP_404_NOT_FOUND
    assert response.json() == {"error": "User not found"}


@pytest.mark.django_db
def test_verify_otp_and_get_token(api_client, non_staff_user):
    """
    Ensure OTP verification returns JWT tokens.
    """
    url = reverse("token_obtain_pair")

    # Generate OTP for the user
    otp = non_staff_user.generate_otp()

    data = {"email": "<EMAIL>", "otp": otp}
    response = api_client.post(url, data, format="json")
    assert response.status_code == status.HTTP_200_OK
    assert "refresh" in response.data
    assert "access" in response.data


@pytest.mark.django_db
def test_verify_invalid_otp(api_client, non_staff_user):
    """
    Ensure verification fails for an invalid OTP.
    """
    url = reverse("token_obtain_pair")
    data = {"email": "<EMAIL>", "otp": "123456"}  # Invalid OTP
    response = api_client.post(url, data, format="json")
    assert response.status_code == status.HTTP_400_BAD_REQUEST
    assert response.json() == {"error": "Invalid or expired OTP"}


@pytest.mark.django_db
def test_token_refresh(api_client, token_pair):
    """
    Ensure we can refresh the access token with a valid refresh token.
    """
    url = reverse("token_refresh")
    data = {"refresh": token_pair["refresh"]}
    response = api_client.post(url, data, format="json")
    assert response.status_code == status.HTTP_200_OK
    assert "access" in response.data


@pytest.mark.django_db
def test_token_refresh_invalid(api_client):
    """
    Ensure refreshing the token fails with an invalid refresh token.
    """
    url = reverse("token_refresh")
    data = {"refresh": "invalid_refresh_token"}
    response = api_client.post(url, data, format="json")
    assert response.status_code == status.HTTP_401_UNAUTHORIZED
    assert response.json()["detail"] == "Token is invalid or expired"


@pytest.mark.django_db
def test_token_verify(api_client, token_pair):
    """
    Ensure we can verify the access token with a valid token.
    """
    url = reverse("token_verify")
    data = {"token": token_pair["access"]}
    response = api_client.post(url, data, format="json")
    assert response.status_code == status.HTTP_200_OK


@pytest.mark.django_db
def test_token_verify_invalid(api_client):
    """
    Ensure verifying the token fails with an invalid token.
    """
    url = reverse("token_verify")
    data = {"token": "invalid_token"}
    response = api_client.post(url, data, format="json")
    assert response.status_code == status.HTTP_401_UNAUTHORIZED
    assert response.json()["detail"] == "Token is invalid or expired"


@pytest.mark.django_db
def test_user_profile(api_client, authenticated_user):
    """
    Ensure a logged-in user can retrieve their profile.
    """
    api_client.force_authenticate(user=authenticated_user)
    url = reverse("user_profile")
    response = api_client.get(url)
    assert response.status_code == status.HTTP_200_OK
    assert response.data["email"] == authenticated_user.email
    assert response.data["role"] == authenticated_user.role


@pytest.mark.django_db
def test_user_profile_unauthenticated(api_client):
    """
    Ensure unauthenticated users cannot access the profile endpoint.
    """
    url = reverse("user_profile")
    response = api_client.get(url)
    assert response.status_code == status.HTTP_401_UNAUTHORIZED


@pytest.mark.django_db
class TestThrottleApi:
    @override_settings(
        REST_FRAMEWORK={
            "DEFAULT_THROTTLE_CLASSES": [
                "rest_framework.throttling.AnonRateThrottle",
                "rest_framework.throttling.UserRateThrottle",
                "rest_framework.throttling.ScopedRateThrottle",
            ],
            "DEFAULT_THROTTLE_RATES": {
                "anon": "100/day",
                "user": "1000/day",
                "login": "10/minute",  # Set a lower threshold for testing
            },
        }
    )
    @pytest.mark.django_db
    def test_login_throttle(self, api_client, non_staff_user):
        """
        Ensure login is throttled after too many attempts.
        """
        token_url = reverse("token_obtain_pair")  # Adjust the URL name as per your setup
        invalid_data = {"email": "<EMAIL>", "password": "wrongpassword"}
        valid_data = {"email": "<EMAIL>", "password": "password"}

        with mock.patch(
            "rest_framework.throttling.ScopedRateThrottle.allow_request",
            side_effect=[True] * 9 + [False],
        ):
            # Attempt to authenticate with invalid credentials
            for i in range(9):  # Should allow exactly 5 attempts
                response = api_client.post(token_url, invalid_data, format="json")
                assert (
                    response.status_code == status.HTTP_401_UNAUTHORIZED
                ), f"Failed on attempt {i+1} with status {response.status_code}"

            # The 6th request should be throttled
            response = api_client.post(token_url, valid_data, format="json")
            assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR

            # Check if the response has the generic error message
            assert response.json() == {"detail": "Something went wrong"}


# @pytest.mark.django_db
# def test_login_throttle(api_client, token_url):
#     """
#     Ensure login is throttled after too many attempts.
#     """
#     invalid_data = {"email": "<EMAIL>", "password": "wrong"}
#     valid_data = {"email": "<EMAIL>", "password": "password"}

#     # Mock the throttling to simulate the request limit being hit
#     with mock.patch(
#         "rest_framework.throttling.ScopedRateThrottle.allow_request",
#         side_effect=[True] * 9 + [False],  # Allow first 9 requests, throttle on the 10th
#     ):
#         # Assuming the throttle limit is 10 requests/minute
#         for i in range(9):
#             response = api_client.post(token_url, invalid_data, format="json")
#             assert (
#                 response.status_code == status.HTTP_401_UNAUTHORIZED
#             ), f"Failed on attempt {i+1} with status {response.status_code}"

#         # The 10th request should be with valid credentials but still fail due to throttling
#         response = api_client.post(token_url, valid_data, format="json")
#         assert response.status_code == status.HTTP_500_INTERNAL_SERVER_ERROR

#         # Check if the response has the generic error message
#         assert response.json() == {"detail": ERROR_MSG}


import requests
import pytest

# URL configuration
BASE_URL = "http://localhost:8000"
TOKEN_URL = f"{BASE_URL}/auth/token/"
USER_INFO_URL = f"{BASE_URL}/auth/users/me/"


@pytest.fixture
def obtain_token(api_client, non_staff_user):
    """
    Obtain a JWT token using the token obtain endpoint.
    """
    # Step 1: Generate OTP
    otp_url = reverse("token_generate_otp")
    response = api_client.post(otp_url, {"email": non_staff_user.email}, format="json")
    assert response.status_code == status.HTTP_200_OK

    # Step 2: Verify OTP to get tokens
    token_url = reverse("token_obtain_pair")
    otp = non_staff_user.generate_otp()  # Generate the OTP
    response = api_client.post(
        token_url, {"email": non_staff_user.email, "otp": otp}, format="json"
    )
    assert response.status_code == status.HTTP_200_OK
    return response.json()["access"]


# def test_get_user_info(obtain_token):
#     """
#     Test obtaining user information using the JWT token.
#     """
#     # Obtain the token using the fixture
#     token = obtain_token

#     # Headers with the token
#     headers = {
#         "Authorization": f"Bearer {token}",
#         "Content-Type": "application/json",
#     }

#     # Make a GET request to the user info endpoint
#     response = requests.get(USER_INFO_URL, headers=headers)

#     # Print the response for debugging purposes
#     print(f"Status Code: {response.status_code}")
#     print(f"Response JSON: {response}")

#     # Ensure the request was successful
#     assert response.status_code == 200, f"Failed to get user info: {response}"
#     assert "email" in response.json(), "Email not found in response"
