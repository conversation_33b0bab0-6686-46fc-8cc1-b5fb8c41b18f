# StretchUp API

A Django REST API backend for the StretchUp application, providing services for appointment scheduling, user management, and business operations for massage and wellness services.

## Table of Contents

- [Prerequisites](#prerequisites)
- [Getting Started](#getting-started)
  - [Setup with Docker](#setup-with-docker)
  - [Manual Setup](#manual-setup)
- [Project Structure](#project-structure)
- [API Endpoints](#api-endpoints)
- [User Roles](#user-roles)
- [Database Management](#database-management)
- [Testing](#testing)
- [Deployment](#deployment)
- [Troubleshooting](#troubleshooting)

## Prerequisites

- [Docker](https://www.docker.com/get-started) and [Docker Compose](https://docs.docker.com/compose/install/) (recommended)
- Python 3.9+
- PostgreSQL 13+
- Git

## Getting Started

### Setup with Docker

1. **Clone the repository**

```bash
git clone <repository-url>
cd stretchup-api
```

2. **Configure environment variables**

Create a `.env` file in the project root with the following content:

```
DEBUG=True
CSRF_TRUSTED_ORIGINS=http://localhost,http://127.0.0.1
ALLOWED_HOSTS=localhost,127.0.0.1
CELERY_BROKER_URL=redis://redis:6379/0
CELERY_RESULT_BACKEND=redis://redis:6379/0
EMAIL_HOST=localhost
EMAIL_PORT=25
EMAIL_USE_TLS=False
EMAIL_USE_SSL=True
EMAIL_HOST_USER=
EMAIL_HOST_PASSWORD=
DEFAULT_FROM_EMAIL=
ENVIRONMENT=local
SECRET_KEY=django-insecure-development-key-for-testing
DB_NAME=postgres
DB_USER=postgres
DB_PASSWORD=password
DB_HOST=db
DB_PORT=5432
```

3. **Build and start the Docker containers**

```bash
docker-compose up -d
```

4. **Create initial users**

```bash
# Import users with roles from the contacts_cleaned.csv file
docker exec -it stretchup-api-web-1 python manage.py import_contacts_with_roles

# Set passwords for all users
docker exec -it stretchup-api-web-1 python manage.py set_user_passwords
```

5. **Access the application**

The API will be available at http://localhost:8000/

### Manual Setup

1. **Clone the repository**

```bash
git clone <repository-url>
cd stretchup-api
```

2. **Create and activate a virtual environment**

```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. **Install dependencies**

```bash
pip install -r requirements.txt
```

4. **Configure environment variables**

Create a `.env` file in the project root with the following content (adjust as needed):

```
DEBUG=True
CSRF_TRUSTED_ORIGINS=http://localhost,http://127.0.0.1
ALLOWED_HOSTS=localhost,127.0.0.1
ENVIRONMENT=local
SECRET_KEY=django-insecure-development-key-for-testing
DB_NAME=postgres
DB_USER=postgres
DB_PASSWORD=password
DB_HOST=localhost
DB_PORT=5432
```

5. **Set up the database**

```bash
# Create a PostgreSQL database
createdb stretchup

# Run migrations
python manage.py migrate
```

6. **Create initial users**

```bash
# Import users with roles from the contacts_cleaned.csv file
python manage.py import_contacts_with_roles

# Set passwords for all users
python manage.py set_user_passwords
```

7. **Run the development server**

```bash
python manage.py runserver
```

## Project Structure

The project follows a modular structure with separate Django apps for different functionalities:

```
stretchup-api/
├── api/                    # Main API package
│   ├── appointments/       # Appointment scheduling and management
│   ├── authentication/     # User authentication and management
│   ├── commissions/        # Commission calculation and management
│   ├── core/               # Core functionality and shared models
│   ├── retail/             # Retail product management
│   ├── services/           # Service offerings management
│   └── staff/              # Staff management
├── config/                 # Project configuration
│   ├── settings/           # Settings for different environments
│   │   ├── base.py         # Base settings
│   │   ├── local.py        # Local development settings
│   │   ├── production.py   # Production settings
│   │   └── stage.py        # Staging environment settings
│   ├── urls.py             # Main URL configuration
│   └── wsgi.py             # WSGI configuration
├── data/                   # Data files for imports
├── logs/                   # Log files
├── media/                  # User-uploaded files
├── static/                 # Static files
├── templates/              # HTML templates
├── utils/                  # Utility functions and classes
├── .env                    # Environment variables
├── docker-compose.yml      # Docker Compose configuration
├── Dockerfile              # Docker configuration
├── manage.py               # Django management script
└── requirements.txt        # Python dependencies
```

## API Endpoints

The API provides the following main endpoints:

- `/auth/` - Authentication endpoints
  - `/auth/token/` - Get JWT token
  - `/auth/token/refresh/` - Refresh JWT token
  - `/auth/register/` - Register a new user

- `/service/` - Service management
  - `/service/list/` - List all services
  - `/service/packages/` - List service packages

- `/staff/` - Staff management
  - `/staff/therapists/` - List therapists
  - `/staff/dashboard/` - Dashboard data

- `/appointment/` - Appointment management
  - `/appointment/create/` - Create a new appointment
  - `/appointment/list/` - List appointments

- `/retail/` - Retail product management
  - `/retail/products/` - List products
  - `/retail/sales/` - List sales

- `/commission/` - Commission management
  - `/commission/profiles/` - Commission profiles for therapists
  - `/commission/rules/` - Commission calculation rules
  - `/commission/manual/` - Manual commission entries
  - `/commission/earnings/` - Commission earnings records
  - `/commission/stats/` - Therapist statistics

## User Roles

The system supports the following user roles:

1. **Customer** - Regular users who can book appointments
2. **Therapist** - Staff members who provide services
3. **Receptionist** - Staff members who manage appointments and customers
4. **Owner** - Administrators with full access to the system

Default users are created with the following credentials:

| Email | Role | Password |
|-------|------|----------|
| <EMAIL> | Customer | password123 |
| <EMAIL> | Customer | password123 |
| <EMAIL> | Therapist | password123 |
| <EMAIL> | Receptionist | password123 |
| <EMAIL> | Owner | password123 |
| <EMAIL> | Owner | password123 |

## Database Management

### Migrations

Run migrations to update the database schema:

```bash
python manage.py migrate
```

### Data Import

Import contacts with roles:

```bash
python manage.py import_contacts_with_roles --csv=path/to/contacts.csv
```

Set user passwords:

```bash
python manage.py set_user_passwords --password=custom_password
```

Create commission profiles for therapists:

```bash
python manage.py shell -c "from api.commissions.models import CommissionProfile; from api.staff.models import TherapistProfile; for therapist in TherapistProfile.objects.all(): CommissionProfile.objects.create(therapist=therapist, name=f'Default Profile for {therapist.user.get_full_name()}', base_percentage=10.0)"
```

## Testing

Run tests using pytest:

```bash
pytest
```

## Deployment

For production deployment, update the `.env` file with production settings and use the production settings module:

```bash
DJANGO_SETTINGS_MODULE=config.settings.production
```

## Troubleshooting

### Common Issues

1. **Database connection errors**

   If you're using Docker and encounter database connection errors, ensure that:
   - The `DB_HOST` in `.env` is set to `db` (the service name in docker-compose.yml)
   - The PostgreSQL container is running (`docker ps`)
   - The database credentials are correct

2. **Permission issues with media files**

   Ensure that the media directory has the correct permissions:
   ```bash
   chmod -R 755 media/
   ```

3. **JWT token issues**

   If you encounter JWT token validation errors, check that:
   - The `SECRET_KEY` is properly set
   - The token is not expired
   - The token is being sent in the correct format (Bearer token)

### Getting Help

If you encounter any issues not covered in this documentation, please:
1. Check the logs in the `logs/` directory
2. Consult the Django documentation
3. Contact the project maintainers

---

## License

[Specify your license here]

## Contributors

[List of contributors]
