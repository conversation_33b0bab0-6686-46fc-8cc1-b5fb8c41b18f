from django.core.management.base import BaseCommand
from api.retail.models import Product
from decimal import Decimal


class Command(BaseCommand):
    help = "Creates initial product data for the retail store"

    def handle(self, *args, **options):
        # Define the base products info
        base_products = [
            # REVIVE products
            {
                "name": "REVIVE (VARIETY PACK) - 40 PIECES",
                "category": "REVIVE",
                "description": "REVIVE energy supplement variety pack with 40 pieces.",
                "price": Decimal("199.00"),
                "quantity_in_stock": 1,
            },
            {
                "name": "REVIVE (WATERMELON) - 30 PIECES",
                "category": "REVIVE",
                "description": "REVIVE energy supplement with watermelon flavor, 30 pieces pack.",
                "price": Decimal("170.00"),
                "quantity_in_stock": 1,
            },
            {
                "name": "REVIVE (APPLE) - 30 PIECES",
                "category": "REVIVE",
                "description": "REVIVE energy supplement with apple flavor, 30 pieces pack.",
                "price": Decimal("170.00"),
                "quantity_in_stock": 1,
            },
            {
                "name": "REVIVE (BLACKCURRANT) - 30 PIECES",
                "category": "REVIVE",
                "description": "REVIVE energy supplement with blackcurrant flavor, 30 pieces pack.",
                "price": Decimal("170.00"),
                "quantity_in_stock": 1,
            },
            {
                "name": "REVIVE (RASPBERRY) - 30 PIECES",
                "category": "REVIVE",
                "description": "REVIVE energy supplement with raspberry flavor, 30 pieces pack.",
                "price": Decimal("170.00"),
                "quantity_in_stock": 1,
            },
            {
                "name": "REVIVE (VARIETY PACK) - 20 PIECES",
                "category": "REVIVE",
                "description": "REVIVE energy supplement variety pack with 20 pieces.",
                "price": Decimal("130.00"),
                "quantity_in_stock": 1,
            },
            {
                "name": "REVIVE (VARIETY PACK) - 8 PIECES",
                "category": "REVIVE",
                "description": "REVIVE energy supplement variety pack with 8 pieces.",
                "price": Decimal("52.00"),
                "quantity_in_stock": 1,
            },
            {
                "name": "REVIVE - 1 PIECE",
                "category": "REVIVE",
                "description": "REVIVE energy supplement single piece.",
                "price": Decimal("8.00"),
                "quantity_in_stock": 1,
            },
            # HUMANTRA products
            {
                "name": "HUMANTRA - 20 PIECES",
                "category": "HUMANTRA",
                "description": "HUMANTRA supplement pack with 20 pieces.",
                "price": Decimal("85.00"),
                "quantity_in_stock": 1,
            },
            {
                "name": "HUMANTRA - 1 PIECE",
                "category": "HUMANTRA",
                "description": "HUMANTRA supplement single piece.",
                "price": Decimal("5.00"),
                "quantity_in_stock": 1,
            },
            # RUSH CLOTHING products
            {
                "name": "RUSH T-SHIRT",
                "category": "RUSH_CLOTHING",
                "description": "RUSH branded t-shirt for workout and casual wear.",
                "price": Decimal("105.00"),
                "quantity_in_stock": 1,
            },
            {
                "name": "RUSH GRAY SHORTS",
                "category": "RUSH_CLOTHING",
                "description": "RUSH branded gray shorts for workout and casual wear.",
                "price": Decimal("85.00"),
                "quantity_in_stock": 1,
            },
            {
                "name": "RUSH BLACK SHORTS",
                "category": "RUSH_CLOTHING",
                "description": "RUSH branded black shorts for workout and casual wear.",
                "price": Decimal("65.00"),
                "quantity_in_stock": 1,
            },
            {
                "name": "RUSH SOCKS",
                "category": "RUSH_CLOTHING",
                "description": "RUSH branded sports socks.",
                "price": Decimal("25.00"),
                "quantity_in_stock": 1,
            },
            # STRETCH UP products
            {
                "name": "STRETCH UP WATER BOTTLE",
                "category": "STRETCH_UP",
                "description": "STRETCH UP branded water bottle.",
                "price": Decimal("20.00"),
                "quantity_in_stock": 1,
            },
            {
                "name": "STRETCH UP ESPRESSO CUP",
                "category": "STRETCH_UP",
                "description": "STRETCH UP branded espresso cup.",
                "price": Decimal("65.00"),
                "quantity_in_stock": 1,
            },
            {
                "name": "STRETCH UP TAPE - LARGE",
                "category": "STRETCH_UP",
                "description": "STRETCH UP large muscle tape for recovery and support.",
                "price": Decimal("80.00"),
                "quantity_in_stock": 1,
            },
            {
                "name": "STRETCH UP TAPE - SMALL",
                "category": "STRETCH_UP",
                "description": "STRETCH UP small muscle tape for recovery and support.",
                "price": Decimal("50.00"),
                "quantity_in_stock": 1,
            },
        ]

        # Create counter for created and skipped products
        created_count = 0
        skipped_count = 0

        # Create each product for both locations
        # locations = ["A", "B"]
        locations = ["B"]

        for location in locations:
            location_display = (
                "Studio Al Warqa Mall" if location == "A" else "Studio Al Mizhar Branch"
            )
            self.stdout.write(
                self.style.SUCCESS(f"\nCreating products for {location_display}:")
            )

            for base_product in base_products:
                # Create a copy of the base product data for this location
                product_data = base_product.copy()

                # Add location suffix to the name to make it unique
                location_name = f"{product_data['name']} ({location_display})"

                # Check if product already exists
                if Product.objects.filter(name=location_name, location=location).exists():
                    self.stdout.write(
                        self.style.WARNING(
                            f'Product "{location_name}" already exists, skipping...'
                        )
                    )
                    skipped_count += 1
                    continue

                # Set location and update name
                product_data["location"] = location
                product_data["name"] = location_name

                # Set default fields
                product_data.setdefault("vat_percentage", Decimal("5.00"))
                product_data.setdefault("is_active", True)

                # Create the product
                product = Product.objects.create(**product_data)

                self.stdout.write(
                    self.style.SUCCESS(
                        f"Created product: {product.name} - {product.price} AED"
                    )
                )
                created_count += 1

        # Print summary
        self.stdout.write("")
        self.stdout.write(
            self.style.SUCCESS(f"Successfully created {created_count} products")
        )
        if skipped_count > 0:
            self.stdout.write(
                self.style.WARNING(
                    f"Skipped {skipped_count} products that already existed"
                )
            )
