# helpers/daily_insights.py
from decimal import Decimal
from django.db.models import Q, Sum, F, Count

from api.retail.models import (
    DailySalesReport,
    ProductSale,
)
from api.appointments.models import Sale, Appointment
from api.retail.serializers import DailySalesReportSerializer


def get_daily_insights(target_date, location):
    """Get detailed insights for a specific day's sales."""

    # Get the daily sales report if it exists
    try:
        report = DailySalesReport.objects.get(date=target_date, location=location)
        report_data = DailySalesReportSerializer(report).data
    except DailySalesReport.DoesNotExist:
        report_data = None

    # Get appointment IDs for service sales at this location
    appointment_ids = Appointment.objects.filter(
        date=target_date, location=location, status="check_in"
    ).values_list("id", flat=True)

    # Service sales from the Sale model
    service_sales = Sale.objects.filter(
        created_at__date=target_date,
        sale_type="service",
        appointment_id__in=appointment_ids,
    )

    # Package sales from the Sale model
    package_sales = Sale.objects.filter(
        created_at__date=target_date, sale_type="package", location=location
    )

    # Product sales
    product_sales = ProductSale.objects.filter(
        created_at__date=target_date, location=location, status="COMPLETED"
    )

    # Calculate hourly sales distribution
    hourly_sales = []
    for hour in range(9, 22):
        hour_start = f"{hour:02d}:00:00"
        hour_end = f"{hour+1:02d}:00:00"

        # Service sales in this hour
        hour_service_sales = service_sales.filter(
            created_at__time__range=(hour_start, hour_end)
        ).aggregate(total=Sum("total_price"))["total"] or Decimal("0")
        hour_service_sales = round(hour_service_sales, 5)

        # Package sales in this hour
        hour_package_sales = package_sales.filter(
            created_at__time__range=(hour_start, hour_end)
        ).aggregate(total=Sum("total_price"))["total"] or Decimal("0")
        hour_package_sales = round(hour_package_sales, 5)

        # Product sales in this hour
        hour_product_sales = product_sales.filter(
            created_at__time__range=(hour_start, hour_end)
        ).aggregate(total=Sum("total_amount"))["total"] or Decimal("0")
        hour_product_sales = round(hour_product_sales, 5)

        hourly_sales.append(
            {
                "hour": hour,
                "display": f"{hour:02d}:00 - {hour+1:02d}:00",
                "service_sales": hour_service_sales,
                "package_sales": hour_package_sales,
                "product_sales": hour_product_sales,
                "total": round(
                    hour_service_sales + hour_package_sales + hour_product_sales, 5
                ),
            }
        )
    # Get detailed payment method breakdown
    payment_breakdown = []
    for method, display in Appointment.PAYMENT_METHOD_CHOICES:
        amount = service_sales.filter(appointment__payment_method=method).aggregate(
            total=Sum("total_price")
        )["total"] or Decimal("0")
        amount = round(amount, 5)

        if amount > 0:
            payment_breakdown.append(
                {
                    "payment_method": method,
                    "payment_method_display": display,
                    "sale_type": "service",
                    "total": amount,
                    "count": service_sales.filter(
                        appointment__payment_method=method
                    ).count(),
                }
            )

    # Get package type breakdown
    package_breakdown = (
        package_sales.values("package_option__package__name", "package_option__time")
        .annotate(count=Count("id"), total=Sum("total_price"))
        .order_by("-count")
    )

    discounted_services = service_sales.filter(discount_percentage__gt=0).count()
    discounted_packages = package_sales.filter(discount_percentage__gt=0).count()

    # For counting
    discounted_services = service_sales.filter(discount_percentage__gt=0).count()
    discounted_packages = package_sales.filter(discount_percentage__gt=0).count()
    total_discounts = discounted_services + discounted_packages

    # For calculating
    discount_amounts = Decimal("0.00")
    if total_discounts > 0:
        service_discount = service_sales.filter(discount_percentage__gt=0).aggregate(
            total=Sum(F("total_price") * F("discount_percentage") / 100)
        )["total"] or Decimal("0")

        package_discount = package_sales.filter(discount_percentage__gt=0).aggregate(
            total=Sum(F("total_price") * F("discount_percentage") / 100)
        )["total"] or Decimal("0")

        discount_amounts = round(service_discount + package_discount, 5)

    # Get top products sold
    top_products = []
    if product_sales.exists():
        from api.retail.models import ProductSaleItem

        top_products = (
            ProductSaleItem.objects.filter(product_sale__in=product_sales)
            .values("product__name")
            .annotate(quantity=Sum("quantity"), revenue=Sum("total_price"))
            .order_by("-quantity")[:5]
        )

    return {
        "date": target_date.isoformat(),
        "location": location,
        "location_display": dict(DailySalesReport.LOCATION_CHOICES).get(
            location, location
        ),
        "report": report_data,
        "hourly_sales": hourly_sales,
        "payment_breakdown": payment_breakdown,
        "package_breakdown": package_breakdown,
        "discount_statistics": {
            "discounted_services": discounted_services,
            "discounted_packages": discounted_packages,
            "total_discounts": total_discounts,
            "discount_amounts": discount_amounts,
        },
        "top_products": top_products,
        "sales_counts": {
            "service_sales": service_sales.count(),
            "package_sales": package_sales.count(),
            "product_sales": product_sales.count(),
            "total_transactions": service_sales.count()
            + package_sales.count()
            + product_sales.count(),
        },
    }
