# helpers/daily_sales_simplified.py
from decimal import Decimal
from django.utils import timezone
import traceback
import logging

from api.retail.models import DailySalesReport, CashRegister
from utils.sales_calculations import (
    calculate_service_sales,
    calculate_package_sales,
    calculate_product_sales,
    calculate_gross_sales,
    calculate_payment_method_totals,
    calculate_deductions,
    calculate_net_sales,
    calculate_expenses_total,
    calculate_withdrawals_total,
    get_all_sales_items,
    get_all_product_sales,
    get_all_expenses,
    get_all_withdrawals,
    get_monthly_sales_data,
)

logger = logging.getLogger(__name__)


def generate_daily_sales_report(user, date, location):
    """
    Generate daily sales report data with monthly context.
    """
    try:
        # Get cash register for this date and location
        try:
            cash_register = CashRegister.objects.get(date=date, location=location)
        except CashRegister.DoesNotExist:
            raise Exception(f"No cash register found for {location} on {date}")

        # Daily calculations
        service_sales = calculate_service_sales(date=date, location=location)
        package_sales = calculate_package_sales(date=date, location=location)
        product_sales = calculate_product_sales(date=date, location=location)
        gross_sales = service_sales + package_sales + product_sales

        # Payment methods
        payment_totals = calculate_payment_method_totals(date=date, location=location)

        # Deductions
        deductions = calculate_deductions(gross_sales, payment_totals)
        net_sales = calculate_net_sales(gross_sales, deductions)

        # Expenses and withdrawals - filter by location, not cash_register
        expenses_total = calculate_expenses_total(date=date, location=location)
        withdrawals_total = calculate_withdrawals_total(date=date, location=location)

        # Get all daily items
        daily_sales_items = get_all_sales_items(date=date, location=location)
        daily_product_sales = get_all_product_sales(date=date, location=location)
        daily_expenses = get_all_expenses(date=date, location=location)
        daily_withdrawals = get_all_withdrawals(date=date, location=location)

        # Serialize items for JSON response
        serialized_sales = []
        for sale in daily_sales_items:
            try:
                services_data = []
                if sale.appointment and sale.sale_type == "service":
                    for app_service in sale.appointment.appointment_services.all():
                        services_data.append(
                            {
                                "name": app_service.service.name,
                                "duration": app_service.duration,
                            }
                        )

                # Get customer name properly
                customer_name = "Guest"
                if sale.appointment and sale.appointment.customer:
                    customer = sale.appointment.customer
                    customer_name = (
                        f"{customer.first_name} {customer.last_name}".strip()
                        or customer.email
                    )
                elif sale.user:
                    customer_name = (
                        f"{sale.user.first_name} {sale.user.last_name}".strip()
                        or sale.user.email
                    )

                # Get therapist name properly
                therapist_name = None
                therapist_email = None
                if sale.appointment and sale.appointment.therapist:
                    therapist_user = sale.appointment.therapist.user
                    therapist_name = (
                        f"{therapist_user.first_name} {therapist_user.last_name}".strip()
                    )
                    therapist_email = therapist_user.email

                serialized_sales.append(
                    {
                        "id": sale.id,
                        "created_at": sale.created_at.isoformat(),
                        "created_at_formatted": sale.created_at.strftime(
                            "%Y-%m-%d %H:%M"
                        ),
                        "sale_type": sale.sale_type,
                        "customer_name": customer_name,
                        "services": services_data,
                        "package_name": (
                            sale.package_option.package.name
                            if sale.package_option and sale.package_option.package
                            else None
                        ),
                        "package_time": (
                            sale.package_option.time if sale.package_option else None
                        ),
                        "therapist_name": therapist_name,
                        "therapist_email": therapist_email,
                        "payment_method": sale.payment_method,
                        "total_price": str(sale.total_price),
                    }
                )
            except Exception as e:
                logger.error(f"Error serializing sale {sale.id}: {str(e)}")
                logger.error(traceback.format_exc())

        serialized_product_sales = []
        for product_sale in daily_product_sales:
            try:
                items_data = []
                for (
                    item
                ) in product_sale.sale_items.all():  # Changed from items to sale_items
                    items_data.append(
                        {
                            "product_name": item.product.name,
                            "quantity": item.quantity,
                            "price": str(item.unit_price),
                            "total_price": str(item.total_price),
                        }
                    )

                serialized_product_sales.append(
                    {
                        "id": product_sale.id,
                        "created_at": product_sale.created_at.isoformat(),
                        "created_at_formatted": product_sale.created_at.strftime(
                            "%Y-%m-%d %H:%M"
                        ),
                        "invoice_number": product_sale.invoice_number,
                        "items": items_data,
                        "payment_method": product_sale.payment_method,
                        "total_amount": str(product_sale.total_amount),
                    }
                )
            except Exception as e:
                logger.error(
                    f"Error serializing product sale {product_sale.id}: {str(e)}"
                )
                logger.error(traceback.format_exc())

        serialized_expenses = []
        for expense in daily_expenses:
            try:
                # Get category display name
                category_name = expense.get_category_display()

                # Get staff name properly
                staff_name = "Unknown"
                if expense.staff:
                    staff_name = (
                        f"{expense.staff.first_name} {expense.staff.last_name}".strip()
                        or expense.staff.email
                    )

                serialized_expenses.append(
                    {
                        "id": expense.id,
                        "created_at": expense.created_at.isoformat(),
                        "created_at_formatted": expense.created_at.strftime(
                            "%Y-%m-%d %H:%M"
                        ),
                        "category_name": category_name,
                        "description": expense.description,
                        "created_by_name": staff_name,
                        "amount": str(expense.amount),
                    }
                )
            except Exception as e:
                logger.error(f"Error serializing expense {expense.id}: {str(e)}")
                logger.error(traceback.format_exc())

        serialized_withdrawals = []
        for withdrawal in daily_withdrawals:
            try:
                # Get staff name properly (the field is 'staff' not 'withdrawn_by')
                staff_name = "Unknown"
                if withdrawal.staff:
                    staff_name = (
                        f"{withdrawal.staff.first_name} {withdrawal.staff.last_name}".strip()
                        or withdrawal.staff.email
                    )

                serialized_withdrawals.append(
                    {
                        "id": withdrawal.id,
                        "created_at": withdrawal.created_at.isoformat(),
                        "created_at_formatted": withdrawal.created_at.strftime(
                            "%Y-%m-%d %H:%M"
                        ),
                        "description": withdrawal.reason,  # Changed from description to reason
                        "withdrawn_by_name": staff_name,
                        "amount": str(withdrawal.amount),
                    }
                )
            except Exception as e:
                logger.error(f"Error serializing withdrawal {withdrawal.id}: {str(e)}")
                logger.error(traceback.format_exc())

        # Get monthly context
        monthly_data = get_monthly_sales_data(date.month, date.year, location)

        # Serialize monthly items
        monthly_serialized_sales = []
        for sale in monthly_data["items"]["sales"]:
            try:
                services_data = []
                if sale.appointment and sale.sale_type == "service":
                    for app_service in sale.appointment.appointment_services.all():
                        services_data.append(
                            {
                                "name": app_service.service.name,
                                "duration": app_service.duration,
                            }
                        )

                # Get customer name properly
                customer_name = "Guest"
                if sale.appointment and sale.appointment.customer:
                    customer = sale.appointment.customer
                    customer_name = (
                        f"{customer.first_name} {customer.last_name}".strip()
                        or customer.email
                    )
                elif sale.user:
                    customer_name = (
                        f"{sale.user.first_name} {sale.user.last_name}".strip()
                        or sale.user.email
                    )

                # Get therapist name properly
                therapist_name = None
                therapist_email = None
                if sale.appointment and sale.appointment.therapist:
                    therapist_user = sale.appointment.therapist.user
                    therapist_name = (
                        f"{therapist_user.first_name} {therapist_user.last_name}".strip()
                    )
                    therapist_email = therapist_user.email

                monthly_serialized_sales.append(
                    {
                        "id": sale.id,
                        "created_at": sale.created_at.isoformat(),
                        "created_at_formatted": sale.created_at.strftime(
                            "%Y-%m-%d %H:%M"
                        ),
                        "sale_type": sale.sale_type,
                        "customer_name": customer_name,
                        "services": services_data,
                        "package_name": (
                            sale.package_option.package.name
                            if sale.package_option and sale.package_option.package
                            else None
                        ),
                        "package_time": (
                            sale.package_option.time if sale.package_option else None
                        ),
                        "therapist_name": therapist_name,
                        "therapist_email": therapist_email,
                        "payment_method": sale.payment_method,
                        "total_price": str(sale.total_price),
                    }
                )
            except Exception as e:
                logger.error(f"Error serializing monthly sale {sale.id}: {str(e)}")
                logger.error(traceback.format_exc())

        monthly_serialized_product_sales = []
        for product_sale in monthly_data["items"]["product_sales"]:
            try:
                items_data = []
                for (
                    item
                ) in product_sale.sale_items.all():  # Changed from items to sale_items
                    items_data.append(
                        {
                            "product_name": item.product.name,
                            "quantity": item.quantity,
                            "unit_price": str(item.unit_price),
                            "total_price": str(item.total_price),
                        }
                    )

                monthly_serialized_product_sales.append(
                    {
                        "id": product_sale.id,
                        "created_at": product_sale.created_at.isoformat(),
                        "created_at_formatted": product_sale.created_at.strftime(
                            "%Y-%m-%d %H:%M"
                        ),
                        "invoice_number": product_sale.invoice_number,
                        "items": items_data,
                        "payment_method": product_sale.payment_method,
                        "total_amount": str(product_sale.total_amount),
                    }
                )
            except Exception as e:
                logger.error(
                    f"Error serializing monthly product sale {product_sale.id}: {str(e)}"
                )
                logger.error(traceback.format_exc())

        monthly_serialized_expenses = []
        for expense in monthly_data["items"]["expenses"]:
            try:
                # Get category display name
                category_name = expense.get_category_display()

                # Get staff name properly
                staff_name = "Unknown"
                if expense.staff:
                    staff_name = (
                        f"{expense.staff.first_name} {expense.staff.last_name}".strip()
                        or expense.staff.email
                    )

                monthly_serialized_expenses.append(
                    {
                        "id": expense.id,
                        "created_at": expense.created_at.isoformat(),
                        "created_at_formatted": expense.created_at.strftime(
                            "%Y-%m-%d %H:%M"
                        ),
                        "category_name": category_name,
                        "description": expense.description,
                        "created_by_name": staff_name,
                        "amount": str(expense.amount),
                    }
                )
            except Exception as e:
                logger.error(f"Error serializing monthly expense {expense.id}: {str(e)}")
                logger.error(traceback.format_exc())

        monthly_serialized_withdrawals = []
        for withdrawal in monthly_data["items"]["withdrawals"]:
            try:
                # Get staff name properly (the field is 'staff' not 'withdrawn_by')
                staff_name = "Unknown"
                if withdrawal.staff:
                    staff_name = (
                        f"{withdrawal.staff.first_name} {withdrawal.staff.last_name}".strip()
                        or withdrawal.staff.email
                    )

                monthly_serialized_withdrawals.append(
                    {
                        "id": withdrawal.id,
                        "created_at": withdrawal.created_at.isoformat(),
                        "created_at_formatted": withdrawal.created_at.strftime(
                            "%Y-%m-%d %H:%M"
                        ),
                        "description": withdrawal.reason,  # Changed from description to reason
                        "withdrawn_by_name": staff_name,
                        "amount": str(withdrawal.amount),
                    }
                )
            except Exception as e:
                logger.error(
                    f"Error serializing monthly withdrawal {withdrawal.id}: {str(e)}"
                )
                logger.error(traceback.format_exc())

        # Create or update the DailySalesReport record
        report, created = DailySalesReport.objects.update_or_create(
            date=date,
            location=location,
            defaults={
                "gross_sales_amount": gross_sales,
                "service_sales_amount": service_sales,
                "product_sales_amount": product_sales,
                "package_sales_amount": package_sales,
                "cash_sales_amount": payment_totals["cash"],
                "card_sales_amount": payment_totals["card"],
                "online_link_amount": payment_totals["link"],
                "vat_amount": deductions["vat"],
                "card_charges_amount": deductions["card_charges"],
                "link_charges_amount": deductions["link_charges"],
                "net_sales_amount": net_sales,
                "expenses_total": expenses_total,
                "cash_withdrawals_total": withdrawals_total,
                "starting_cash_balance": Decimal("0.00"),  # Removed as requested
                "ending_cash_balance": Decimal("0.00"),  # Removed as requested
                "created_by": user,
                "notes": f"Generated on {timezone.now().strftime('%Y-%m-%d %H:%M:%S')}",
            },
        )

        return {
            "report": report,
            "created": created,
            "daily": {
                "summary": {
                    "service_sales": str(service_sales),
                    "package_sales": str(package_sales),
                    "product_sales": str(product_sales),
                    "gross_sales": str(gross_sales),
                    "payment_totals": {
                        "cash": str(payment_totals["cash"]),
                        "card": str(payment_totals["card"]),
                        "link": str(payment_totals["link"]),
                    },
                    "deductions": {
                        "vat": str(deductions["vat"]),
                        "card_charges": str(deductions["card_charges"]),
                        "link_charges": str(deductions["link_charges"]),
                        "total": str(deductions["total"]),
                    },
                    "net_sales": str(net_sales),
                    "expenses_total": str(expenses_total),
                    "withdrawals_total": str(withdrawals_total),
                },
                "items": {
                    "sales": serialized_sales,
                    "product_sales": serialized_product_sales,
                    "expenses": serialized_expenses,
                    "withdrawals": serialized_withdrawals,
                },
            },
            "monthly": {
                "summary": {
                    "service_sales": str(monthly_data["summary"]["service_sales"]),
                    "package_sales": str(monthly_data["summary"]["package_sales"]),
                    "product_sales": str(monthly_data["summary"]["product_sales"]),
                    "gross_sales": str(monthly_data["summary"]["gross_sales"]),
                    "payment_totals": {
                        "cash": str(monthly_data["summary"]["payment_totals"]["cash"]),
                        "card": str(monthly_data["summary"]["payment_totals"]["card"]),
                        "link": str(monthly_data["summary"]["payment_totals"]["link"]),
                    },
                    "deductions": {
                        "vat": str(monthly_data["summary"]["deductions"]["vat"]),
                        "card_charges": str(
                            monthly_data["summary"]["deductions"]["card_charges"]
                        ),
                        "link_charges": str(
                            monthly_data["summary"]["deductions"]["link_charges"]
                        ),
                        "total": str(monthly_data["summary"]["deductions"]["total"]),
                    },
                    "net_sales": str(monthly_data["summary"]["net_sales"]),
                    "expenses_total": str(monthly_data["summary"]["expenses_total"]),
                    "withdrawals_total": str(
                        monthly_data["summary"]["withdrawals_total"]
                    ),
                },
                "items": {
                    "sales": monthly_serialized_sales,
                    "product_sales": monthly_serialized_product_sales,
                    "expenses": monthly_serialized_expenses,
                    "withdrawals": monthly_serialized_withdrawals,
                },
                "meta": {
                    "month": monthly_data["meta"]["month"],
                    "year": monthly_data["meta"]["year"],
                    "month_name": monthly_data["meta"]["month_name"],
                    "start_date": monthly_data["meta"]["start_date"].isoformat(),
                    "end_date": monthly_data["meta"]["end_date"].isoformat(),
                    "days_in_period": monthly_data["meta"]["days_in_period"],
                },
            },
            "location": location,
            "date": date,
        }
    except Exception as e:
        logger.error(f"Error generating daily sales report: {str(e)}")
        logger.error(traceback.format_exc())
        raise
