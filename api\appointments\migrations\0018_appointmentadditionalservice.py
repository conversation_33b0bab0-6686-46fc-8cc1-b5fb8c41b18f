# Generated by Django 4.2.19 on 2025-03-05 05:54

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('appointments', '0017_unlimitedpackage_appointment_unlimited_package'),
    ]

    operations = [
        migrations.CreateModel(
            name='AppointmentAdditionalService',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('service_type', models.CharField(choices=[('Massage', 'Massage'), ('Stretching', 'Stretching'), ('Compression', 'Compression'), ('Red-Light Therapy', 'Red-Light Therapy'), ('Physio', 'Physio')], max_length=50)),
                ('duration', models.PositiveIntegerField(help_text='Duration of the additional service in minutes')),
                ('price_per_minute', models.DecimalField(decimal_places=2, help_text='Rate charged per minute for this service', max_digits=10)),
                ('total_price', models.DecimalField(decimal_places=2, help_text='Total price for this additional service', max_digits=10)),
                ('added_at', models.DateTimeField(auto_now_add=True)),
                ('added_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='added_services', to=settings.AUTH_USER_MODEL)),
                ('appointment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='additional_services', to='appointments.appointment')),
            ],
        ),
    ]
